import { NextRequest, NextResponse } from 'next/server'
import { SimpleAuthService } from '@/lib/auth-simple'

export async function GET(request: NextRequest) {
  try {
    // Get authenticated user
    const authResult = await SimpleAuthService.authenticateRequest(request)

    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const user = authResult.user

    // Generate mock dashboard statistics based on user data
    const userStats = {
      level: user.level,
      score: user.score,
      streak: user.streak_days,
      rank: user.level > 50 ? Math.floor(Math.random() * 100) + 1 : Math.floor(Math.random() * 1000) + 100,
      nextLevelProgress: Math.floor((user.score % 1000) / 10),
      totalScans: Math.floor(user.score / 10) + Math.floor(Math.random() * 50),
      vulnerabilitiesFound: Math.floor(user.score / 20) + Math.floor(Math.random() * 25),
      osintQueries: Math.floor(user.score / 15) + Math.floor(Math.random() * 30),
      fileAnalyses: Math.floor(user.score / 25) + Math.floor(Math.random() * 20),
      cveSearches: Math.floor(user.score / 30) + Math.floor(Math.random() * 15),
      dorkingQueries: Math.floor(user.score / 35) + Math.floor(Math.random() * 10)
    }

    // Generate mock recent activities
    const recentActivities = [
      {
        id: 1,
        type: 'vulnerability_scan',
        description: 'Completed vulnerability scan on target.example.com',
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        severity: 'high',
        result: 'Found 3 critical vulnerabilities'
      },
      {
        id: 2,
        type: 'osint_query',
        description: 'OSINT investigation on domain example.com',
        timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
        severity: 'medium',
        result: 'Collected 15 data points'
      },
      {
        id: 3,
        type: 'file_analysis',
        description: 'Analyzed suspicious file: malware.exe',
        timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
        severity: 'critical',
        result: 'Detected Trojan.Win32.Agent'
      },
      {
        id: 4,
        type: 'cve_search',
        description: 'CVE research for Apache HTTP Server',
        timestamp: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString(),
        severity: 'low',
        result: 'Found 2 relevant CVEs'
      },
      {
        id: 5,
        type: 'dorking',
        description: 'Google dorking for exposed databases',
        timestamp: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString(),
        severity: 'medium',
        result: 'Discovered 5 potential targets'
      }
    ]

    // Generate mock plan usage based on user plan
    const planLimits = {
      'Free': { scans: 10, osint: 20, files: 5, cve: 50, dorking: 10 },
      'Student': { scans: 50, osint: 100, files: 25, cve: 200, dorking: 50 },
      'Hobby': { scans: 100, osint: 200, files: 50, cve: 500, dorking: 100 },
      'Bughunter': { scans: 500, osint: 1000, files: 200, cve: 2000, dorking: 500 },
      'Cybersecurity': { scans: 1000, osint: 2000, files: 500, cve: 5000, dorking: 1000 },
      'Pro': { scans: 2000, osint: 5000, files: 1000, cve: 10000, dorking: 2000 },
      'Expert': { scans: 5000, osint: 10000, files: 2000, cve: 20000, dorking: 5000 },
      'Elite': { scans: -1, osint: -1, files: -1, cve: -1, dorking: -1 }
    }

    const limits = planLimits[user.plan as keyof typeof planLimits] || planLimits.Free

    const planUsage = {
      plan: user.plan,
      limits,
      usage: {
        vulnerabilityScans: userStats.totalScans,
        osintQueries: userStats.osintQueries,
        fileAnalyses: userStats.fileAnalyses,
        dorkingQueries: userStats.dorkingQueries,
        cveSearches: userStats.cveSearches
      },
      percentage: {
        vulnerabilityScans: limits.scans === -1 ? 0 : Math.min((userStats.totalScans / limits.scans) * 100, 100),
        osintQueries: limits.osint === -1 ? 0 : Math.min((userStats.osintQueries / limits.osint) * 100, 100),
        fileAnalyses: limits.files === -1 ? 0 : Math.min((userStats.fileAnalyses / limits.files) * 100, 100),
        dorkingQueries: limits.dorking === -1 ? 0 : Math.min((userStats.dorkingQueries / limits.dorking) * 100, 100),
        cveSearches: limits.cve === -1 ? 0 : Math.min((userStats.cveSearches / limits.cve) * 100, 100)
      }
    }

    // Generate mock achievements
    const achievements = [
      {
        id: 1,
        title: 'First Scan',
        description: 'Complete your first vulnerability scan',
        icon: 'shield',
        unlocked: userStats.totalScans > 0,
        unlockedAt: userStats.totalScans > 0 ? new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString() : null,
        rarity: 'common'
      },
      {
        id: 2,
        title: 'OSINT Expert',
        description: 'Perform 100 OSINT queries',
        icon: 'eye',
        unlocked: userStats.osintQueries >= 100,
        unlockedAt: userStats.osintQueries >= 100 ? new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString() : null,
        rarity: 'rare'
      },
      {
        id: 3,
        title: 'Vulnerability Hunter',
        description: 'Find 50 vulnerabilities',
        icon: 'bug',
        unlocked: userStats.vulnerabilitiesFound >= 50,
        unlockedAt: userStats.vulnerabilitiesFound >= 50 ? new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString() : null,
        rarity: 'epic'
      },
      {
        id: 4,
        title: 'Streak Master',
        description: 'Maintain a 30-day streak',
        icon: 'flame',
        unlocked: user.streak_days >= 30,
        unlockedAt: user.streak_days >= 30 ? new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString() : null,
        rarity: 'legendary'
      }
    ]

    // Generate mock activity chart data (last 30 days)
    const activityChart = Array.from({ length: 30 }, (_, i) => {
      const date = new Date()
      date.setDate(date.getDate() - (29 - i))
      return {
        date: date.toISOString().split('T')[0],
        scans: Math.floor(Math.random() * 10),
        osint: Math.floor(Math.random() * 15),
        files: Math.floor(Math.random() * 5),
        cve: Math.floor(Math.random() * 20),
        dorking: Math.floor(Math.random() * 8)
      }
    })

    // Generate mock trending vulnerabilities
    const trendingVulnerabilities = [
      {
        id: 'CVE-2024-0001',
        title: 'Critical RCE in Apache HTTP Server',
        severity: 'critical',
        score: 9.8,
        description: 'Remote code execution vulnerability in Apache HTTP Server 2.4.x',
        affectedSystems: 15420,
        discoveredDate: '2024-01-15',
        trending: true
      },
      {
        id: 'CVE-2024-0002',
        title: 'SQL Injection in WordPress Plugin',
        severity: 'high',
        score: 8.5,
        description: 'SQL injection vulnerability in popular WordPress plugin',
        affectedSystems: 8930,
        discoveredDate: '2024-01-12',
        trending: true
      },
      {
        id: 'CVE-2024-0003',
        title: 'XSS in React Component Library',
        severity: 'medium',
        score: 6.2,
        description: 'Cross-site scripting vulnerability in React component library',
        affectedSystems: 5670,
        discoveredDate: '2024-01-10',
        trending: false
      }
    ]

    return NextResponse.json({
      success: true,
      data: {
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          fullName: user.full_name,
          role: user.role,
          plan: user.plan,
          level: user.level,
          score: user.score,
          streak: user.streak_days,
          rank: userStats.rank,
          nextLevelProgress: userStats.nextLevelProgress,
          emailVerified: user.email_verified,
          lastActive: user.last_active,
          createdAt: user.created_at
        },
        stats: {
          totalScans: userStats.totalScans,
          vulnerabilitiesFound: userStats.vulnerabilitiesFound,
          osintQueries: userStats.osintQueries,
          fileAnalyses: userStats.fileAnalyses,
          cveSearches: userStats.cveSearches,
          dorkingQueries: userStats.dorkingQueries
        },
        usage: planUsage,
        recentActivities,
        achievements,
        activityChart,
        trendingVulnerabilities
      }
    })

  } catch (error) {
    console.error('Dashboard stats error:', error)

    return NextResponse.json(
      {
        success: false,
        message: 'Internal server error',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
