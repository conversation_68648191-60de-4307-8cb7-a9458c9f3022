import { NextRequest, NextResponse } from 'next/server'
import { SimpleAuthService } from '@/lib/auth-simple'
import { z } from 'zod'

const registerSchema = z.object({
  username: z.string().min(3, 'Username must be at least 3 characters'),
  email: z.string().email('Invalid email address'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
  fullName: z.string().min(1, 'Full name is required'),
  confirmPassword: z.string().optional()
})

export async function POST(request: NextRequest) {
  const startTime = Date.now()

  try {
    console.log('🔐 Register attempt started')

    const body = await request.json()
    console.log('Register attempt for email:', body.email)

    const validation = registerSchema.safeParse(body)
    if (!validation.success) {
      console.log('❌ Validation failed:', validation.error.errors)
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid input data',
          details: validation.error.errors
        },
        { status: 400 }
      )
    }

    const { username, email, password, fullName, confirmPassword } = validation.data

    // Check password confirmation if provided
    if (confirmPassword && password !== confirmPassword) {
      return NextResponse.json({
        success: false,
        error: 'Passwords do not match'
      }, { status: 400 })
    }

    console.log('🔍 Attempting registration for:', email)

    // Attempt registration
    const result = await SimpleAuthService.register({
      username: username.trim(),
      email: email.trim().toLowerCase(),
      password,
      fullName: fullName.trim()
    })

    if (!result.success) {
      console.log('❌ Registration failed:', result.message)
      return NextResponse.json(
        {
          success: false,
          error: result.message || 'Registration failed'
        },
        { status: 400 }
      )
    }

    console.log('✅ Registration successful for user:', result.user?.id)

    const response = NextResponse.json({
      success: true,
      message: 'Registration successful! Welcome to KodeXGuard!',
      data: {
        user: {
          id: result.user!.id,
          username: result.user!.username,
          email: result.user!.email,
          fullName: result.user!.full_name,
          role: result.user!.role,
          plan: result.user!.plan,
          level: result.user!.level,
          score: result.user!.score,
          streak: result.user!.streak_days,
          emailVerified: result.user!.email_verified,
          lastActive: result.user!.last_active,
          createdAt: result.user!.created_at
        },
        tokens: {
          accessToken: result.tokens!.accessToken,
          refreshToken: result.tokens!.refreshToken,
          expiresIn: result.tokens!.expiresIn
        }
      }
    })

    // Set cookies for automatic login
    const cookieOptions = {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict' as const,
      path: '/'
    }

    response.cookies.set('accessToken', result.tokens!.accessToken, {
      ...cookieOptions,
      maxAge: 24 * 60 * 60 // 24 hours
    })

    response.cookies.set('refreshToken', result.tokens!.refreshToken, {
      ...cookieOptions,
      maxAge: 7 * 24 * 60 * 60 // 7 days
    })

    response.cookies.set('user', JSON.stringify({
      id: result.user!.id,
      username: result.user!.username,
      email: result.user!.email,
      role: result.user!.role,
      plan: result.user!.plan
    }), {
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict' as const,
      path: '/',
      maxAge: 24 * 60 * 60 // 24 hours
    })

    console.log(`✅ Registration completed in ${Date.now() - startTime}ms`)
    return response

  } catch (error) {
    console.error('❌ Registration error:', error)

    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: 'An unexpected error occurred during registration'
      },
      { status: 500 }
    )
  }
}

export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}
