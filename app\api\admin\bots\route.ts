import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/database'
import { WhatsAppBotService } from '@/lib/services/whatsapp-bot'
import { TelegramBotService } from '@/lib/services/telegram-bot'
import { verifyToken } from '@/lib/auth'

// Global bot service instances
const whatsappBot = new WhatsAppBotService()
const telegramBot = new TelegramBotService()

export async function GET(request: NextRequest) {
  try {
    const token = request.headers.get('authorization')?.replace('Bearer ', '')
    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const user = await verifyToken(token)
    if (!user || (user.role !== 'admin' && user.role !== 'super_admin')) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // Get all bot instances from database
    const [botRows] = await db.query(`
      SELECT 
        id,
        type,
        name,
        status,
        config,
        created_at,
        updated_at,
        last_activity
      FROM bot_instances 
      ORDER BY created_at DESC
    `)

    // Get active sessions from services
    const whatsappSessions = await whatsappBot.getAllSessions()
    const telegramSession = telegramBot.getSession()

    const activeSessions = [
      ...whatsappSessions.map(session => ({
        ...session,
        type: 'whatsapp'
      })),
      ...(telegramSession ? [{ ...telegramSession, type: 'telegram' }] : [])
    ]

    return NextResponse.json({
      success: true,
      data: {
        instances: botRows,
        activeSessions,
        statistics: {
          total: (botRows as any[]).length,
          active: activeSessions.filter(s => s.status === 'connected').length,
          whatsapp: whatsappSessions.length,
          telegram: telegramSession ? 1 : 0
        }
      }
    })

  } catch (error) {
    console.error('Error fetching bot instances:', error)
    return NextResponse.json(
      { error: 'Failed to fetch bot instances' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const token = request.headers.get('authorization')?.replace('Bearer ', '')
    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const user = await verifyToken(token)
    if (!user || user.role !== 'super_admin') {
      return NextResponse.json({ error: 'Forbidden - Super Admin only' }, { status: 403 })
    }

    const body = await request.json()
    const { type, config } = body

    if (!type || !config) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 })
    }

    let result

    if (type === 'whatsapp') {
      // Create WhatsApp bot session
      const sessionName = config.sessionName || 'kodexguard-wa'
      result = await whatsappBot.createSession(sessionName)
      
      return NextResponse.json({
        success: true,
        data: {
          sessionId: result,
          type: 'whatsapp',
          message: 'WhatsApp bot session created. Please scan QR code to connect.'
        }
      })

    } else if (type === 'telegram') {
      // Create Telegram bot session
      if (!config.token) {
        return NextResponse.json({ error: 'Telegram bot token is required' }, { status: 400 })
      }

      result = await telegramBot.connect({
        token: config.token,
        webhookUrl: config.webhookUrl,
        webhookSecret: config.webhookSecret
      })

      return NextResponse.json({
        success: true,
        data: {
          sessionId: result.id,
          botUsername: result.botUsername,
          type: 'telegram',
          message: 'Telegram bot connected successfully'
        }
      })

    } else {
      return NextResponse.json({ error: 'Invalid bot type' }, { status: 400 })
    }

  } catch (error) {
    console.error('Error creating bot instance:', error)
    return NextResponse.json(
      { error: 'Failed to create bot instance' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const token = request.headers.get('authorization')?.replace('Bearer ', '')
    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const user = await verifyToken(token)
    if (!user || user.role !== 'super_admin') {
      return NextResponse.json({ error: 'Forbidden - Super Admin only' }, { status: 403 })
    }

    const { searchParams } = new URL(request.url)
    const sessionId = searchParams.get('sessionId')
    const type = searchParams.get('type')

    if (!sessionId || !type) {
      return NextResponse.json({ error: 'Missing sessionId or type' }, { status: 400 })
    }

    let success = false

    if (type === 'whatsapp') {
      success = await whatsappBot.disconnectSession(sessionId)
    } else if (type === 'telegram') {
      success = await telegramBot.disconnect()
    }

    if (success) {
      // Remove from database
      await db.query('DELETE FROM bot_instances WHERE id = ?', [sessionId])
      
      return NextResponse.json({
        success: true,
        message: `${type} bot disconnected successfully`
      })
    } else {
      return NextResponse.json({ error: 'Failed to disconnect bot' }, { status: 500 })
    }

  } catch (error) {
    console.error('Error disconnecting bot:', error)
    return NextResponse.json(
      { error: 'Failed to disconnect bot' },
      { status: 500 }
    )
  }
}
