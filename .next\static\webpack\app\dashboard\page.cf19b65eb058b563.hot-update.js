"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./components/dashboard/UserDashboard.tsx":
/*!************************************************!*\
  !*** ./components/dashboard/UserDashboard.tsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ UserDashboard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Crown_Database_FileText_Flame_Globe_Rocket_Search_Shield_Star_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Crown,Database,FileText,Flame,Globe,Rocket,Search,Shield,Star,Target,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Crown_Database_FileText_Flame_Globe_Rocket_Search_Shield_Star_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Crown,Database,FileText,Flame,Globe,Rocket,Search,Shield,Star,Target,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Crown_Database_FileText_Flame_Globe_Rocket_Search_Shield_Star_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Crown,Database,FileText,Flame,Globe,Rocket,Search,Shield,Star,Target,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Crown_Database_FileText_Flame_Globe_Rocket_Search_Shield_Star_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Crown,Database,FileText,Flame,Globe,Rocket,Search,Shield,Star,Target,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Crown_Database_FileText_Flame_Globe_Rocket_Search_Shield_Star_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Crown,Database,FileText,Flame,Globe,Rocket,Search,Shield,Star,Target,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Crown_Database_FileText_Flame_Globe_Rocket_Search_Shield_Star_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Crown,Database,FileText,Flame,Globe,Rocket,Search,Shield,Star,Target,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Crown_Database_FileText_Flame_Globe_Rocket_Search_Shield_Star_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Crown,Database,FileText,Flame,Globe,Rocket,Search,Shield,Star,Target,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Crown_Database_FileText_Flame_Globe_Rocket_Search_Shield_Star_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Crown,Database,FileText,Flame,Globe,Rocket,Search,Shield,Star,Target,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Crown_Database_FileText_Flame_Globe_Rocket_Search_Shield_Star_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Crown,Database,FileText,Flame,Globe,Rocket,Search,Shield,Star,Target,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Crown_Database_FileText_Flame_Globe_Rocket_Search_Shield_Star_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Crown,Database,FileText,Flame,Globe,Rocket,Search,Shield,Star,Target,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Crown_Database_FileText_Flame_Globe_Rocket_Search_Shield_Star_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Crown,Database,FileText,Flame,Globe,Rocket,Search,Shield,Star,Target,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/flame.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Crown_Database_FileText_Flame_Globe_Rocket_Search_Shield_Star_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Crown,Database,FileText,Flame,Globe,Rocket,Search,Shield,Star,Target,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Crown_Database_FileText_Flame_Globe_Rocket_Search_Shield_Star_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Crown,Database,FileText,Flame,Globe,Rocket,Search,Shield,Star,Target,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Crown_Database_FileText_Flame_Globe_Rocket_Search_Shield_Star_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Crown,Database,FileText,Flame,Globe,Rocket,Search,Shield,Star,Target,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Crown_Database_FileText_Flame_Globe_Rocket_Search_Shield_Star_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Crown,Database,FileText,Flame,Globe,Rocket,Search,Shield,Star,Target,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rocket.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Crown_Database_FileText_Flame_Globe_Rocket_Search_Shield_Star_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Crown,Database,FileText,Flame,Globe,Rocket,Search,Shield,Star,Target,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction UserDashboard(param) {\n    let { user } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        level: 1,\n        score: 0,\n        rank: 999,\n        streak: 0,\n        scansCompleted: 0,\n        vulnerabilitiesFound: 0,\n        osintQueries: 0,\n        filesAnalyzed: 0\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadDashboardStats();\n    }, []);\n    const loadDashboardStats = async ()=>{\n        try {\n            setLoading(true);\n            console.log(\"\\uD83D\\uDD04 Loading dashboard stats...\");\n            const response = await fetch(\"/api/dashboard/stats\", {\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(localStorage.getItem(\"token\"))\n                }\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setStats(data);\n                console.log(\"✅ Dashboard stats loaded:\", data);\n            } else {\n                console.error(\"❌ Failed to load dashboard stats\");\n            }\n        } catch (error) {\n            console.error(\"❌ Error loading dashboard stats:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Base actions for all users\n    const baseActions = [\n        {\n            title: \"OSINT Lookup\",\n            description: \"Investigate emails, phone numbers, and personal information\",\n            icon: _barrel_optimize_names_ArrowRight_Award_Crown_Database_FileText_Flame_Globe_Rocket_Search_Shield_Star_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            href: \"/osint\",\n            badge: \"Popular\",\n            color: \"cyber-primary\",\n            stats: \"456 queries\"\n        },\n        {\n            title: \"Vulnerability Scan\",\n            description: \"Scan websites and applications for security vulnerabilities\",\n            icon: _barrel_optimize_names_ArrowRight_Award_Crown_Database_FileText_Flame_Globe_Rocket_Search_Shield_Star_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            href: \"/scanner\",\n            premium: true,\n            color: \"cyber-secondary\",\n            stats: \"142 scans\"\n        },\n        {\n            title: \"File Analysis\",\n            description: \"Analyze files for malware, webshells, and suspicious content\",\n            icon: _barrel_optimize_names_ArrowRight_Award_Crown_Database_FileText_Flame_Globe_Rocket_Search_Shield_Star_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            href: \"/file-analyzer\",\n            premium: true,\n            color: \"cyber-accent\",\n            stats: \"89 files\"\n        },\n        {\n            title: \"CVE Database\",\n            description: \"Search and explore the latest CVE vulnerabilities\",\n            icon: _barrel_optimize_names_ArrowRight_Award_Crown_Database_FileText_Flame_Globe_Rocket_Search_Shield_Star_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            href: \"/cve\",\n            color: \"green-400\",\n            stats: \"50K+ CVEs\"\n        },\n        {\n            title: \"Google Dorking\",\n            description: \"Use advanced search queries to find sensitive information\",\n            icon: _barrel_optimize_names_ArrowRight_Award_Crown_Database_FileText_Flame_Globe_Rocket_Search_Shield_Star_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            href: \"/dorking\",\n            color: \"blue-400\",\n            stats: \"Advanced\"\n        },\n        {\n            title: \"API Playground\",\n            description: \"Test and explore our API endpoints\",\n            icon: _barrel_optimize_names_ArrowRight_Award_Crown_Database_FileText_Flame_Globe_Rocket_Search_Shield_Star_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            href: \"/playground\",\n            color: \"purple-400\",\n            stats: \"2.8K calls\"\n        }\n    ];\n    // Admin actions for admin and super admin\n    const adminActions = [\n        {\n            title: \"User Management\",\n            description: \"Manage users, roles, and permissions\",\n            icon: _barrel_optimize_names_ArrowRight_Award_Crown_Database_FileText_Flame_Globe_Rocket_Search_Shield_Star_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            href: \"/dashboard?section=users\",\n            badge: \"Admin\",\n            color: \"yellow-400\",\n            stats: \"Management\"\n        },\n        {\n            title: \"System Analytics\",\n            description: \"View platform analytics and reports\",\n            icon: _barrel_optimize_names_ArrowRight_Award_Crown_Database_FileText_Flame_Globe_Rocket_Search_Shield_Star_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            href: \"/dashboard?section=analytics\",\n            badge: \"Admin\",\n            color: \"green-400\",\n            stats: \"Reports\"\n        }\n    ];\n    // Super admin actions for super admin only\n    const superAdminActions = [\n        {\n            title: \"Website Settings\",\n            description: \"Configure website settings and parameters\",\n            icon: _barrel_optimize_names_ArrowRight_Award_Crown_Database_FileText_Flame_Globe_Rocket_Search_Shield_Star_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            href: \"/dashboard?section=website-settings\",\n            badge: \"Super Admin\",\n            color: \"red-400\",\n            stats: \"Full Control\"\n        },\n        {\n            title: \"Payment System\",\n            description: \"Manage payments and subscriptions\",\n            icon: _barrel_optimize_names_ArrowRight_Award_Crown_Database_FileText_Flame_Globe_Rocket_Search_Shield_Star_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            href: \"/dashboard?section=payments\",\n            badge: \"Super Admin\",\n            color: \"green-400\",\n            stats: \"Revenue\"\n        }\n    ];\n    // Combine actions based on user role\n    const quickActions = [\n        ...baseActions,\n        ...(user === null || user === void 0 ? void 0 : user.role) === \"admin\" || (user === null || user === void 0 ? void 0 : user.role) === \"super_admin\" ? adminActions : [],\n        ...(user === null || user === void 0 ? void 0 : user.role) === \"super_admin\" ? superAdminActions : []\n    ];\n    const achievements = [\n        {\n            id: 1,\n            title: \"First Scan\",\n            description: \"Completed your first vulnerability scan\",\n            icon: _barrel_optimize_names_ArrowRight_Award_Crown_Database_FileText_Flame_Globe_Rocket_Search_Shield_Star_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            earned: true\n        },\n        {\n            id: 2,\n            title: \"OSINT Expert\",\n            description: \"Performed 100 OSINT queries\",\n            icon: _barrel_optimize_names_ArrowRight_Award_Crown_Database_FileText_Flame_Globe_Rocket_Search_Shield_Star_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            earned: true\n        },\n        {\n            id: 3,\n            title: \"Vulnerability Hunter\",\n            description: \"Found 10 vulnerabilities\",\n            icon: _barrel_optimize_names_ArrowRight_Award_Crown_Database_FileText_Flame_Globe_Rocket_Search_Shield_Star_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            earned: false\n        },\n        {\n            id: 4,\n            title: \"Streak Master\",\n            description: \"Maintained a 7-day streak\",\n            icon: _barrel_optimize_names_ArrowRight_Award_Crown_Database_FileText_Flame_Globe_Rocket_Search_Shield_Star_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n            earned: false\n        }\n    ];\n    const recentActivity = [\n        {\n            id: 1,\n            type: \"scan\",\n            description: \"Completed vulnerability scan on example.com\",\n            time: \"2 hours ago\",\n            severity: \"medium\"\n        },\n        {\n            id: 2,\n            type: \"osint\",\n            description: \"OSINT <NAME_EMAIL>\",\n            time: \"4 hours ago\",\n            severity: \"low\"\n        },\n        {\n            id: 3,\n            type: \"file\",\n            description: \"Analyzed suspicious.exe file\",\n            time: \"1 day ago\",\n            severity: \"high\"\n        },\n        {\n            id: 4,\n            type: \"cve\",\n            description: \"Searched CVE-2024-0001\",\n            time: \"2 days ago\",\n            severity: \"info\"\n        }\n    ];\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-2 border-cyber-primary border-t-transparent\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\UserDashboard.tsx\",\n                lineNumber: 238,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\UserDashboard.tsx\",\n            lineNumber: 237,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col lg:flex-row lg:items-center lg:justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl md:text-3xl lg:text-4xl font-bold mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-cyber-glow\",\n                                        children: \"\\uD83D\\uDEE1️ Welcome back,\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\UserDashboard.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 13\n                                    }, this),\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-cyber-pink\",\n                                        children: (user === null || user === void 0 ? void 0 : user.username) || \"User\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\UserDashboard.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\UserDashboard.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-300 text-base md:text-lg\",\n                                children: \"Your personal cybersecurity command center\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\UserDashboard.tsx\",\n                                lineNumber: 252,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap items-center gap-3 md:gap-6 mt-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Crown_Database_FileText_Flame_Globe_Rocket_Search_Shield_Star_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"h-5 w-5 text-cyber-primary\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\UserDashboard.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-cyber-primary font-medium\",\n                                                children: [\n                                                    \"Level \",\n                                                    stats.level\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\UserDashboard.tsx\",\n                                                lineNumber: 258,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\UserDashboard.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Crown_Database_FileText_Flame_Globe_Rocket_Search_Shield_Star_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"h-5 w-5 text-yellow-400\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\UserDashboard.tsx\",\n                                                lineNumber: 261,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-yellow-400 font-medium\",\n                                                children: [\n                                                    stats.score.toLocaleString(),\n                                                    \" points\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\UserDashboard.tsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\UserDashboard.tsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Crown_Database_FileText_Flame_Globe_Rocket_Search_Shield_Star_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-5 w-5 text-purple-400\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\UserDashboard.tsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-purple-400 font-medium\",\n                                                children: [\n                                                    \"Rank #\",\n                                                    stats.rank\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\UserDashboard.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\UserDashboard.tsx\",\n                                        lineNumber: 264,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Crown_Database_FileText_Flame_Globe_Rocket_Search_Shield_Star_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"h-4 w-4 text-cyber-secondary\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\UserDashboard.tsx\",\n                                                lineNumber: 269,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-cyber-secondary font-medium\",\n                                                children: [\n                                                    stats.streak,\n                                                    \" day streak\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\UserDashboard.tsx\",\n                                                lineNumber: 270,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\UserDashboard.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 13\n                                    }, this),\n                                    ((user === null || user === void 0 ? void 0 : user.role) === \"admin\" || (user === null || user === void 0 ? void 0 : user.role) === \"super_admin\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"•\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\UserDashboard.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"px-2 py-1 rounded text-xs font-medium \".concat((user === null || user === void 0 ? void 0 : user.role) === \"super_admin\" ? \"bg-red-500/20 text-red-400\" : \"bg-yellow-500/20 text-yellow-400\"),\n                                                children: (user === null || user === void 0 ? void 0 : user.role) === \"super_admin\" ? \"\\uD83D\\uDC51 SUPER ADMIN\" : \"\\uD83D\\uDEE1️ ADMIN\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\UserDashboard.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\UserDashboard.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\UserDashboard.tsx\",\n                        lineNumber: 247,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6 lg:mt-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-right\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-400\",\n                                            children: \"Current Plan\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\UserDashboard.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-lg font-semibold text-cyber-primary\",\n                                            children: (user === null || user === void 0 ? void 0 : user.plan) || \"Free\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\UserDashboard.tsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\UserDashboard.tsx\",\n                                    lineNumber: 288,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push(\"/plan\"),\n                                    className: \"btn-cyber-primary\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Crown_Database_FileText_Flame_Globe_Rocket_Search_Shield_Star_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\UserDashboard.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Upgrade Plan\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\UserDashboard.tsx\",\n                                    lineNumber: 294,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\UserDashboard.tsx\",\n                            lineNumber: 287,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\UserDashboard.tsx\",\n                        lineNumber: 286,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\UserDashboard.tsx\",\n                lineNumber: 246,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card-cyber\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400 text-sm\",\n                                            children: \"Scans Completed\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\UserDashboard.tsx\",\n                                            lineNumber: 310,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-white\",\n                                            children: stats.scansCompleted\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\UserDashboard.tsx\",\n                                            lineNumber: 311,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\UserDashboard.tsx\",\n                                    lineNumber: 309,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Crown_Database_FileText_Flame_Globe_Rocket_Search_Shield_Star_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"h-8 w-8 text-cyber-primary\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\UserDashboard.tsx\",\n                                    lineNumber: 313,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\UserDashboard.tsx\",\n                            lineNumber: 308,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\UserDashboard.tsx\",\n                        lineNumber: 307,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card-cyber\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400 text-sm\",\n                                            children: \"Vulnerabilities Found\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\UserDashboard.tsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-white\",\n                                            children: stats.vulnerabilitiesFound\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\UserDashboard.tsx\",\n                                            lineNumber: 321,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\UserDashboard.tsx\",\n                                    lineNumber: 319,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Crown_Database_FileText_Flame_Globe_Rocket_Search_Shield_Star_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-8 w-8 text-red-400\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\UserDashboard.tsx\",\n                                    lineNumber: 323,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\UserDashboard.tsx\",\n                            lineNumber: 318,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\UserDashboard.tsx\",\n                        lineNumber: 317,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card-cyber\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400 text-sm\",\n                                            children: \"OSINT Queries\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\UserDashboard.tsx\",\n                                            lineNumber: 330,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-white\",\n                                            children: stats.osintQueries\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\UserDashboard.tsx\",\n                                            lineNumber: 331,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\UserDashboard.tsx\",\n                                    lineNumber: 329,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Crown_Database_FileText_Flame_Globe_Rocket_Search_Shield_Star_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"h-8 w-8 text-blue-400\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\UserDashboard.tsx\",\n                                    lineNumber: 333,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\UserDashboard.tsx\",\n                            lineNumber: 328,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\UserDashboard.tsx\",\n                        lineNumber: 327,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card-cyber\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400 text-sm\",\n                                            children: \"Files Analyzed\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\UserDashboard.tsx\",\n                                            lineNumber: 340,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-white\",\n                                            children: stats.filesAnalyzed\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\UserDashboard.tsx\",\n                                            lineNumber: 341,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\UserDashboard.tsx\",\n                                    lineNumber: 339,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Crown_Database_FileText_Flame_Globe_Rocket_Search_Shield_Star_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"h-8 w-8 text-green-400\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\UserDashboard.tsx\",\n                                    lineNumber: 343,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\UserDashboard.tsx\",\n                            lineNumber: 338,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\UserDashboard.tsx\",\n                        lineNumber: 337,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\UserDashboard.tsx\",\n                lineNumber: 306,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-white mb-6\",\n                        children: \"Quick Actions\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\UserDashboard.tsx\",\n                        lineNumber: 350,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                        children: quickActions.map((action, index)=>{\n                            const Icon = action.icon;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                onClick: ()=>router.push(action.href),\n                                className: \"card-cyber hover:border-cyber-primary transition-all duration-300 cursor-pointer group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start justify-between mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 rounded-lg bg-\".concat(action.color, \"/20\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                    className: \"h-6 w-6 text-\".concat(action.color)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\UserDashboard.tsx\",\n                                                    lineNumber: 362,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\UserDashboard.tsx\",\n                                                lineNumber: 361,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    action.badge && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 text-xs bg-cyber-primary/20 text-cyber-primary rounded-full\",\n                                                        children: action.badge\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\UserDashboard.tsx\",\n                                                        lineNumber: 366,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    action.premium && (user === null || user === void 0 ? void 0 : user.plan) === \"Free\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 text-xs bg-yellow-500/20 text-yellow-400 rounded-full\",\n                                                        children: \"PRO\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\UserDashboard.tsx\",\n                                                        lineNumber: 371,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Crown_Database_FileText_Flame_Globe_Rocket_Search_Shield_Star_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-400 group-hover:text-cyber-primary transition-colors\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\UserDashboard.tsx\",\n                                                        lineNumber: 375,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\UserDashboard.tsx\",\n                                                lineNumber: 364,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\UserDashboard.tsx\",\n                                        lineNumber: 360,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-white mb-2\",\n                                        children: action.title\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\UserDashboard.tsx\",\n                                        lineNumber: 379,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 text-sm mb-3\",\n                                        children: action.description\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\UserDashboard.tsx\",\n                                        lineNumber: 380,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: action.stats\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\UserDashboard.tsx\",\n                                                lineNumber: 383,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-green-400 rounded-full animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\UserDashboard.tsx\",\n                                                lineNumber: 384,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\UserDashboard.tsx\",\n                                        lineNumber: 382,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\UserDashboard.tsx\",\n                                lineNumber: 355,\n                                columnNumber: 15\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\UserDashboard.tsx\",\n                        lineNumber: 351,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\UserDashboard.tsx\",\n                lineNumber: 349,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\UserDashboard.tsx\",\n        lineNumber: 244,\n        columnNumber: 5\n    }, this);\n}\n_s(UserDashboard, \"6kFn175cYZQgl9qT56s6VyL+Iwo=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = UserDashboard;\nvar _c;\n$RefreshReg$(_c, \"UserDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/dashboard/UserDashboard.tsx\n"));

/***/ })

});