import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/database'
import crypto from 'crypto'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const signature = request.headers.get('x-callback-signature') || request.headers.get('signature')
    const gateway = request.headers.get('x-gateway') || 'unknown'

    console.log('Webhook received:', { gateway, body })

    // Verify webhook signature based on gateway
    let isValidSignature = false
    let paymentData: any = {}

    switch (gateway) {
      case 'tripay':
        isValidSignature = await verifyTripaySignature(body, signature)
        paymentData = parseTripayWebhook(body)
        break
      case 'midtrans':
        isValidSignature = await verifyMidtransSignature(body, signature)
        paymentData = parseMidtransWebhook(body)
        break
      case 'xendit':
        isValidSignature = await verifyXenditSignature(body, signature)
        paymentData = parseXenditWebhook(body)
        break
      default:
        console.log('Unknown gateway:', gateway)
        return NextResponse.json({ error: 'Unknown gateway' }, { status: 400 })
    }

    if (!isValidSignature) {
      console.log('Invalid webhook signature')
      return NextResponse.json({ error: 'Invalid signature' }, { status: 401 })
    }

    // Process payment update
    await processPaymentUpdate(paymentData)

    return NextResponse.json({ success: true, message: 'Webhook processed' })

  } catch (error) {
    console.error('Webhook processing error:', error)
    return NextResponse.json(
      { error: 'Webhook processing failed' },
      { status: 500 }
    )
  }
}

async function verifyTripaySignature(body: any, signature: string | null): Promise<boolean> {
  if (!signature) return false
  
  const privateKey = process.env.TRIPAY_PRIVATE_KEY || ''
  const callbackSignature = crypto
    .createHmac('sha256', privateKey)
    .update(JSON.stringify(body))
    .digest('hex')

  return signature === callbackSignature
}

async function verifyMidtransSignature(body: any, signature: string | null): Promise<boolean> {
  if (!signature) return false
  
  const serverKey = process.env.MIDTRANS_SERVER_KEY || ''
  const orderId = body.order_id
  const statusCode = body.status_code
  const grossAmount = body.gross_amount
  
  const signatureKey = crypto
    .createHash('sha512')
    .update(orderId + statusCode + grossAmount + serverKey)
    .digest('hex')

  return signature === signatureKey
}

async function verifyXenditSignature(body: any, signature: string | null): Promise<boolean> {
  if (!signature) return false
  
  const webhookToken = process.env.XENDIT_WEBHOOK_TOKEN || ''
  const computedSignature = crypto
    .createHmac('sha256', webhookToken)
    .update(JSON.stringify(body))
    .digest('hex')

  return signature === computedSignature
}

function parseTripayWebhook(body: any): any {
  return {
    gateway: 'tripay',
    paymentId: body.merchant_ref,
    gatewayPaymentId: body.reference,
    status: mapTripayStatus(body.status),
    amount: body.amount,
    paidAt: body.paid_at ? new Date(body.paid_at * 1000) : null,
    raw: body
  }
}

function parseMidtransWebhook(body: any): any {
  return {
    gateway: 'midtrans',
    paymentId: body.order_id,
    gatewayPaymentId: body.transaction_id,
    status: mapMidtransStatus(body.transaction_status),
    amount: parseFloat(body.gross_amount),
    paidAt: body.settlement_time ? new Date(body.settlement_time) : null,
    raw: body
  }
}

function parseXenditWebhook(body: any): any {
  return {
    gateway: 'xendit',
    paymentId: body.external_id,
    gatewayPaymentId: body.id,
    status: mapXenditStatus(body.status),
    amount: body.amount,
    paidAt: body.paid_at ? new Date(body.paid_at) : null,
    raw: body
  }
}

function mapTripayStatus(status: string): string {
  const statusMap: Record<string, string> = {
    'PAID': 'completed',
    'EXPIRED': 'expired',
    'FAILED': 'failed',
    'UNPAID': 'pending'
  }
  return statusMap[status] || 'unknown'
}

function mapMidtransStatus(status: string): string {
  const statusMap: Record<string, string> = {
    'settlement': 'completed',
    'pending': 'pending',
    'expire': 'expired',
    'cancel': 'cancelled',
    'deny': 'failed'
  }
  return statusMap[status] || 'unknown'
}

function mapXenditStatus(status: string): string {
  const statusMap: Record<string, string> = {
    'PAID': 'completed',
    'EXPIRED': 'expired',
    'PENDING': 'pending'
  }
  return statusMap[status] || 'unknown'
}

async function processPaymentUpdate(paymentData: any): Promise<void> {
  const { paymentId, gatewayPaymentId, status, amount, paidAt, gateway, raw } = paymentData

  try {
    // Get payment record
    const [paymentRows] = await db.query(
      'SELECT * FROM payments WHERE id = ? OR gateway_payment_id = ?',
      [paymentId, gatewayPaymentId]
    )

    if (!paymentRows || (paymentRows as any[]).length === 0) {
      console.log('Payment not found:', paymentId)
      return
    }

    const payment = (paymentRows as any[])[0]

    // Update payment status
    await db.query(`
      UPDATE payments 
      SET status = ?, gateway_payment_id = ?, paid_at = ?, webhook_data = ?, updated_at = NOW()
      WHERE id = ?
    `, [status, gatewayPaymentId, paidAt, JSON.stringify(raw), payment.id])

    // Log webhook
    await db.query(`
      INSERT INTO payment_logs (payment_id, user_id, action, details, created_at)
      VALUES (?, ?, 'webhook_received', ?, NOW())
    `, [payment.id, payment.user_id, JSON.stringify({ gateway, status, amount })])

    // If payment is completed, activate subscription
    if (status === 'completed') {
      await activateSubscription(payment)
    }

    // If payment failed or expired, handle accordingly
    if (status === 'failed' || status === 'expired') {
      await handleFailedPayment(payment)
    }

  } catch (error) {
    console.error('Error processing payment update:', error)
    throw error
  }
}

async function activateSubscription(payment: any): Promise<void> {
  try {
    // Get plan details
    const [planRows] = await db.query(
      'SELECT * FROM plans WHERE id = ?',
      [payment.plan_id]
    )

    if (!planRows || (planRows as any[]).length === 0) {
      throw new Error('Plan not found')
    }

    const plan = (planRows as any[])[0]

    // Calculate subscription end date
    const startDate = new Date()
    const endDate = new Date(startDate)

    // Add duration based on plan
    switch (plan.duration) {
      case 'daily':
        endDate.setDate(endDate.getDate() + 1)
        break
      case 'weekly':
        endDate.setDate(endDate.getDate() + 7)
        break
      case 'monthly':
        endDate.setMonth(endDate.getMonth() + 1)
        break
      case 'yearly':
        endDate.setFullYear(endDate.getFullYear() + 1)
        break
    }

    // Create or update subscription
    await db.query(`
      INSERT INTO subscriptions (
        user_id, plan_id, payment_id, status, starts_at, expires_at, created_at
      ) VALUES (?, ?, ?, 'active', ?, ?, NOW())
      ON DUPLICATE KEY UPDATE
        plan_id = VALUES(plan_id),
        payment_id = VALUES(payment_id),
        status = 'active',
        expires_at = VALUES(expires_at),
        updated_at = NOW()
    `, [payment.user_id, payment.plan_id, payment.id, startDate, endDate])

    // Update user plan
    await db.query(
      'UPDATE users SET plan = ?, plan_expires_at = ?, updated_at = NOW() WHERE id = ?',
      [plan.type, endDate, payment.user_id]
    )

    // Log subscription activation
    await db.query(`
      INSERT INTO payment_logs (payment_id, user_id, action, details, created_at)
      VALUES (?, ?, 'subscription_activated', ?, NOW())
    `, [payment.id, payment.user_id, JSON.stringify({ planType: plan.type, expiresAt: endDate })])

    console.log('Subscription activated for user:', payment.user_id)

  } catch (error) {
    console.error('Error activating subscription:', error)
    throw error
  }
}

async function handleFailedPayment(payment: any): Promise<void> {
  try {
    // Log failed payment
    await db.query(`
      INSERT INTO payment_logs (payment_id, user_id, action, details, created_at)
      VALUES (?, ?, 'payment_failed', ?, NOW())
    `, [payment.id, payment.user_id, JSON.stringify({ reason: 'Payment failed or expired' })])

    // TODO: Send notification to user about failed payment
    // TODO: Optionally create a new payment link with extended expiry

    console.log('Payment failed for user:', payment.user_id)

  } catch (error) {
    console.error('Error handling failed payment:', error)
  }
}
