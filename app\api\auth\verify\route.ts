import { NextRequest, NextResponse } from 'next/server'
import { SimpleAuthService } from '@/lib/auth-simple'

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 Token verification request started')

    // Get authentication result
    const authResult = await SimpleAuthService.authenticateRequest(request)

    if (!authResult.success || !authResult.user) {
      console.log('❌ Token verification failed:', authResult.message)
      return NextResponse.json(
        {
          success: false,
          error: authResult.message || 'Invalid or expired token',
          authenticated: false
        },
        { status: 401 }
      )
    }

    console.log(`✅ Token verified for user: ${authResult.user.username} (${authResult.user.id})`)

    return NextResponse.json({
      success: true,
      authenticated: true,
      user: {
        id: authResult.user.id,
        username: authResult.user.username,
        email: authResult.user.email,
        fullName: authResult.user.full_name,
        role: authResult.user.role,
        plan: authResult.user.plan,
        level: authResult.user.level,
        score: authResult.user.score,
        streak: authResult.user.streak_days,
        emailVerified: authResult.user.email_verified,
        lastActive: authResult.user.last_active,
        createdAt: authResult.user.created_at
      }
    })

  } catch (error) {
    console.error('❌ Token verification error:', error)

    return NextResponse.json(
      {
        success: false,
        error: 'Token verification failed',
        authenticated: false
      },
      { status: 401 }
    )
  }
}

export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}
