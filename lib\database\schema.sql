-- KodeXGuard Complete Database Schema
-- Enterprise-grade cybersecurity platform database

-- =====================================================
-- 1. USERS & AUTHENTICATION TABLES
-- =====================================================

CREATE TABLE IF NOT EXISTS users (
  id INT AUTO_INCREMENT PRIMARY KEY,
  username VARCHAR(50) UNIQUE NOT NULL,
  email VARCHAR(255) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  full_name VARCHAR(255),
  role ENUM('user', 'admin', 'super_admin', 'moderator') DEFAULT 'user',
  plan ENUM('Free', 'Pro', 'Expert', 'Elite') DEFAULT 'Free',
  level INT DEFAULT 1,
  score INT DEFAULT 0,
  streak_days INT DEFAULT 0,
  email_verified BOOLEAN DEFAULT FALSE,
  email_verification_token VARCHAR(255),
  password_reset_token VARCHAR(255),
  password_reset_expires TIMESTAMP NULL,
  last_active TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_email (email),
  INDEX idx_username (username),
  INDEX idx_role (role),
  INDEX idx_plan (plan)
);

CREATE TABLE IF NOT EXISTS user_sessions (
  id VARCHAR(36) PRIMARY KEY,
  user_id INT NOT NULL,
  refresh_token VARCHAR(255) NOT NULL,
  ip_address VARCHAR(45),
  user_agent TEXT,
  expires_at TIMESTAMP NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  INDEX idx_user_id (user_id),
  INDEX idx_refresh_token (refresh_token),
  INDEX idx_expires_at (expires_at)
);

-- =====================================================
-- 2. API MANAGEMENT TABLES
-- =====================================================

CREATE TABLE IF NOT EXISTS api_keys (
  id VARCHAR(36) PRIMARY KEY,
  user_id INT NOT NULL,
  name VARCHAR(255) NOT NULL,
  key_hash VARCHAR(255) UNIQUE NOT NULL,
  permissions JSON NOT NULL,
  rate_limit_requests INT DEFAULT 1000,
  rate_limit_window INT DEFAULT 3600,
  is_active BOOLEAN DEFAULT TRUE,
  last_used TIMESTAMP NULL,
  expires_at TIMESTAMP NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  INDEX idx_user_id (user_id),
  INDEX idx_key_hash (key_hash),
  INDEX idx_active (is_active)
);

CREATE TABLE IF NOT EXISTS api_usage (
  id VARCHAR(36) PRIMARY KEY,
  api_key_id VARCHAR(36),
  endpoint VARCHAR(255) NOT NULL,
  method ENUM('GET', 'POST', 'PUT', 'DELETE', 'PATCH') NOT NULL,
  status_code INT NOT NULL,
  response_time_ms INT NOT NULL,
  request_size BIGINT DEFAULT 0,
  response_size BIGINT DEFAULT 0,
  ip_address VARCHAR(45),
  user_agent TEXT,
  error_message TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (api_key_id) REFERENCES api_keys(id) ON DELETE SET NULL,
  INDEX idx_api_key (api_key_id),
  INDEX idx_endpoint (endpoint),
  INDEX idx_created_at (created_at)
);

-- =====================================================
-- 3. CRAWLING ENGINE TABLES
-- =====================================================

CREATE TABLE IF NOT EXISTS crawl_sessions (
  id VARCHAR(36) PRIMARY KEY,
  user_id INT,
  target_url VARCHAR(2048) NOT NULL,
  config JSON,
  status ENUM('pending', 'running', 'completed', 'failed', 'paused') DEFAULT 'pending',
  pages_found INT DEFAULT 0,
  pages_crawled INT DEFAULT 0,
  errors JSON,
  results JSON,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  completed_at TIMESTAMP NULL,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
  INDEX idx_user_id (user_id),
  INDEX idx_status (status),
  INDEX idx_created_at (created_at)
);

CREATE TABLE IF NOT EXISTS crawl_results (
  id VARCHAR(36) PRIMARY KEY,
  session_id VARCHAR(36),
  url VARCHAR(2048) NOT NULL,
  title TEXT,
  content LONGTEXT,
  links JSON,
  emails JSON,
  phones JSON,
  technologies JSON,
  headers JSON,
  status_code INT,
  response_time INT,
  metadata JSON,
  crawled_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (session_id) REFERENCES crawl_sessions(id) ON DELETE CASCADE,
  INDEX idx_session_id (session_id),
  INDEX idx_url (url(255)),
  INDEX idx_crawled_at (crawled_at)
);

-- =====================================================
-- 4. SCANNER ENGINE TABLES
-- =====================================================

CREATE TABLE IF NOT EXISTS scan_sessions (
  id VARCHAR(36) PRIMARY KEY,
  user_id INT,
  target VARCHAR(500) NOT NULL,
  scan_type ENUM('nmap', 'masscan', 'zmap', 'custom') NOT NULL,
  config JSON,
  status ENUM('pending', 'running', 'completed', 'failed', 'cancelled') DEFAULT 'pending',
  progress INT DEFAULT 0,
  hosts_found INT DEFAULT 0,
  hosts_scanned INT DEFAULT 0,
  ports_found INT DEFAULT 0,
  results JSON,
  errors JSON,
  command TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  completed_at TIMESTAMP NULL,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
  INDEX idx_user_id (user_id),
  INDEX idx_status (status),
  INDEX idx_scan_type (scan_type)
);

CREATE TABLE IF NOT EXISTS scan_results (
  id VARCHAR(36) PRIMARY KEY,
  session_id VARCHAR(36),
  ip VARCHAR(45) NOT NULL,
  hostname VARCHAR(255),
  status ENUM('up', 'down', 'unknown') DEFAULT 'unknown',
  os_name VARCHAR(255),
  os_accuracy INT,
  ports JSON,
  latency DECIMAL(8,3),
  geolocation JSON,
  last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (session_id) REFERENCES scan_sessions(id) ON DELETE CASCADE,
  INDEX idx_session_id (session_id),
  INDEX idx_ip (ip),
  INDEX idx_status (status)
);

-- =====================================================
-- 5. MALWARE ANALYZER TABLES
-- =====================================================

CREATE TABLE IF NOT EXISTS malware_analyses (
  id VARCHAR(36) PRIMARY KEY,
  user_id INT,
  file_name VARCHAR(255) NOT NULL,
  file_size BIGINT NOT NULL,
  file_hash_md5 VARCHAR(32),
  file_hash_sha256 VARCHAR(64),
  analysis_type ENUM('static', 'dynamic', 'sandbox', 'comprehensive') NOT NULL,
  status ENUM('pending', 'running', 'completed', 'failed', 'timeout') DEFAULT 'pending',
  progress INT DEFAULT 0,
  is_malicious BOOLEAN DEFAULT FALSE,
  threat_type JSON,
  confidence_score DECIMAL(3,2),
  severity ENUM('low', 'medium', 'high', 'critical'),
  results JSON,
  errors JSON,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  completed_at TIMESTAMP NULL,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
  INDEX idx_user_id (user_id),
  INDEX idx_file_hash (file_hash_sha256),
  INDEX idx_is_malicious (is_malicious),
  INDEX idx_severity (severity)
);

CREATE TABLE IF NOT EXISTS malware_signatures (
  id VARCHAR(36) PRIMARY KEY,
  signature_type ENUM('yara', 'clamav', 'custom') NOT NULL,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  rule_content TEXT NOT NULL,
  severity ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium',
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_signature_type (signature_type),
  INDEX idx_is_active (is_active)
);

-- =====================================================
-- 6. BREACH PARSER TABLES
-- =====================================================

CREATE TABLE IF NOT EXISTS breaches (
  id VARCHAR(36) PRIMARY KEY,
  name VARCHAR(255) UNIQUE NOT NULL,
  domain VARCHAR(255),
  breach_date DATE,
  added_date DATE,
  modified_date DATE,
  pwn_count BIGINT DEFAULT 0,
  description TEXT,
  logo_path VARCHAR(500),
  data_classes JSON,
  is_verified BOOLEAN DEFAULT FALSE,
  is_fabricated BOOLEAN DEFAULT FALSE,
  is_sensitive BOOLEAN DEFAULT FALSE,
  is_retired BOOLEAN DEFAULT FALSE,
  is_spam_list BOOLEAN DEFAULT FALSE,
  is_malware BOOLEAN DEFAULT FALSE,
  is_subscription_free BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_name (name),
  INDEX idx_domain (domain),
  INDEX idx_breach_date (breach_date)
);

CREATE TABLE IF NOT EXISTS breach_records (
  id VARCHAR(36) PRIMARY KEY,
  breach_id VARCHAR(36),
  email VARCHAR(255) NOT NULL,
  email_hash VARCHAR(64) NOT NULL,
  password_hash VARCHAR(255),
  username VARCHAR(255),
  name VARCHAR(255),
  phone VARCHAR(50),
  address TEXT,
  additional_data JSON,
  date_added TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (breach_id) REFERENCES breaches(id) ON DELETE CASCADE,
  INDEX idx_breach_id (breach_id),
  INDEX idx_email_hash (email_hash),
  INDEX idx_email (email),
  INDEX idx_username (username)
);

CREATE TABLE IF NOT EXISTS parse_jobs (
  id VARCHAR(36) PRIMARY KEY,
  user_id INT,
  file_name VARCHAR(255) NOT NULL,
  file_path VARCHAR(500) NOT NULL,
  file_size BIGINT NOT NULL,
  file_hash VARCHAR(64) NOT NULL,
  format ENUM('csv', 'json', 'txt', 'sql', 'xml', 'custom') NOT NULL,
  status ENUM('pending', 'parsing', 'completed', 'failed', 'cancelled') DEFAULT 'pending',
  progress INT DEFAULT 0,
  records_found BIGINT DEFAULT 0,
  records_parsed BIGINT DEFAULT 0,
  records_valid BIGINT DEFAULT 0,
  errors JSON,
  breach_id VARCHAR(36),
  options JSON,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  completed_at TIMESTAMP NULL,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
  FOREIGN KEY (breach_id) REFERENCES breaches(id) ON DELETE SET NULL,
  INDEX idx_user_id (user_id),
  INDEX idx_status (status)
);

-- =====================================================
-- 7. SEARCH ENGINE TABLES
-- =====================================================

CREATE TABLE IF NOT EXISTS search_indices (
  id VARCHAR(36) PRIMARY KEY,
  name VARCHAR(100) UNIQUE NOT NULL,
  type VARCHAR(50) NOT NULL,
  settings JSON,
  mappings JSON,
  document_count BIGINT DEFAULT 0,
  size_bytes BIGINT DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_name (name),
  INDEX idx_type (type)
);

CREATE TABLE IF NOT EXISTS search_queries (
  id VARCHAR(36) PRIMARY KEY,
  user_id INT,
  query_text TEXT NOT NULL,
  filters JSON,
  results_count INT DEFAULT 0,
  response_time_ms INT DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
  INDEX idx_user_id (user_id),
  INDEX idx_created_at (created_at)
);

-- =====================================================
-- 8. QUEUE SYSTEM TABLES
-- =====================================================

CREATE TABLE IF NOT EXISTS queue_messages (
  id VARCHAR(36) PRIMARY KEY,
  queue_name VARCHAR(100) NOT NULL,
  message_type VARCHAR(100) NOT NULL,
  payload JSON NOT NULL,
  priority ENUM('low', 'normal', 'high', 'critical') DEFAULT 'normal',
  status ENUM('pending', 'processing', 'completed', 'failed', 'retrying') DEFAULT 'pending',
  retries INT DEFAULT 0,
  max_retries INT DEFAULT 3,
  error_message TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  scheduled_at TIMESTAMP NULL,
  started_at TIMESTAMP NULL,
  completed_at TIMESTAMP NULL,
  INDEX idx_queue_status (queue_name, status),
  INDEX idx_scheduled (scheduled_at),
  INDEX idx_created (created_at)
);

CREATE TABLE IF NOT EXISTS queue_stats (
  id VARCHAR(36) PRIMARY KEY,
  queue_name VARCHAR(100) UNIQUE NOT NULL,
  messages_total BIGINT DEFAULT 0,
  messages_processed BIGINT DEFAULT 0,
  messages_failed BIGINT DEFAULT 0,
  messages_in_queue BIGINT DEFAULT 0,
  avg_processing_time_ms INT DEFAULT 0,
  last_processed TIMESTAMP NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_queue_name (queue_name)
);

-- =====================================================
-- 9. VULNERABILITY MANAGEMENT TABLES
-- =====================================================

CREATE TABLE IF NOT EXISTS vulnerabilities (
  id VARCHAR(36) PRIMARY KEY,
  cve_id VARCHAR(20) UNIQUE,
  title VARCHAR(500) NOT NULL,
  description TEXT,
  severity ENUM('low', 'medium', 'high', 'critical') NOT NULL,
  cvss_score DECIMAL(3,1),
  cvss_vector VARCHAR(255),
  affected_products JSON,
  references JSON,
  exploit_available BOOLEAN DEFAULT FALSE,
  patch_available BOOLEAN DEFAULT FALSE,
  published_date DATE,
  modified_date DATE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_cve_id (cve_id),
  INDEX idx_severity (severity),
  INDEX idx_cvss_score (cvss_score),
  INDEX idx_published_date (published_date)
);

CREATE TABLE IF NOT EXISTS vulnerability_scans (
  id VARCHAR(36) PRIMARY KEY,
  user_id INT,
  target_url VARCHAR(2048) NOT NULL,
  scan_type ENUM('web', 'network', 'api', 'mobile') NOT NULL,
  status ENUM('pending', 'running', 'completed', 'failed') DEFAULT 'pending',
  vulnerabilities_found INT DEFAULT 0,
  scan_results JSON,
  scan_config JSON,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  completed_at TIMESTAMP NULL,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
  INDEX idx_user_id (user_id),
  INDEX idx_target_url (target_url(255)),
  INDEX idx_status (status)
);

-- =====================================================
-- 10. NOTIFICATION & REPORTING TABLES
-- =====================================================

CREATE TABLE IF NOT EXISTS notifications (
  id VARCHAR(36) PRIMARY KEY,
  user_id INT,
  type ENUM('info', 'warning', 'error', 'success') NOT NULL,
  title VARCHAR(255) NOT NULL,
  message TEXT NOT NULL,
  data JSON,
  is_read BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  INDEX idx_user_id (user_id),
  INDEX idx_is_read (is_read),
  INDEX idx_created_at (created_at)
);

CREATE TABLE IF NOT EXISTS reports (
  id VARCHAR(36) PRIMARY KEY,
  user_id INT,
  title VARCHAR(255) NOT NULL,
  type ENUM('vulnerability', 'scan', 'breach', 'malware', 'custom') NOT NULL,
  format ENUM('pdf', 'html', 'json', 'csv') NOT NULL,
  status ENUM('pending', 'generating', 'completed', 'failed') DEFAULT 'pending',
  file_path VARCHAR(500),
  data JSON,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  completed_at TIMESTAMP NULL,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  INDEX idx_user_id (user_id),
  INDEX idx_type (type),
  INDEX idx_status (status)
);

-- =====================================================
-- 11. SYSTEM MONITORING TABLES
-- =====================================================

CREATE TABLE IF NOT EXISTS system_logs (
  id VARCHAR(36) PRIMARY KEY,
  level ENUM('debug', 'info', 'warning', 'error', 'critical') NOT NULL,
  component VARCHAR(100) NOT NULL,
  message TEXT NOT NULL,
  data JSON,
  user_id INT,
  ip_address VARCHAR(45),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
  INDEX idx_level (level),
  INDEX idx_component (component),
  INDEX idx_created_at (created_at)
);

CREATE TABLE IF NOT EXISTS system_metrics (
  id VARCHAR(36) PRIMARY KEY,
  metric_name VARCHAR(100) NOT NULL,
  metric_value DECIMAL(15,4) NOT NULL,
  metric_unit VARCHAR(20),
  tags JSON,
  timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_metric_name (metric_name),
  INDEX idx_timestamp (timestamp)
);

-- =====================================================
-- 12. INITIAL DATA INSERTS
-- =====================================================

-- Insert default admin user (password: admin123)
INSERT IGNORE INTO users (username, email, password_hash, full_name, role, plan, email_verified)
VALUES ('admin', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/hL.ooqWw.', 'System Administrator', 'admin', 'Elite', TRUE);

-- Insert default super admin user (password: superadmin123)
INSERT IGNORE INTO users (username, email, password_hash, full_name, role, plan, email_verified)
VALUES ('superadmin', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/hL.ooqWw.', 'Super Administrator', 'super_admin', 'Elite', TRUE);

-- Insert default queue stats
INSERT IGNORE INTO queue_stats (id, queue_name) VALUES 
('q1', 'vulnerability-scans'),
('q2', 'osint-queries'),
('q3', 'malware-analysis'),
('q4', 'breach-parsing'),
('q5', 'search-indexing'),
('q6', 'notifications'),
('q7', 'reports'),
('q8', 'cleanup');

-- Insert default search indices
INSERT IGNORE INTO search_indices (id, name, type, settings) VALUES 
('i1', 'vulnerabilities', 'vulnerability', '{}'),
('i2', 'breaches', 'breach', '{}'),
('i3', 'malware', 'malware', '{}'),
('i4', 'hosts', 'host', '{}'),
('i5', 'cves', 'cve', '{}');
