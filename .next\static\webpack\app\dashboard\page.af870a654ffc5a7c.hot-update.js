"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./components/dashboard/AdminSections.tsx":
/*!************************************************!*\
  !*** ./components/dashboard/AdminSections.tsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AdminSections; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Ban_CheckCircle_Clock_CreditCard_Crown_DollarSign_Edit_Eye_Lock_Search_Shield_UserPlus_Users_Webhook_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Ban,CheckCircle,Clock,CreditCard,Crown,DollarSign,Edit,Eye,Lock,Search,Shield,UserPlus,Users,Webhook!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Ban_CheckCircle_Clock_CreditCard_Crown_DollarSign_Edit_Eye_Lock_Search_Shield_UserPlus_Users_Webhook_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Ban,CheckCircle,Clock,CreditCard,Crown,DollarSign,Edit,Eye,Lock,Search,Shield,UserPlus,Users,Webhook!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Ban_CheckCircle_Clock_CreditCard_Crown_DollarSign_Edit_Eye_Lock_Search_Shield_UserPlus_Users_Webhook_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Ban,CheckCircle,Clock,CreditCard,Crown,DollarSign,Edit,Eye,Lock,Search,Shield,UserPlus,Users,Webhook!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Ban_CheckCircle_Clock_CreditCard_Crown_DollarSign_Edit_Eye_Lock_Search_Shield_UserPlus_Users_Webhook_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Ban,CheckCircle,Clock,CreditCard,Crown,DollarSign,Edit,Eye,Lock,Search,Shield,UserPlus,Users,Webhook!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Ban_CheckCircle_Clock_CreditCard_Crown_DollarSign_Edit_Eye_Lock_Search_Shield_UserPlus_Users_Webhook_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Ban,CheckCircle,Clock,CreditCard,Crown,DollarSign,Edit,Eye,Lock,Search,Shield,UserPlus,Users,Webhook!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Ban_CheckCircle_Clock_CreditCard_Crown_DollarSign_Edit_Eye_Lock_Search_Shield_UserPlus_Users_Webhook_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Ban,CheckCircle,Clock,CreditCard,Crown,DollarSign,Edit,Eye,Lock,Search,Shield,UserPlus,Users,Webhook!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Ban_CheckCircle_Clock_CreditCard_Crown_DollarSign_Edit_Eye_Lock_Search_Shield_UserPlus_Users_Webhook_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Ban,CheckCircle,Clock,CreditCard,Crown,DollarSign,Edit,Eye,Lock,Search,Shield,UserPlus,Users,Webhook!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen-square.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Ban_CheckCircle_Clock_CreditCard_Crown_DollarSign_Edit_Eye_Lock_Search_Shield_UserPlus_Users_Webhook_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Ban,CheckCircle,Clock,CreditCard,Crown,DollarSign,Edit,Eye,Lock,Search,Shield,UserPlus,Users,Webhook!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/ban.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Ban_CheckCircle_Clock_CreditCard_Crown_DollarSign_Edit_Eye_Lock_Search_Shield_UserPlus_Users_Webhook_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Ban,CheckCircle,Clock,CreditCard,Crown,DollarSign,Edit,Eye,Lock,Search,Shield,UserPlus,Users,Webhook!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Ban_CheckCircle_Clock_CreditCard_Crown_DollarSign_Edit_Eye_Lock_Search_Shield_UserPlus_Users_Webhook_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Ban,CheckCircle,Clock,CreditCard,Crown,DollarSign,Edit,Eye,Lock,Search,Shield,UserPlus,Users,Webhook!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Ban_CheckCircle_Clock_CreditCard_Crown_DollarSign_Edit_Eye_Lock_Search_Shield_UserPlus_Users_Webhook_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Ban,CheckCircle,Clock,CreditCard,Crown,DollarSign,Edit,Eye,Lock,Search,Shield,UserPlus,Users,Webhook!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/webhook.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Ban_CheckCircle_Clock_CreditCard_Crown_DollarSign_Edit_Eye_Lock_Search_Shield_UserPlus_Users_Webhook_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Ban,CheckCircle,Clock,CreditCard,Crown,DollarSign,Edit,Eye,Lock,Search,Shield,UserPlus,Users,Webhook!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Ban_CheckCircle_Clock_CreditCard_Crown_DollarSign_Edit_Eye_Lock_Search_Shield_UserPlus_Users_Webhook_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Ban,CheckCircle,Clock,CreditCard,Crown,DollarSign,Edit,Eye,Lock,Search,Shield,UserPlus,Users,Webhook!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Ban_CheckCircle_Clock_CreditCard_Crown_DollarSign_Edit_Eye_Lock_Search_Shield_UserPlus_Users_Webhook_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Ban,CheckCircle,Clock,CreditCard,Crown,DollarSign,Edit,Eye,Lock,Search,Shield,UserPlus,Users,Webhook!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Ban_CheckCircle_Clock_CreditCard_Crown_DollarSign_Edit_Eye_Lock_Search_Shield_UserPlus_Users_Webhook_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Ban,CheckCircle,Clock,CreditCard,Crown,DollarSign,Edit,Eye,Lock,Search,Shield,UserPlus,Users,Webhook!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Ban_CheckCircle_Clock_CreditCard_Crown_DollarSign_Edit_Eye_Lock_Search_Shield_UserPlus_Users_Webhook_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Ban,CheckCircle,Clock,CreditCard,Crown,DollarSign,Edit,Eye,Lock,Search,Shield,UserPlus,Users,Webhook!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction AdminSections(param) {\n    let { section, user } = param;\n    _s();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [users, setUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadSectionData();\n    }, [\n        section\n    ]);\n    const loadSectionData = async ()=>{\n        try {\n            setLoading(true);\n            console.log(\"\\uD83D\\uDD04 Loading \".concat(section, \" data...\"));\n            // Load different data based on section\n            switch(section){\n                case \"users\":\n                    await loadUsers();\n                    break;\n                case \"analytics\":\n                    await loadAnalytics();\n                    break;\n                case \"monitoring\":\n                    await loadMonitoring();\n                    break;\n                case \"website-settings\":\n                    await loadWebsiteSettings();\n                    break;\n                case \"payments\":\n                    await loadPayments();\n                    break;\n                case \"bots\":\n                    await loadBots();\n                    break;\n                case \"whatsapp\":\n                    await loadWhatsApp();\n                    break;\n                case \"telegram\":\n                    await loadTelegram();\n                    break;\n                case \"system-config\":\n                    await loadSystemConfig();\n                    break;\n                case \"security\":\n                    await loadSecurity();\n                    break;\n                case \"database\":\n                    await loadDatabase();\n                    break;\n                case \"server\":\n                    await loadServer();\n                    break;\n                case \"api\":\n                    await loadAPI();\n                    break;\n                default:\n                    console.log(\"Unknown section:\", section);\n            }\n        } catch (error) {\n            console.error(\"❌ Error loading \".concat(section, \" data:\"), error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loadUsers = async ()=>{\n        // Mock data for now\n        setUsers([\n            {\n                id: \"1\",\n                username: \"admin\",\n                email: \"<EMAIL>\",\n                role: \"admin\",\n                plan: \"Elite\",\n                level: 10,\n                score: 5000,\n                full_name: \"System Administrator\",\n                created_at: \"2024-01-01T00:00:00Z\",\n                last_active: new Date().toISOString(),\n                email_verified: true,\n                status: \"active\"\n            },\n            {\n                id: \"2\",\n                username: \"superadmin\",\n                email: \"<EMAIL>\",\n                role: \"super_admin\",\n                plan: \"Elite\",\n                level: 15,\n                score: 10000,\n                full_name: \"Super Administrator\",\n                created_at: \"2024-01-01T00:00:00Z\",\n                last_active: new Date().toISOString(),\n                email_verified: true,\n                status: \"active\"\n            },\n            {\n                id: \"3\",\n                username: \"testuser\",\n                email: \"<EMAIL>\",\n                role: \"user\",\n                plan: \"Free\",\n                level: 3,\n                score: 150,\n                full_name: \"Test User\",\n                created_at: \"2024-01-15T00:00:00Z\",\n                last_active: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),\n                email_verified: true,\n                status: \"active\"\n            }\n        ]);\n    };\n    const loadAnalytics = async ()=>{\n        setStats({\n            totalUsers: 1247,\n            activeToday: 156,\n            totalScans: 15634,\n            revenue: 125000,\n            newUsersThisMonth: 89,\n            premiumUsers: 234,\n            vulnerabilitiesFound: 2456,\n            apiCalls: 45678\n        });\n    };\n    const loadMonitoring = async ()=>{\n        setStats({\n            systemHealth: \"healthy\",\n            uptime: 99.8,\n            cpuUsage: 45.2,\n            memoryUsage: 67.8,\n            diskUsage: 34.5,\n            activeConnections: 1234,\n            requestsPerMinute: 567,\n            errorRate: 0.02\n        });\n    };\n    const loadWebsiteSettings = async ()=>{\n        setStats({\n            siteName: \"KodeXGuard\",\n            siteDescription: \"Advanced Cybersecurity Platform\",\n            maintenanceMode: false,\n            registrationEnabled: true,\n            emailVerificationRequired: true,\n            maxUsersPerPlan: {\n                Free: 1000,\n                Student: 500,\n                Hobby: 200,\n                Bughunter: 100,\n                Cybersecurity: 50\n            }\n        });\n    };\n    const loadPayments = async ()=>{\n        setStats({\n            totalRevenue: 125000,\n            monthlyRevenue: 15000,\n            activeSubscriptions: 234,\n            pendingPayments: 12,\n            refunds: 3,\n            chargeback: 1\n        });\n    };\n    const loadBots = async ()=>{\n        setStats({\n            whatsappBot: {\n                status: \"online\",\n                connectedUsers: 45,\n                messagesProcessed: 1234,\n                uptime: 99.5\n            },\n            telegramBot: {\n                status: \"online\",\n                connectedUsers: 78,\n                messagesProcessed: 2345,\n                uptime: 98.9\n            }\n        });\n    };\n    const loadWhatsApp = async ()=>{\n        setStats({\n            status: \"connected\",\n            qrCode: null,\n            connectedUsers: 45,\n            messagesProcessed: 1234,\n            lastMessage: new Date().toISOString(),\n            features: {\n                osintLookup: true,\n                vulnerabilityAlerts: true,\n                fileAnalysis: false,\n                customCommands: true\n            }\n        });\n    };\n    const loadTelegram = async ()=>{\n        setStats({\n            status: \"connected\",\n            botToken: \"CONFIGURED\",\n            connectedUsers: 78,\n            messagesProcessed: 2345,\n            lastMessage: new Date().toISOString(),\n            features: {\n                osintLookup: true,\n                vulnerabilityAlerts: true,\n                fileAnalysis: true,\n                customCommands: true,\n                inlineQueries: true\n            }\n        });\n    };\n    const loadSystemConfig = async ()=>{\n        setStats({\n            environment: \"production\",\n            version: \"1.0.0\",\n            database: \"connected\",\n            redis: \"connected\",\n            elasticsearch: \"connected\",\n            apiRateLimit: 1000,\n            maxFileSize: \"10MB\",\n            allowedFileTypes: [\n                \".exe\",\n                \".pdf\",\n                \".doc\",\n                \".zip\"\n            ],\n            securitySettings: {\n                twoFactorRequired: false,\n                passwordMinLength: 8,\n                sessionTimeout: 24,\n                maxLoginAttempts: 5\n            }\n        });\n    };\n    const loadSecurity = async ()=>{\n        setStats({\n            activeThreats: 3,\n            blockedIPs: 156,\n            suspiciousActivity: 12,\n            failedLogins: 45,\n            securityAlerts: [\n                {\n                    id: 1,\n                    type: \"brute_force\",\n                    severity: \"high\",\n                    ip: \"*************\",\n                    timestamp: new Date().toISOString(),\n                    blocked: true\n                },\n                {\n                    id: 2,\n                    type: \"suspicious_file\",\n                    severity: \"medium\",\n                    user: \"testuser\",\n                    timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(),\n                    blocked: false\n                }\n            ]\n        });\n    };\n    const loadDatabase = async ()=>{\n        setStats({\n            status: \"connected\",\n            size: \"2.5GB\",\n            tables: 25,\n            connections: 12,\n            queries: 15678,\n            slowQueries: 3,\n            backupStatus: \"completed\",\n            lastBackup: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString()\n        });\n    };\n    const loadServer = async ()=>{\n        setStats({\n            status: \"running\",\n            uptime: \"15 days\",\n            cpu: 45.2,\n            memory: 67.8,\n            disk: 34.5,\n            network: {\n                inbound: \"125 MB/s\",\n                outbound: \"89 MB/s\"\n            },\n            processes: 156,\n            services: {\n                nginx: \"running\",\n                mysql: \"running\",\n                redis: \"running\",\n                elasticsearch: \"running\"\n            }\n        });\n    };\n    const loadAPI = async ()=>{\n        setStats({\n            totalEndpoints: 45,\n            activeEndpoints: 42,\n            requestsToday: 15678,\n            averageResponseTime: \"125ms\",\n            errorRate: \"0.02%\",\n            topEndpoints: [\n                {\n                    endpoint: \"/api/osint/lookup\",\n                    requests: 5678,\n                    avgTime: \"89ms\"\n                },\n                {\n                    endpoint: \"/api/scanner/scan\",\n                    requests: 3456,\n                    avgTime: \"234ms\"\n                },\n                {\n                    endpoint: \"/api/cve/search\",\n                    requests: 2345,\n                    avgTime: \"45ms\"\n                }\n            ]\n        });\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-2 border-cyber-primary border-t-transparent\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                lineNumber: 436,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n            lineNumber: 435,\n            columnNumber: 7\n        }, this);\n    }\n    const renderSection = ()=>{\n        switch(section){\n            case \"users\":\n                return renderUserManagement();\n            case \"analytics\":\n                return renderAnalytics();\n            case \"monitoring\":\n                return renderMonitoring();\n            case \"website-settings\":\n                return renderWebsiteSettings();\n            case \"payments\":\n                return renderPayments();\n            case \"bots\":\n                return renderBots();\n            case \"whatsapp\":\n                return renderWhatsApp();\n            case \"telegram\":\n                return renderTelegram();\n            case \"system-config\":\n                return renderSystemConfig();\n            case \"security\":\n                return renderSecurity();\n            case \"database\":\n                return renderDatabase();\n            case \"server\":\n                return renderServer();\n            case \"api\":\n                return renderAPI();\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-white mb-4\",\n                            children: \"Section Not Found\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                            lineNumber: 472,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400\",\n                            children: [\n                                'The requested section \"',\n                                section,\n                                '\" is not available.'\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                            lineNumber: 473,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                    lineNumber: 471,\n                    columnNumber: 11\n                }, this);\n        }\n    };\n    const renderUserManagement = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4 md:space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl md:text-2xl font-bold text-white\",\n                            children: \"User Management\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                            lineNumber: 482,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row items-stretch sm:items-center gap-2 sm:gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"btn-cyber-secondary\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Ban_CheckCircle_Clock_CreditCard_Crown_DollarSign_Edit_Eye_Lock_Search_Shield_UserPlus_Users_Webhook_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                            lineNumber: 485,\n                                            columnNumber: 13\n                                        }, this),\n                                        \"Search Users\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                    lineNumber: 484,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"btn-cyber-primary\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Ban_CheckCircle_Clock_CreditCard_Crown_DollarSign_Edit_Eye_Lock_Search_Shield_UserPlus_Users_Webhook_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                            lineNumber: 489,\n                                            columnNumber: 13\n                                        }, this),\n                                        \"Add User\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                    lineNumber: 488,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                            lineNumber: 483,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                    lineNumber: 481,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400 text-sm\",\n                                                children: \"Total Users\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                lineNumber: 499,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: users.length\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                lineNumber: 500,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                        lineNumber: 498,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Ban_CheckCircle_Clock_CreditCard_Crown_DollarSign_Edit_Eye_Lock_Search_Shield_UserPlus_Users_Webhook_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"h-8 w-8 text-blue-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                        lineNumber: 502,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                lineNumber: 497,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                            lineNumber: 496,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400 text-sm\",\n                                                children: \"Active Users\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                lineNumber: 508,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: users.filter((u)=>u.status === \"active\").length\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                lineNumber: 509,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                        lineNumber: 507,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Ban_CheckCircle_Clock_CreditCard_Crown_DollarSign_Edit_Eye_Lock_Search_Shield_UserPlus_Users_Webhook_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-8 w-8 text-green-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                        lineNumber: 511,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                lineNumber: 506,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                            lineNumber: 505,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400 text-sm\",\n                                                children: \"Admins\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                lineNumber: 517,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: users.filter((u)=>u.role === \"admin\" || u.role === \"super_admin\").length\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                lineNumber: 518,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                        lineNumber: 516,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Ban_CheckCircle_Clock_CreditCard_Crown_DollarSign_Edit_Eye_Lock_Search_Shield_UserPlus_Users_Webhook_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-8 w-8 text-yellow-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                        lineNumber: 520,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                lineNumber: 515,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                            lineNumber: 514,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400 text-sm\",\n                                                children: \"Premium Users\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                lineNumber: 526,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: users.filter((u)=>u.plan !== \"Free\").length\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                lineNumber: 527,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                        lineNumber: 525,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Star, {\n                                        className: \"h-8 w-8 text-purple-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                        lineNumber: 529,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                lineNumber: 524,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                            lineNumber: 523,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                    lineNumber: 495,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card-cyber\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-x-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        className: \"border-b border-gray-700\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"text-left py-3 px-4 text-gray-300\",\n                                                children: \"User\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                lineNumber: 539,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"text-left py-3 px-4 text-gray-300\",\n                                                children: \"Role\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                lineNumber: 540,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"text-left py-3 px-4 text-gray-300\",\n                                                children: \"Plan\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                lineNumber: 541,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"text-left py-3 px-4 text-gray-300\",\n                                                children: \"Status\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                lineNumber: 542,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"text-left py-3 px-4 text-gray-300\",\n                                                children: \"Last Active\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                lineNumber: 543,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"text-left py-3 px-4 text-gray-300\",\n                                                children: \"Actions\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                lineNumber: 544,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                        lineNumber: 538,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                    lineNumber: 537,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                    children: users.map((user)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            className: \"border-b border-gray-800 hover:bg-gray-800/50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-3 px-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-white font-medium\",\n                                                                children: user.username\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                                lineNumber: 552,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-400 text-sm\",\n                                                                children: user.email\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                                lineNumber: 553,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                        lineNumber: 551,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                    lineNumber: 550,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-3 px-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 rounded text-xs font-medium \".concat(user.role === \"super_admin\" ? \"bg-red-500/20 text-red-400\" : user.role === \"admin\" ? \"bg-yellow-500/20 text-yellow-400\" : \"bg-blue-500/20 text-blue-400\"),\n                                                        children: user.role.replace(\"_\", \" \").toUpperCase()\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                        lineNumber: 557,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                    lineNumber: 556,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-3 px-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-cyber-primary\",\n                                                        children: user.plan\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                        lineNumber: 566,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                    lineNumber: 565,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-3 px-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 rounded text-xs font-medium \".concat(user.status === \"active\" ? \"bg-green-500/20 text-green-400\" : user.status === \"suspended\" ? \"bg-yellow-500/20 text-yellow-400\" : \"bg-red-500/20 text-red-400\"),\n                                                        children: user.status.toUpperCase()\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                        lineNumber: 569,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                    lineNumber: 568,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-3 px-4 text-gray-400 text-sm\",\n                                                    children: new Date(user.last_active).toLocaleDateString()\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                    lineNumber: 577,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-3 px-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"p-1 text-gray-400 hover:text-white\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Ban_CheckCircle_Clock_CreditCard_Crown_DollarSign_Edit_Eye_Lock_Search_Shield_UserPlus_Users_Webhook_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                                    lineNumber: 583,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                                lineNumber: 582,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"p-1 text-gray-400 hover:text-white\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Ban_CheckCircle_Clock_CreditCard_Crown_DollarSign_Edit_Eye_Lock_Search_Shield_UserPlus_Users_Webhook_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                                    lineNumber: 586,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                                lineNumber: 585,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            user.role !== \"super_admin\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"p-1 text-gray-400 hover:text-red-400\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Ban_CheckCircle_Clock_CreditCard_Crown_DollarSign_Edit_Eye_Lock_Search_Shield_UserPlus_Users_Webhook_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                                    lineNumber: 590,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                                lineNumber: 589,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                        lineNumber: 581,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                    lineNumber: 580,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, user.id, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                            lineNumber: 549,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                    lineNumber: 547,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                            lineNumber: 536,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                        lineNumber: 535,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                    lineNumber: 534,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n            lineNumber: 480,\n            columnNumber: 5\n        }, this);\n    const renderAnalytics = ()=>{\n        var _stats_totalUsers, _stats_revenue, _stats_totalScans, _stats_apiCalls;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-2xl font-bold text-white\",\n                    children: \"Platform Analytics\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                    lineNumber: 606,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400 text-sm\",\n                                                children: \"Total Users\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                lineNumber: 612,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: (_stats_totalUsers = stats.totalUsers) === null || _stats_totalUsers === void 0 ? void 0 : _stats_totalUsers.toLocaleString()\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                lineNumber: 613,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                        lineNumber: 611,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Ban_CheckCircle_Clock_CreditCard_Crown_DollarSign_Edit_Eye_Lock_Search_Shield_UserPlus_Users_Webhook_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"h-8 w-8 text-blue-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                        lineNumber: 615,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                lineNumber: 610,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                            lineNumber: 609,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400 text-sm\",\n                                                children: \"Revenue\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                lineNumber: 621,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: [\n                                                    \"$\",\n                                                    (_stats_revenue = stats.revenue) === null || _stats_revenue === void 0 ? void 0 : _stats_revenue.toLocaleString()\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                lineNumber: 622,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                        lineNumber: 620,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Ban_CheckCircle_Clock_CreditCard_Crown_DollarSign_Edit_Eye_Lock_Search_Shield_UserPlus_Users_Webhook_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-8 w-8 text-green-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                        lineNumber: 624,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                lineNumber: 619,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                            lineNumber: 618,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400 text-sm\",\n                                                children: \"Total Scans\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                lineNumber: 630,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: (_stats_totalScans = stats.totalScans) === null || _stats_totalScans === void 0 ? void 0 : _stats_totalScans.toLocaleString()\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                lineNumber: 631,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                        lineNumber: 629,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Ban_CheckCircle_Clock_CreditCard_Crown_DollarSign_Edit_Eye_Lock_Search_Shield_UserPlus_Users_Webhook_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-8 w-8 text-cyber-primary\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                        lineNumber: 633,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                lineNumber: 628,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                            lineNumber: 627,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400 text-sm\",\n                                                children: \"API Calls\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                lineNumber: 639,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: (_stats_apiCalls = stats.apiCalls) === null || _stats_apiCalls === void 0 ? void 0 : _stats_apiCalls.toLocaleString()\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                lineNumber: 640,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                        lineNumber: 638,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Ban_CheckCircle_Clock_CreditCard_Crown_DollarSign_Edit_Eye_Lock_Search_Shield_UserPlus_Users_Webhook_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-8 w-8 text-purple-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                        lineNumber: 642,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                lineNumber: 637,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                            lineNumber: 636,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                    lineNumber: 608,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n            lineNumber: 605,\n            columnNumber: 5\n        }, this);\n    };\n    const renderMonitoring = ()=>{\n        var _stats_cpuUsage, _stats_memoryUsage, _stats_diskUsage;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-2xl font-bold text-white\",\n                    children: \"System Monitoring\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                    lineNumber: 651,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-white mb-4\",\n                                    children: \"CPU Usage\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                    lineNumber: 655,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-300\",\n                                            children: \"Current\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                            lineNumber: 657,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-cyber-primary font-medium\",\n                                            children: [\n                                                (_stats_cpuUsage = stats.cpuUsage) === null || _stats_cpuUsage === void 0 ? void 0 : _stats_cpuUsage.toFixed(1),\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                            lineNumber: 658,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                    lineNumber: 656,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full bg-gray-700 rounded-full h-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-cyber-primary h-2 rounded-full transition-all duration-300\",\n                                        style: {\n                                            width: \"\".concat(stats.cpuUsage, \"%\")\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                        lineNumber: 661,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                    lineNumber: 660,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                            lineNumber: 654,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-white mb-4\",\n                                    children: \"Memory Usage\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                    lineNumber: 669,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-300\",\n                                            children: \"Current\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                            lineNumber: 671,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-yellow-400 font-medium\",\n                                            children: [\n                                                (_stats_memoryUsage = stats.memoryUsage) === null || _stats_memoryUsage === void 0 ? void 0 : _stats_memoryUsage.toFixed(1),\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                            lineNumber: 672,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                    lineNumber: 670,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full bg-gray-700 rounded-full h-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-yellow-400 h-2 rounded-full transition-all duration-300\",\n                                        style: {\n                                            width: \"\".concat(stats.memoryUsage, \"%\")\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                        lineNumber: 675,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                    lineNumber: 674,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                            lineNumber: 668,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-white mb-4\",\n                                    children: \"Disk Usage\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                    lineNumber: 683,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-300\",\n                                            children: \"Current\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                            lineNumber: 685,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-green-400 font-medium\",\n                                            children: [\n                                                (_stats_diskUsage = stats.diskUsage) === null || _stats_diskUsage === void 0 ? void 0 : _stats_diskUsage.toFixed(1),\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                            lineNumber: 686,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                    lineNumber: 684,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full bg-gray-700 rounded-full h-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-green-400 h-2 rounded-full transition-all duration-300\",\n                                        style: {\n                                            width: \"\".concat(stats.diskUsage, \"%\")\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                        lineNumber: 689,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                    lineNumber: 688,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                            lineNumber: 682,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                    lineNumber: 653,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n            lineNumber: 650,\n            columnNumber: 5\n        }, this);\n    };\n    const renderWebsiteSettings = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-2xl font-bold text-white\",\n                    children: \"Website Settings\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                    lineNumber: 701,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card-cyber\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-white mb-4\",\n                            children: \"General Settings\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                            lineNumber: 704,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-gray-300 text-sm font-medium mb-2\",\n                                            children: \"Site Name\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                            lineNumber: 707,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: stats.siteName || \"\",\n                                            className: \"input-cyber w-full\",\n                                            placeholder: \"Enter site name\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                            lineNumber: 708,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                    lineNumber: 706,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-gray-300 text-sm font-medium mb-2\",\n                                            children: \"Site Description\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                            lineNumber: 716,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            value: stats.siteDescription || \"\",\n                                            className: \"input-cyber w-full h-24\",\n                                            placeholder: \"Enter site description\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                            lineNumber: 717,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                    lineNumber: 715,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-300\",\n                                            children: \"Maintenance Mode\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                            lineNumber: 724,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"px-4 py-2 rounded-lg font-medium \".concat(stats.maintenanceMode ? \"bg-red-500/20 text-red-400\" : \"bg-green-500/20 text-green-400\"),\n                                            children: stats.maintenanceMode ? \"Enabled\" : \"Disabled\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                            lineNumber: 725,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                    lineNumber: 723,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                            lineNumber: 705,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                    lineNumber: 703,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n            lineNumber: 700,\n            columnNumber: 5\n        }, this);\n    const renderPayments = ()=>{\n        var _stats_totalRevenue;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-2xl font-bold text-white\",\n                    children: \"Payment Management\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                    lineNumber: 738,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400 text-sm\",\n                                                children: \"Total Revenue\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                lineNumber: 744,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: [\n                                                    \"$\",\n                                                    (_stats_totalRevenue = stats.totalRevenue) === null || _stats_totalRevenue === void 0 ? void 0 : _stats_totalRevenue.toLocaleString()\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                lineNumber: 745,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                        lineNumber: 743,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Ban_CheckCircle_Clock_CreditCard_Crown_DollarSign_Edit_Eye_Lock_Search_Shield_UserPlus_Users_Webhook_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-8 w-8 text-green-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                        lineNumber: 747,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                lineNumber: 742,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                            lineNumber: 741,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400 text-sm\",\n                                                children: \"Active Subscriptions\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                lineNumber: 753,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: stats.activeSubscriptions\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                lineNumber: 754,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                        lineNumber: 752,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Ban_CheckCircle_Clock_CreditCard_Crown_DollarSign_Edit_Eye_Lock_Search_Shield_UserPlus_Users_Webhook_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-8 w-8 text-blue-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                        lineNumber: 756,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                lineNumber: 751,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                            lineNumber: 750,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400 text-sm\",\n                                                children: \"Pending Payments\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                lineNumber: 762,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: stats.pendingPayments\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                lineNumber: 763,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                        lineNumber: 761,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Ban_CheckCircle_Clock_CreditCard_Crown_DollarSign_Edit_Eye_Lock_Search_Shield_UserPlus_Users_Webhook_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-8 w-8 text-yellow-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                        lineNumber: 765,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                lineNumber: 760,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                            lineNumber: 759,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                    lineNumber: 740,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n            lineNumber: 737,\n            columnNumber: 5\n        }, this);\n    };\n    const renderBots = ()=>{\n        var _stats_whatsappBot, _stats_whatsappBot1, _stats_whatsappBot2, _stats_whatsappBot3, _stats_telegramBot, _stats_telegramBot1, _stats_telegramBot2, _stats_telegramBot3;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-2xl font-bold text-white\",\n                    children: \"Bot Management\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                    lineNumber: 774,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-white\",\n                                            children: \"WhatsApp Bot\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                            lineNumber: 779,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-3 h-3 rounded-full \".concat(((_stats_whatsappBot = stats.whatsappBot) === null || _stats_whatsappBot === void 0 ? void 0 : _stats_whatsappBot.status) === \"online\" ? \"bg-green-400\" : \"bg-red-400\", \" animate-pulse\")\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                            lineNumber: 780,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                    lineNumber: 778,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-400\",\n                                                    children: \"Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                    lineNumber: 786,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-400\",\n                                                    children: (_stats_whatsappBot1 = stats.whatsappBot) === null || _stats_whatsappBot1 === void 0 ? void 0 : _stats_whatsappBot1.status\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                    lineNumber: 787,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                            lineNumber: 785,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-400\",\n                                                    children: \"Connected Users\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                    lineNumber: 790,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white\",\n                                                    children: (_stats_whatsappBot2 = stats.whatsappBot) === null || _stats_whatsappBot2 === void 0 ? void 0 : _stats_whatsappBot2.connectedUsers\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                    lineNumber: 791,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                            lineNumber: 789,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-400\",\n                                                    children: \"Messages Processed\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                    lineNumber: 794,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white\",\n                                                    children: (_stats_whatsappBot3 = stats.whatsappBot) === null || _stats_whatsappBot3 === void 0 ? void 0 : _stats_whatsappBot3.messagesProcessed\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                    lineNumber: 795,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                            lineNumber: 793,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                    lineNumber: 784,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                            lineNumber: 777,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-white\",\n                                            children: \"Telegram Bot\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                            lineNumber: 802,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-3 h-3 rounded-full \".concat(((_stats_telegramBot = stats.telegramBot) === null || _stats_telegramBot === void 0 ? void 0 : _stats_telegramBot.status) === \"online\" ? \"bg-green-400\" : \"bg-red-400\", \" animate-pulse\")\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                            lineNumber: 803,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                    lineNumber: 801,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-400\",\n                                                    children: \"Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                    lineNumber: 809,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-400\",\n                                                    children: (_stats_telegramBot1 = stats.telegramBot) === null || _stats_telegramBot1 === void 0 ? void 0 : _stats_telegramBot1.status\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                    lineNumber: 810,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                            lineNumber: 808,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-400\",\n                                                    children: \"Connected Users\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                    lineNumber: 813,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white\",\n                                                    children: (_stats_telegramBot2 = stats.telegramBot) === null || _stats_telegramBot2 === void 0 ? void 0 : _stats_telegramBot2.connectedUsers\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                    lineNumber: 814,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                            lineNumber: 812,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-400\",\n                                                    children: \"Messages Processed\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                    lineNumber: 817,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white\",\n                                                    children: (_stats_telegramBot3 = stats.telegramBot) === null || _stats_telegramBot3 === void 0 ? void 0 : _stats_telegramBot3.messagesProcessed\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                    lineNumber: 818,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                            lineNumber: 816,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                    lineNumber: 807,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                            lineNumber: 800,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                    lineNumber: 776,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n            lineNumber: 773,\n            columnNumber: 5\n        }, this);\n    };\n    const renderWhatsApp = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-2xl font-bold text-white\",\n                    children: \"WhatsApp Bot Management\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                    lineNumber: 828,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card-cyber\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-white mb-4\",\n                            children: \"Bot Status\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                            lineNumber: 831,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-300\",\n                                                    children: \"Connection Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                    lineNumber: 835,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"px-3 py-1 rounded-full text-sm font-medium \".concat(stats.status === \"connected\" ? \"bg-green-500/20 text-green-400\" : \"bg-red-500/20 text-red-400\"),\n                                                    children: stats.status\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                    lineNumber: 836,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                            lineNumber: 834,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-300\",\n                                                    children: \"Connected Users\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                    lineNumber: 843,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white\",\n                                                    children: stats.connectedUsers\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                    lineNumber: 844,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                            lineNumber: 842,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-300\",\n                                                    children: \"Messages Processed\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                    lineNumber: 847,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white\",\n                                                    children: stats.messagesProcessed\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                    lineNumber: 848,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                            lineNumber: 846,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                    lineNumber: 833,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-white font-medium\",\n                                            children: \"Enabled Features\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                            lineNumber: 852,\n                                            columnNumber: 13\n                                        }, this),\n                                        stats.features && Object.entries(stats.features).map((param)=>{\n                                            let [feature, enabled] = param;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-300 capitalize\",\n                                                        children: feature.replace(/([A-Z])/g, \" $1\")\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                        lineNumber: 855,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-3 h-3 rounded-full \".concat(enabled ? \"bg-green-400\" : \"bg-gray-600\")\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                        lineNumber: 856,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, feature, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                lineNumber: 854,\n                                                columnNumber: 15\n                                            }, this);\n                                        })\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                    lineNumber: 851,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                            lineNumber: 832,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                    lineNumber: 830,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n            lineNumber: 827,\n            columnNumber: 5\n        }, this);\n    const renderTelegram = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-2xl font-bold text-white\",\n                    children: \"Telegram Bot Management\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                    lineNumber: 867,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card-cyber\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-white mb-4\",\n                            children: \"Bot Status\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                            lineNumber: 870,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-300\",\n                                                    children: \"Connection Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                    lineNumber: 874,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"px-3 py-1 rounded-full text-sm font-medium \".concat(stats.status === \"connected\" ? \"bg-green-500/20 text-green-400\" : \"bg-red-500/20 text-red-400\"),\n                                                    children: stats.status\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                    lineNumber: 875,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                            lineNumber: 873,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-300\",\n                                                    children: \"Bot Token\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                    lineNumber: 882,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-400\",\n                                                    children: stats.botToken\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                    lineNumber: 883,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                            lineNumber: 881,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-300\",\n                                                    children: \"Connected Users\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                    lineNumber: 886,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white\",\n                                                    children: stats.connectedUsers\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                    lineNumber: 887,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                            lineNumber: 885,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                    lineNumber: 872,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-white font-medium\",\n                                            children: \"Enabled Features\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                            lineNumber: 891,\n                                            columnNumber: 13\n                                        }, this),\n                                        stats.features && Object.entries(stats.features).map((param)=>{\n                                            let [feature, enabled] = param;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-300 capitalize\",\n                                                        children: feature.replace(/([A-Z])/g, \" $1\")\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                        lineNumber: 894,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-3 h-3 rounded-full \".concat(enabled ? \"bg-green-400\" : \"bg-gray-600\")\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                        lineNumber: 895,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, feature, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                lineNumber: 893,\n                                                columnNumber: 15\n                                            }, this);\n                                        })\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                    lineNumber: 890,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                            lineNumber: 871,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                    lineNumber: 869,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n            lineNumber: 866,\n            columnNumber: 5\n        }, this);\n    const renderSystemConfig = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-2xl font-bold text-white\",\n                    children: \"System Configuration\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                    lineNumber: 906,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-white mb-4\",\n                                    children: \"System Info\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                    lineNumber: 910,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-400\",\n                                                    children: \"Environment\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                    lineNumber: 913,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-cyber-primary\",\n                                                    children: stats.environment\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                    lineNumber: 914,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                            lineNumber: 912,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-400\",\n                                                    children: \"Version\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                    lineNumber: 917,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white\",\n                                                    children: stats.version\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                    lineNumber: 918,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                            lineNumber: 916,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-400\",\n                                                    children: \"Database\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                    lineNumber: 921,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-400\",\n                                                    children: stats.database\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                    lineNumber: 922,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                            lineNumber: 920,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-400\",\n                                                    children: \"Redis\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                    lineNumber: 925,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-400\",\n                                                    children: stats.redis\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                    lineNumber: 926,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                            lineNumber: 924,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                    lineNumber: 911,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                            lineNumber: 909,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-white mb-4\",\n                                    children: \"Security Settings\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                    lineNumber: 932,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: stats.securitySettings && Object.entries(stats.securitySettings).map((param)=>{\n                                        let [key, value] = param;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-400 capitalize\",\n                                                    children: key.replace(/([A-Z])/g, \" $1\")\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                    lineNumber: 936,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white\",\n                                                    children: String(value)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                    lineNumber: 937,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, key, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                            lineNumber: 935,\n                                            columnNumber: 15\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                    lineNumber: 933,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                            lineNumber: 931,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                    lineNumber: 908,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n            lineNumber: 905,\n            columnNumber: 5\n        }, this);\n    const renderSecurity = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-2xl font-bold text-white\",\n                    children: \"Security Center\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                    lineNumber: 948,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400 text-sm\",\n                                                children: \"Active Threats\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                lineNumber: 954,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-red-400\",\n                                                children: stats.activeThreats\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                lineNumber: 955,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                        lineNumber: 953,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Ban_CheckCircle_Clock_CreditCard_Crown_DollarSign_Edit_Eye_Lock_Search_Shield_UserPlus_Users_Webhook_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-8 w-8 text-red-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                        lineNumber: 957,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                lineNumber: 952,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                            lineNumber: 951,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400 text-sm\",\n                                                children: \"Blocked IPs\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                lineNumber: 963,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: stats.blockedIPs\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                lineNumber: 964,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                        lineNumber: 962,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Ban_CheckCircle_Clock_CreditCard_Crown_DollarSign_Edit_Eye_Lock_Search_Shield_UserPlus_Users_Webhook_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-8 w-8 text-cyber-primary\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                        lineNumber: 966,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                lineNumber: 961,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                            lineNumber: 960,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400 text-sm\",\n                                                children: \"Suspicious Activity\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                lineNumber: 972,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-yellow-400\",\n                                                children: stats.suspiciousActivity\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                lineNumber: 973,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                        lineNumber: 971,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Ban_CheckCircle_Clock_CreditCard_Crown_DollarSign_Edit_Eye_Lock_Search_Shield_UserPlus_Users_Webhook_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-8 w-8 text-yellow-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                        lineNumber: 975,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                lineNumber: 970,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                            lineNumber: 969,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400 text-sm\",\n                                                children: \"Failed Logins\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                lineNumber: 981,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-orange-400\",\n                                                children: stats.failedLogins\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                lineNumber: 982,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                        lineNumber: 980,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Ban_CheckCircle_Clock_CreditCard_Crown_DollarSign_Edit_Eye_Lock_Search_Shield_UserPlus_Users_Webhook_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-8 w-8 text-orange-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                        lineNumber: 984,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                lineNumber: 979,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                            lineNumber: 978,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                    lineNumber: 950,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n            lineNumber: 947,\n            columnNumber: 5\n        }, this);\n    const renderDatabase = ()=>{\n        var _stats_queries;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-2xl font-bold text-white\",\n                    children: \"Database Administration\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                    lineNumber: 993,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-white mb-4\",\n                                    children: \"Database Status\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                    lineNumber: 997,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-400\",\n                                                    children: \"Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                    lineNumber: 1000,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-400\",\n                                                    children: stats.status\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                    lineNumber: 1001,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                            lineNumber: 999,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-400\",\n                                                    children: \"Size\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                    lineNumber: 1004,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white\",\n                                                    children: stats.size\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                    lineNumber: 1005,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                            lineNumber: 1003,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-400\",\n                                                    children: \"Tables\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                    lineNumber: 1008,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white\",\n                                                    children: stats.tables\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                    lineNumber: 1009,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                            lineNumber: 1007,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-400\",\n                                                    children: \"Connections\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                    lineNumber: 1012,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white\",\n                                                    children: stats.connections\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                    lineNumber: 1013,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                            lineNumber: 1011,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                    lineNumber: 998,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                            lineNumber: 996,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-white mb-4\",\n                                    children: \"Performance\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                    lineNumber: 1019,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-400\",\n                                                    children: \"Total Queries\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                    lineNumber: 1022,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white\",\n                                                    children: (_stats_queries = stats.queries) === null || _stats_queries === void 0 ? void 0 : _stats_queries.toLocaleString()\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                    lineNumber: 1023,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                            lineNumber: 1021,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-400\",\n                                                    children: \"Slow Queries\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                    lineNumber: 1026,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-yellow-400\",\n                                                    children: stats.slowQueries\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                    lineNumber: 1027,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                            lineNumber: 1025,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                    lineNumber: 1020,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                            lineNumber: 1018,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-white mb-4\",\n                                    children: \"Backup\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                    lineNumber: 1033,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-400\",\n                                                    children: \"Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                    lineNumber: 1036,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-400\",\n                                                    children: stats.backupStatus\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                    lineNumber: 1037,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                            lineNumber: 1035,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-400\",\n                                                    children: \"Last Backup\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                    lineNumber: 1040,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white\",\n                                                    children: stats.lastBackup && new Date(stats.lastBackup).toLocaleDateString()\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                    lineNumber: 1041,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                            lineNumber: 1039,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                    lineNumber: 1034,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                            lineNumber: 1032,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                    lineNumber: 995,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n            lineNumber: 992,\n            columnNumber: 5\n        }, this);\n    };\n    const renderServer = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-2xl font-bold text-white\",\n                    children: \"Server Management\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                    lineNumber: 1051,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-white mb-4\",\n                                    children: \"Server Status\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                    lineNumber: 1055,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-400\",\n                                                    children: \"Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                    lineNumber: 1058,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-400\",\n                                                    children: stats.status\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                    lineNumber: 1059,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                            lineNumber: 1057,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-400\",\n                                                    children: \"Uptime\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                    lineNumber: 1062,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white\",\n                                                    children: stats.uptime\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                    lineNumber: 1063,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                            lineNumber: 1061,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-400\",\n                                                    children: \"Processes\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                    lineNumber: 1066,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white\",\n                                                    children: stats.processes\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                    lineNumber: 1067,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                            lineNumber: 1065,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                    lineNumber: 1056,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                            lineNumber: 1054,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-white mb-4\",\n                                    children: \"Services\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                    lineNumber: 1073,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: stats.services && Object.entries(stats.services).map((param)=>{\n                                        let [service, status] = param;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-400 capitalize\",\n                                                    children: service\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                    lineNumber: 1077,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"\".concat(status === \"running\" ? \"text-green-400\" : \"text-red-400\"),\n                                                    children: String(status)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                    lineNumber: 1078,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, service, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                            lineNumber: 1076,\n                                            columnNumber: 15\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                    lineNumber: 1074,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                            lineNumber: 1072,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                    lineNumber: 1053,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n            lineNumber: 1050,\n            columnNumber: 5\n        }, this);\n    const renderAPI = ()=>{\n        var _stats_requestsToday;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-2xl font-bold text-white\",\n                    children: \"API Management\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                    lineNumber: 1091,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400 text-sm\",\n                                                children: \"Total Endpoints\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                lineNumber: 1097,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: stats.totalEndpoints\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                lineNumber: 1098,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                        lineNumber: 1096,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Ban_CheckCircle_Clock_CreditCard_Crown_DollarSign_Edit_Eye_Lock_Search_Shield_UserPlus_Users_Webhook_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-8 w-8 text-purple-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                        lineNumber: 1100,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                lineNumber: 1095,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                            lineNumber: 1094,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400 text-sm\",\n                                                children: \"Requests Today\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                lineNumber: 1106,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: (_stats_requestsToday = stats.requestsToday) === null || _stats_requestsToday === void 0 ? void 0 : _stats_requestsToday.toLocaleString()\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                lineNumber: 1107,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                        lineNumber: 1105,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Ban_CheckCircle_Clock_CreditCard_Crown_DollarSign_Edit_Eye_Lock_Search_Shield_UserPlus_Users_Webhook_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"h-8 w-8 text-green-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                        lineNumber: 1109,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                lineNumber: 1104,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                            lineNumber: 1103,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400 text-sm\",\n                                                children: \"Avg Response Time\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                lineNumber: 1115,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: stats.averageResponseTime\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                lineNumber: 1116,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                        lineNumber: 1114,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Ban_CheckCircle_Clock_CreditCard_Crown_DollarSign_Edit_Eye_Lock_Search_Shield_UserPlus_Users_Webhook_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-8 w-8 text-blue-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                        lineNumber: 1118,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                lineNumber: 1113,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                            lineNumber: 1112,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400 text-sm\",\n                                                children: \"Error Rate\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                lineNumber: 1124,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: stats.errorRate\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                                lineNumber: 1125,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                        lineNumber: 1123,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Ban_CheckCircle_Clock_CreditCard_Crown_DollarSign_Edit_Eye_Lock_Search_Shield_UserPlus_Users_Webhook_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-8 w-8 text-yellow-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                        lineNumber: 1127,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                                lineNumber: 1122,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                            lineNumber: 1121,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n                    lineNumber: 1093,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n            lineNumber: 1090,\n            columnNumber: 5\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8\",\n        children: renderSection()\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\dashboard\\\\AdminSections.tsx\",\n        lineNumber: 1135,\n        columnNumber: 5\n    }, this);\n}\n_s(AdminSections, \"9IvCJdkaghwGz8Bz85RSGRTMvmQ=\");\n_c = AdminSections;\nvar _c;\n$RefreshReg$(_c, \"AdminSections\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/dashboard/AdminSections.tsx\n"));

/***/ })

});