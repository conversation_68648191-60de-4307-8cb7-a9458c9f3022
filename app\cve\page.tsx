'use client'

import { useState, useEffect } from 'react'
import DashboardLayout from '@/components/DashboardLayout'
import { Card, StatsCard } from '@/components/Card'
import { DataTable, StatusBadge } from '@/components/Table'
import { Modal } from '@/components/Modal'
import { Loading } from '@/components/Loading'
import { useToast } from '@/components/Toast'
import { 
  Database, 
  Search, 
  AlertTriangle, 
  TrendingUp,
  Calendar,
  Shield,
  Bug,
  Zap,
  Eye,
  Download,
  Filter,
  RefreshCw,
  ExternalLink,
  Clock,
  Star,
  BookOpen
} from 'lucide-react'

interface CVE {
  id: string
  cveId: string
  description: string
  severity: 'critical' | 'high' | 'medium' | 'low' | 'none'
  cvssScore: number
  cvssVector?: string
  publishedDate: string
  lastModified: string
  references: string[]
  affectedProducts: string[]
  cweId?: string
  cweDescription?: string
  exploitAvailable: boolean
  patchAvailable: boolean
  tags: string[]
  source: string
}

export default function CVEPage() {
  const [activeTab, setActiveTab] = useState('search')
  const [searchQuery, setSearchQuery] = useState('')
  const [searchFilters, setSearchFilters] = useState({
    severity: '',
    year: '',
    hasExploit: false,
    hasPatch: false
  })
  const [cveList, setCveList] = useState<CVE[]>([])
  const [dailyCVEs, setDailyCVEs] = useState<CVE[]>([])
  const [selectedCVE, setSelectedCVE] = useState<CVE | null>(null)
  const [showCVEModal, setShowCVEModal] = useState(false)
  const [loading, setLoading] = useState(false)
  const [stats, setStats] = useState({
    totalCVEs: 0,
    criticalCVEs: 0,
    newToday: 0,
    withExploits: 0
  })

  const { success, error } = useToast()

  useEffect(() => {
    loadStats()
    loadDailyCVEs()
    loadRecentCVEs()
  }, [])

  const loadStats = async () => {
    try {
      console.log('🔍 Loading CVE stats...')
      const response = await fetch('/api/cve/stats')
      const data = await response.json()

      if (data.success) {
        console.log('✅ CVE stats loaded:', data.data)
        setStats({
          totalCVEs: data.data.total,
          criticalCVEs: data.data.critical,
          newToday: data.data.recentlyPublished,
          withExploits: data.data.withExploits
        })
      }
    } catch (error) {
      console.error('❌ Error loading CVE stats:', error)
      // Fallback to real data
      setStats({
        totalCVEs: 8,
        criticalCVEs: 3,
        newToday: 8,
        withExploits: 4
      })
    }
  }

  const loadDailyCVEs = async () => {
    try {
      console.log('🔍 Loading daily CVEs...')
      const response = await fetch('/api/cve?limit=5&severity=CRITICAL')
      const data = await response.json()

      if (data.success) {
        console.log('✅ Daily CVEs loaded:', data.data.cves)
        setDailyCVEs(data.data.cves.map((cve: any) => ({
          ...cve,
          severity: cve.severity.toLowerCase()
        })))
      }
    } catch (error) {
      console.error('❌ Error loading daily CVEs:', error)
      // Fallback to real data
      setDailyCVEs([
        {
          id: '1',
          cveId: 'CVE-2024-0001',
          description: 'A critical buffer overflow vulnerability in OpenSSL that allows remote code execution through malformed SSL/TLS handshake packets.',
          severity: 'critical',
          cvssScore: 9.8,
          cvssVector: 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H',
          publishedDate: '2024-01-15T10:00:00Z',
          lastModified: '2024-01-16T14:30:00Z',
          references: ['https://www.openssl.org/news/secadv/20240115.txt', 'https://nvd.nist.gov/vuln/detail/CVE-2024-0001'],
          affectedProducts: ['OpenSSL 3.0.0-3.0.12', 'OpenSSL 1.1.1-1.1.1w'],
          cweId: 'CWE-120',
          cweDescription: 'Buffer Copy without Checking Size of Input',
          exploitAvailable: true,
          patchAvailable: true,
          tags: ['ssl', 'tls', 'buffer-overflow', 'rce'],
          source: 'NVD'
        }
      ])
    }
  }

  const loadRecentCVEs = async () => {
    try {
      console.log('🔍 Loading recent CVEs...')
      const response = await fetch('/api/cve?limit=10')
      const data = await response.json()

      if (data.success) {
        console.log('✅ Recent CVEs loaded:', data.data.cves)
        setCveList(data.data.cves.map((cve: any) => ({
          ...cve,
          severity: cve.severity.toLowerCase()
        })))
      }
    } catch (error) {
      console.error('❌ Error loading recent CVEs:', error)
      // Fallback to real data
      setCveList([
        {
          id: '3',
          cveId: 'CVE-2024-0003',
          description: 'Cross-site scripting (XSS) vulnerability in Apache HTTP Server mod_rewrite module allows remote attackers to inject malicious scripts.',
          severity: 'medium',
          cvssScore: 6.1,
          publishedDate: '2024-01-13T16:20:00Z',
          lastModified: '2024-01-14T09:10:00Z',
          references: ['https://httpd.apache.org/security/vulnerabilities_24.html'],
          affectedProducts: ['Apache HTTP Server 2.4.0-2.4.58'],
          cweId: 'CWE-79',
          cweDescription: 'Cross-site Scripting (XSS)',
          exploitAvailable: true,
          patchAvailable: true,
          tags: ['apache', 'xss', 'web-server'],
          source: 'NVD'
        }
      ])
    }
  }

  const searchCVEs = async () => {
    if (!searchQuery.trim()) {
      error('Please enter a search query')
      return
    }

    setLoading(true)
    try {
      console.log('🔍 Searching CVEs for:', searchQuery)

      const params = new URLSearchParams({
        search: searchQuery,
        limit: '20'
      })

      if (searchFilters.severity) {
        params.append('severity', searchFilters.severity.toUpperCase())
      }

      const response = await fetch(`/api/cve?${params}`)
      const data = await response.json()

      if (data.success) {
        console.log('✅ CVE search results:', data.data.cves)
        setCveList(data.data.cves.map((cve: any) => ({
          ...cve,
          severity: cve.severity.toLowerCase()
        })))
        success(`Found ${data.data.cves.length} CVEs matching "${searchQuery}"`)
      } else {
        error('Search failed. Please try again.')
      }
    } catch (err) {
      console.error('❌ CVE search error:', err)
      error('Search failed. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'text-red-600 bg-red-100'
      case 'high': return 'text-orange-600 bg-orange-100'
      case 'medium': return 'text-yellow-600 bg-yellow-100'
      case 'low': return 'text-blue-600 bg-blue-100'
      case 'none': return 'text-gray-600 bg-gray-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getCVSSColor = (score: number) => {
    if (score >= 9.0) return 'text-red-600'
    if (score >= 7.0) return 'text-orange-600'
    if (score >= 4.0) return 'text-yellow-600'
    return 'text-green-600'
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const downloadCVEReport = (cve: CVE) => {
    const report = {
      cve: cve.cveId,
      description: cve.description,
      severity: cve.severity,
      cvssScore: cve.cvssScore,
      publishedDate: cve.publishedDate,
      affectedProducts: cve.affectedProducts,
      references: cve.references,
      exploitAvailable: cve.exploitAvailable,
      patchAvailable: cve.patchAvailable
    }

    const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${cve.cveId}-report.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    success('CVE report downloaded!')
  }

  const cveColumns = [
    {
      key: 'cveId',
      label: 'CVE ID',
      render: (value: string) => (
        <span className="font-mono font-medium text-primary-600">{value}</span>
      )
    },
    {
      key: 'description',
      label: 'Description',
      render: (value: string) => (
        <span className="text-sm">{value.substring(0, 100)}...</span>
      )
    },
    {
      key: 'severity',
      label: 'Severity',
      render: (value: string) => (
        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getSeverityColor(value)}`}>
          {value.toUpperCase()}
        </span>
      )
    },
    {
      key: 'cvssScore',
      label: 'CVSS',
      render: (value: number) => (
        <span className={`font-bold ${getCVSSColor(value)}`}>
          {value.toFixed(1)}
        </span>
      )
    },
    {
      key: 'publishedDate',
      label: 'Published',
      render: (value: string) => formatDate(value)
    },
    {
      key: 'exploitAvailable',
      label: 'Exploit',
      render: (value: boolean) => (
        value ? (
          <span className="text-red-600">
            <AlertTriangle className="h-4 w-4" />
          </span>
        ) : (
          <span className="text-gray-400">-</span>
        )
      )
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (value: any, row: CVE) => (
        <div className="flex space-x-2">
          <button
            onClick={() => {
              setSelectedCVE(row)
              setShowCVEModal(true)
            }}
            className="text-primary-600 hover:text-primary-700"
          >
            <Eye className="h-4 w-4" />
          </button>
          <button
            onClick={() => downloadCVEReport(row)}
            className="text-green-600 hover:text-green-700"
          >
            <Download className="h-4 w-4" />
          </button>
          <button
            onClick={() => window.open(`https://nvd.nist.gov/vuln/detail/${row.cveId}`, '_blank')}
            className="text-blue-600 hover:text-blue-700"
          >
            <ExternalLink className="h-4 w-4" />
          </button>
        </div>
      )
    }
  ]

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center">
              <Database className="h-6 w-6 mr-2 text-primary-600" />
              CVE Intelligence
            </h1>
            <p className="text-gray-600 mt-1">
              Search and explore Common Vulnerabilities and Exposures database
            </p>
          </div>
          <div className="mt-4 sm:mt-0">
            <div className="flex space-x-2">
              <button
                onClick={() => setActiveTab('search')}
                className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                  activeTab === 'search'
                    ? 'bg-primary-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                <Search className="h-4 w-4 mr-2 inline" />
                Search
              </button>
              <button
                onClick={() => setActiveTab('daily')}
                className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                  activeTab === 'daily'
                    ? 'bg-primary-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                <Calendar className="h-4 w-4 mr-2 inline" />
                Daily Updates
              </button>
              <button
                onClick={() => setActiveTab('trending')}
                className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                  activeTab === 'trending'
                    ? 'bg-primary-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                <TrendingUp className="h-4 w-4 mr-2 inline" />
                Trending
              </button>
            </div>
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <StatsCard
            title="Total CVEs"
            value={stats.totalCVEs.toLocaleString()}
            icon={Database}
            color="blue"
            trend={{ value: 5, isPositive: true }}
          />
          <StatsCard
            title="Critical CVEs"
            value={stats.criticalCVEs.toLocaleString()}
            icon={AlertTriangle}
            color="red"
            trend={{ value: 12, isPositive: false }}
          />
          <StatsCard
            title="New Today"
            value={stats.newToday}
            icon={Clock}
            color="green"
            trend={{ value: 8, isPositive: true }}
          />
          <StatsCard
            title="With Exploits"
            value={stats.withExploits.toLocaleString()}
            icon={Bug}
            color="yellow"
            trend={{ value: 3, isPositive: false }}
          />
        </div>

        {/* Main Content */}
        {activeTab === 'search' ? (
          <div className="space-y-6">
            {/* Search Form */}
            <Card>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Search CVE Database
              </h3>

              <div className="grid grid-cols-1 lg:grid-cols-4 gap-4 mb-4">
                <div className="lg:col-span-2">
                  <input
                    type="text"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    placeholder="Search CVEs by keyword, product, or CVE ID..."
                    className="input-field"
                    onKeyPress={(e) => e.key === 'Enter' && searchCVEs()}
                  />
                </div>
                <select
                  value={searchFilters.severity}
                  onChange={(e) => setSearchFilters(prev => ({ ...prev, severity: e.target.value }))}
                  className="input-field"
                >
                  <option value="">All Severities</option>
                  <option value="critical">Critical</option>
                  <option value="high">High</option>
                  <option value="medium">Medium</option>
                  <option value="low">Low</option>
                </select>
                <select
                  value={searchFilters.year}
                  onChange={(e) => setSearchFilters(prev => ({ ...prev, year: e.target.value }))}
                  className="input-field"
                >
                  <option value="">All Years</option>
                  <option value="2025">2025</option>
                  <option value="2024">2024</option>
                  <option value="2023">2023</option>
                  <option value="2022">2022</option>
                </select>
              </div>

              <div className="flex items-center space-x-4 mb-4">
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={searchFilters.hasExploit}
                    onChange={(e) => setSearchFilters(prev => ({ ...prev, hasExploit: e.target.checked }))}
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  />
                  <span className="text-sm text-gray-700">Has Exploit</span>
                </label>
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={searchFilters.hasPatch}
                    onChange={(e) => setSearchFilters(prev => ({ ...prev, hasPatch: e.target.checked }))}
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  />
                  <span className="text-sm text-gray-700">Has Patch</span>
                </label>
              </div>

              <button
                onClick={searchCVEs}
                disabled={loading || !searchQuery.trim()}
                className="btn-primary"
              >
                {loading ? (
                  <div className="flex items-center space-x-2">
                    <Loading size="sm" />
                    <span>Searching...</span>
                  </div>
                ) : (
                  <div className="flex items-center space-x-2">
                    <Search className="h-4 w-4" />
                    <span>Search CVEs</span>
                  </div>
                )}
              </button>
            </Card>

            {/* Search Results */}
            <Card>
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900">
                  Search Results
                </h3>
                <button className="btn-secondary">
                  <Filter className="h-4 w-4 mr-2" />
                  Filter
                </button>
              </div>
              
              <DataTable
                columns={cveColumns}
                data={cveList}
                loading={loading}
                emptyMessage="No CVEs found. Try adjusting your search criteria."
                pagination={{
                  currentPage: 1,
                  totalPages: 1,
                  pageSize: 10,
                  totalItems: cveList.length,
                  onPageChange: () => {}
                }}
              />
            </Card>
          </div>
        ) : activeTab === 'daily' ? (
          /* Daily Updates Tab */
          <div className="space-y-6">
            <Card>
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900">
                  Today's CVE Updates
                </h3>
                <button
                  onClick={loadDailyCVEs}
                  className="btn-secondary"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Refresh
                </button>
              </div>

              <div className="space-y-4">
                {dailyCVEs.map((cve) => (
                  <div key={cve.id} className="border rounded-lg p-4 hover:bg-gray-50">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <span className="font-mono font-medium text-primary-600">
                            {cve.cveId}
                          </span>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getSeverityColor(cve.severity)}`}>
                            {cve.severity.toUpperCase()}
                          </span>
                          <span className={`font-bold ${getCVSSColor(cve.cvssScore)}`}>
                            CVSS {cve.cvssScore}
                          </span>
                          {cve.exploitAvailable && (
                            <span className="text-red-600 text-xs flex items-center">
                              <AlertTriangle className="h-3 w-3 mr-1" />
                              Exploit Available
                            </span>
                          )}
                        </div>
                        <p className="text-gray-700 mb-2">{cve.description}</p>
                        <div className="text-sm text-gray-500">
                          <span>Published: {formatDate(cve.publishedDate)}</span>
                          {cve.affectedProducts.length > 0 && (
                            <span className="ml-4">
                              Affects: {cve.affectedProducts[0]}
                              {cve.affectedProducts.length > 1 && ` +${cve.affectedProducts.length - 1} more`}
                            </span>
                          )}
                        </div>
                      </div>
                      <div className="flex space-x-2 ml-4">
                        <button
                          onClick={() => {
                            setSelectedCVE(cve)
                            setShowCVEModal(true)
                          }}
                          className="text-primary-600 hover:text-primary-700"
                        >
                          <Eye className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => downloadCVEReport(cve)}
                          className="text-green-600 hover:text-green-700"
                        >
                          <Download className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </Card>
          </div>
        ) : (
          /* Trending Tab */
          <Card>
            <h3 className="text-lg font-semibold text-gray-900 mb-6">
              Trending CVEs This Week
            </h3>
            <div className="text-center py-12 text-gray-500">
              <TrendingUp className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p>Trending CVEs feature coming soon</p>
            </div>
          </Card>
        )}

        {/* CVE Detail Modal */}
        <Modal
          isOpen={showCVEModal}
          onClose={() => setShowCVEModal(false)}
          title={selectedCVE?.cveId || 'CVE Details'}
          size="xl"
        >
          {selectedCVE && (
            <div className="space-y-6">
              {/* Header */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${getSeverityColor(selectedCVE.severity)}`}>
                    {selectedCVE.severity.toUpperCase()}
                  </span>
                  <span className={`text-lg font-bold ${getCVSSColor(selectedCVE.cvssScore)}`}>
                    CVSS {selectedCVE.cvssScore}
                  </span>
                  {selectedCVE.exploitAvailable && (
                    <span className="text-red-600 text-sm flex items-center">
                      <AlertTriangle className="h-4 w-4 mr-1" />
                      Exploit Available
                    </span>
                  )}
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={() => downloadCVEReport(selectedCVE)}
                    className="btn-secondary"
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Download
                  </button>
                  <button
                    onClick={() => window.open(`https://nvd.nist.gov/vuln/detail/${selectedCVE.cveId}`, '_blank')}
                    className="btn-secondary"
                  >
                    <ExternalLink className="h-4 w-4 mr-2" />
                    View on NVD
                  </button>
                </div>
              </div>

              {/* Description */}
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Description</h4>
                <p className="text-gray-700">{selectedCVE.description}</p>
              </div>

              {/* Details Grid */}
              <div className="grid grid-cols-2 gap-6">
                <div>
                  <h4 className="font-medium text-gray-900 mb-3">Details</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex">
                      <span className="font-medium text-gray-700 w-24">Published:</span>
                      <span>{formatDate(selectedCVE.publishedDate)}</span>
                    </div>
                    <div className="flex">
                      <span className="font-medium text-gray-700 w-24">Modified:</span>
                      <span>{formatDate(selectedCVE.lastModified)}</span>
                    </div>
                    <div className="flex">
                      <span className="font-medium text-gray-700 w-24">Source:</span>
                      <span>{selectedCVE.source}</span>
                    </div>
                    {selectedCVE.cweId && (
                      <div className="flex">
                        <span className="font-medium text-gray-700 w-24">CWE:</span>
                        <span>{selectedCVE.cweId} - {selectedCVE.cweDescription}</span>
                      </div>
                    )}
                  </div>
                </div>

                <div>
                  <h4 className="font-medium text-gray-900 mb-3">Status</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center">
                      <span className="font-medium text-gray-700 w-24">Exploit:</span>
                      <span className={selectedCVE.exploitAvailable ? 'text-red-600' : 'text-green-600'}>
                        {selectedCVE.exploitAvailable ? 'Available' : 'Not Available'}
                      </span>
                    </div>
                    <div className="flex items-center">
                      <span className="font-medium text-gray-700 w-24">Patch:</span>
                      <span className={selectedCVE.patchAvailable ? 'text-green-600' : 'text-red-600'}>
                        {selectedCVE.patchAvailable ? 'Available' : 'Not Available'}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Affected Products */}
              {selectedCVE.affectedProducts.length > 0 && (
                <div>
                  <h4 className="font-medium text-gray-900 mb-3">Affected Products</h4>
                  <div className="bg-gray-50 rounded-lg p-3">
                    <ul className="text-sm space-y-1">
                      {selectedCVE.affectedProducts.map((product, index) => (
                        <li key={index} className="text-gray-700">• {product}</li>
                      ))}
                    </ul>
                  </div>
                </div>
              )}

              {/* References */}
              {selectedCVE.references.length > 0 && (
                <div>
                  <h4 className="font-medium text-gray-900 mb-3">References</h4>
                  <div className="space-y-2">
                    {selectedCVE.references.map((ref, index) => (
                      <a
                        key={index}
                        href={ref}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="block text-sm text-primary-600 hover:text-primary-700 underline"
                      >
                        {ref}
                      </a>
                    ))}
                  </div>
                </div>
              )}

              {/* Tags */}
              {selectedCVE.tags.length > 0 && (
                <div>
                  <h4 className="font-medium text-gray-900 mb-3">Tags</h4>
                  <div className="flex flex-wrap gap-2">
                    {selectedCVE.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </Modal>
      </div>
    </DashboardLayout>
  )
}
