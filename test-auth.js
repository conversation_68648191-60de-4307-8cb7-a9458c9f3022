// Simple test script for authentication
const testLogin = async () => {
  try {
    console.log('Testing login API...')
    
    const response = await fetch('http://localhost:3000/api/auth/test-login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'admin123'
      }),
    })

    const data = await response.json()
    
    console.log('Response status:', response.status)
    console.log('Response data:', JSON.stringify(data, null, 2))
    
    if (response.ok && data.success) {
      console.log('✅ Login successful!')
      console.log('Access Token:', data.data.tokens.accessToken.substring(0, 50) + '...')
      return data.data.tokens.accessToken
    } else {
      console.log('❌ Login failed:', data.error)
      return null
    }
  } catch (error) {
    console.error('❌ Network error:', error.message)
    return null
  }
}

const testRegister = async () => {
  try {
    console.log('\nTesting register API...')
    
    const response = await fetch('http://localhost:3001/api/auth/register', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        username: 'testuser' + Date.now(),
        email: 'test' + Date.now() + '@example.com',
        fullName: 'Test User',
        password: 'test123'
      }),
    })

    const data = await response.json()
    
    console.log('Response status:', response.status)
    console.log('Response data:', JSON.stringify(data, null, 2))
    
    if (response.ok && data.success) {
      console.log('✅ Registration successful!')
      return data.data.tokens.accessToken
    } else {
      console.log('❌ Registration failed:', data.error)
      return null
    }
  } catch (error) {
    console.error('❌ Network error:', error.message)
    return null
  }
}

const runTests = async () => {
  console.log('🚀 Starting authentication tests...\n')
  
  // Test login
  const loginToken = await testLogin()
  
  // Test register
  const registerToken = await testRegister()
  
  console.log('\n📊 Test Results:')
  console.log('Login:', loginToken ? '✅ PASS' : '❌ FAIL')
  console.log('Register:', registerToken ? '✅ PASS' : '❌ FAIL')
}

// Run tests
runTests().catch(console.error)
