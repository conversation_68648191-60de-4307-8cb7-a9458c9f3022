'use client'

import { useState, useEffect, useContext, createContext, ReactNode } from 'react'
import { useRouter } from 'next/navigation'

export interface User {
  id: number
  username: string
  email: string
  fullName?: string
  role: 'user' | 'admin' | 'super_admin' | 'moderator'
  plan: 'Free' | 'Student' | 'Hobby' | 'Bughunter' | 'Cybersecurity' | 'Pro' | 'Expert' | 'Elite'
  level: number
  score: number
  streak: number
  emailVerified: boolean
  lastActive: string
  createdAt: string
}

export interface AuthTokens {
  accessToken: string
  refreshToken: string
  expiresIn: number
}

interface AuthContextType {
  user: User | null
  tokens: AuthTokens | null
  isLoading: boolean
  isAuthenticated: boolean
  login: (email: string, password: string, rememberMe?: boolean) => Promise<{ success: boolean; error?: string }>
  register: (userData: { username: string; email: string; password: string; fullName: string }) => Promise<{ success: boolean; error?: string; details?: any }>
  logout: () => Promise<void>
  refreshToken: () => Promise<boolean>
  updateUser: (userData: Partial<User>) => void
  getToken: () => string | null
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [tokens, setTokens] = useState<AuthTokens | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const router = useRouter()

  const isAuthenticated = !!user && !!tokens

  // Verify token validity
  const verifyToken = async (token: string): Promise<boolean> => {
    try {
      const response = await fetch('/api/auth/verify', {
        headers: { Authorization: `Bearer ${token}` }
      })
      return response.ok
    } catch {
      return false
    }
  }

  // Initialize auth state from localStorage
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        if (typeof window !== 'undefined') {
          const storedUser = localStorage.getItem('user')
          const storedToken = localStorage.getItem('token')
          const storedRefreshToken = localStorage.getItem('refreshToken')
          
          if (storedUser && storedToken && storedRefreshToken) {
            const userData = JSON.parse(storedUser)
            setUser(userData)
            setTokens({
              accessToken: storedToken,
              refreshToken: storedRefreshToken,
              expiresIn: 7 * 24 * 60 * 60 // 7 days
            })
            
            // Verify token is still valid
            const isValid = await verifyToken(storedToken)
            if (!isValid) {
              await logout()
            }
          }
        }
      } catch (error) {
        console.error('Error initializing auth:', error)
        await logout()
      } finally {
        setIsLoading(false)
      }
    }

    initializeAuth()
  }, [])

  const login = async (email: string, password: string, rememberMe = false) => {
    try {
      setIsLoading(true)

      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password, rememberMe }),
      })

      const data = await response.json()

      if (response.ok && data.success) {
        const { user: userData, tokens: tokenData } = data.data

        setUser(userData)
        setTokens(tokenData)
        
        // Store in localStorage
        if (typeof window !== 'undefined') {
          localStorage.setItem('user', JSON.stringify(userData))
          localStorage.setItem('token', tokenData.accessToken)
          localStorage.setItem('refreshToken', tokenData.refreshToken)
        }
        
        console.log('✅ User logged in:', userData.username)
        return { success: true }
      } else {
        return { success: false, error: data.error || 'Login failed' }
      }
    } catch (error) {
      console.error('Login error:', error)
      return { success: false, error: 'Network error. Please try again.' }
    } finally {
      setIsLoading(false)
    }
  }

  const register = async (userData: { username: string; email: string; password: string; fullName: string }) => {
    try {
      setIsLoading(true)

      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(userData),
      })

      const data = await response.json()

      if (response.ok && data.success) {
        const { user: userInfo, tokens: tokenData } = data.data

        setUser(userInfo)
        setTokens(tokenData)
        
        // Store in localStorage
        if (typeof window !== 'undefined') {
          localStorage.setItem('user', JSON.stringify(userInfo))
          localStorage.setItem('token', tokenData.accessToken)
          localStorage.setItem('refreshToken', tokenData.refreshToken)
        }
        
        console.log('✅ User registered:', userInfo.username)
        return { success: true }
      } else {
        return { success: false, error: data.error || 'Registration failed', details: data.details }
      }
    } catch (error) {
      console.error('Registration error:', error)
      return { success: false, error: 'Network error. Please try again.' }
    } finally {
      setIsLoading(false)
    }
  }

  const logout = async () => {
    try {
      // Call logout API if we have a token
      if (tokens?.accessToken) {
        await fetch('/api/auth/logout', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${tokens.accessToken}`,
            'Content-Type': 'application/json',
          },
        })
      }
    } catch (error) {
      console.error('Logout API error:', error)
    } finally {
      // Clear state
      setUser(null)
      setTokens(null)
      
      // Clear localStorage
      if (typeof window !== 'undefined') {
        localStorage.removeItem('user')
        localStorage.removeItem('token')
        localStorage.removeItem('refreshToken')
      }
      
      console.log('✅ User logged out')
      router.push('/login')
    }
  }

  const refreshTokenFunc = async (): Promise<boolean> => {
    try {
      if (!tokens?.refreshToken) {
        return false
      }

      const response = await fetch('/api/auth/refresh', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ refreshToken: tokens.refreshToken }),
      })

      const data = await response.json()

      if (response.ok && data.success) {
        const { tokens: newTokens } = data.data
        setTokens(newTokens)
        
        if (typeof window !== 'undefined') {
          localStorage.setItem('token', newTokens.accessToken)
          localStorage.setItem('refreshToken', newTokens.refreshToken)
        }
        
        return true
      } else {
        await logout()
        return false
      }
    } catch (error) {
      console.error('Token refresh error:', error)
      await logout()
      return false
    }
  }

  const updateUser = (userData: Partial<User>) => {
    if (user) {
      const updatedUser = { ...user, ...userData }
      setUser(updatedUser)
      
      if (typeof window !== 'undefined') {
        localStorage.setItem('user', JSON.stringify(updatedUser))
      }
    }
  }

  // Get current access token
  const getToken = (): string | null => {
    if (tokens?.accessToken) {
      return tokens.accessToken
    }

    // Fallback to localStorage if tokens not in state
    if (typeof window !== 'undefined') {
      return localStorage.getItem('token')
    }

    return null
  }

  const value: AuthContextType = {
    user,
    tokens,
    isLoading,
    isAuthenticated,
    login,
    register,
    logout,
    refreshToken: refreshTokenFunc,
    updateUser,
    getToken
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
