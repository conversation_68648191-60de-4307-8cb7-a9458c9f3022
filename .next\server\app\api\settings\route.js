"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/settings/route";
exports.ids = ["app/api/settings/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsettings%2Froute&page=%2Fapi%2Fsettings%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsettings%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsettings%2Froute&page=%2Fapi%2Fsettings%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsettings%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_Users_Downloads_Kode_XGuard_app_api_settings_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/settings/route.ts */ \"(rsc)/./app/api/settings/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/settings/route\",\n        pathname: \"/api/settings\",\n        filename: \"route\",\n        bundlePath: \"app/api/settings/route\"\n    },\n    resolvedPagePath: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\api\\\\settings\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_Users_Downloads_Kode_XGuard_app_api_settings_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/settings/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkZhcGklMkZzZXR0aW5ncyUyRnJvdXRlJnBhZ2U9JTJGYXBpJTJGc2V0dGluZ3MlMkZyb3V0ZSZhcHBQYXRocz0mcGFnZVBhdGg9cHJpdmF0ZS1uZXh0LWFwcC1kaXIlMkZhcGklMkZzZXR0aW5ncyUyRnJvdXRlLnRzJmFwcERpcj1EJTNBJTVDVXNlcnMlNUNEb3dubG9hZHMlNUNLb2RlLVhHdWFyZCU1Q2FwcCZwYWdlRXh0ZW5zaW9ucz10c3gmcGFnZUV4dGVuc2lvbnM9dHMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPWpzJnJvb3REaXI9RCUzQSU1Q1VzZXJzJTVDRG93bmxvYWRzJTVDS29kZS1YR3VhcmQmaXNEZXY9dHJ1ZSZ0c2NvbmZpZ1BhdGg9dHNjb25maWcuanNvbiZiYXNlUGF0aD0mYXNzZXRQcmVmaXg9Jm5leHRDb25maWdPdXRwdXQ9JnByZWZlcnJlZFJlZ2lvbj0mbWlkZGxld2FyZUNvbmZpZz1lMzAlM0QhIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7OztBQUFzRztBQUN2QztBQUNjO0FBQ2U7QUFDNUY7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLGdIQUFtQjtBQUMzQztBQUNBLGNBQWMseUVBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLFlBQVk7QUFDWixDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0EsUUFBUSxpRUFBaUU7QUFDekU7QUFDQTtBQUNBLFdBQVcsNEVBQVc7QUFDdEI7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUN1SDs7QUFFdkgiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9kYl9rb2RleGd1YXJkLz9mNzc2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEFwcFJvdXRlUm91dGVNb2R1bGUgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUtbW9kdWxlcy9hcHAtcm91dGUvbW9kdWxlLmNvbXBpbGVkXCI7XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUta2luZFwiO1xuaW1wb3J0IHsgcGF0Y2hGZXRjaCBhcyBfcGF0Y2hGZXRjaCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2xpYi9wYXRjaC1mZXRjaFwiO1xuaW1wb3J0ICogYXMgdXNlcmxhbmQgZnJvbSBcIkQ6XFxcXFVzZXJzXFxcXERvd25sb2Fkc1xcXFxLb2RlLVhHdWFyZFxcXFxhcHBcXFxcYXBpXFxcXHNldHRpbmdzXFxcXHJvdXRlLnRzXCI7XG4vLyBXZSBpbmplY3QgdGhlIG5leHRDb25maWdPdXRwdXQgaGVyZSBzbyB0aGF0IHdlIGNhbiB1c2UgdGhlbSBpbiB0aGUgcm91dGVcbi8vIG1vZHVsZS5cbmNvbnN0IG5leHRDb25maWdPdXRwdXQgPSBcIlwiXG5jb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBBcHBSb3V0ZVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5BUFBfUk9VVEUsXG4gICAgICAgIHBhZ2U6IFwiL2FwaS9zZXR0aW5ncy9yb3V0ZVwiLFxuICAgICAgICBwYXRobmFtZTogXCIvYXBpL3NldHRpbmdzXCIsXG4gICAgICAgIGZpbGVuYW1lOiBcInJvdXRlXCIsXG4gICAgICAgIGJ1bmRsZVBhdGg6IFwiYXBwL2FwaS9zZXR0aW5ncy9yb3V0ZVwiXG4gICAgfSxcbiAgICByZXNvbHZlZFBhZ2VQYXRoOiBcIkQ6XFxcXFVzZXJzXFxcXERvd25sb2Fkc1xcXFxLb2RlLVhHdWFyZFxcXFxhcHBcXFxcYXBpXFxcXHNldHRpbmdzXFxcXHJvdXRlLnRzXCIsXG4gICAgbmV4dENvbmZpZ091dHB1dCxcbiAgICB1c2VybGFuZFxufSk7XG4vLyBQdWxsIG91dCB0aGUgZXhwb3J0cyB0aGF0IHdlIG5lZWQgdG8gZXhwb3NlIGZyb20gdGhlIG1vZHVsZS4gVGhpcyBzaG91bGRcbi8vIGJlIGVsaW1pbmF0ZWQgd2hlbiB3ZSd2ZSBtb3ZlZCB0aGUgb3RoZXIgcm91dGVzIHRvIHRoZSBuZXcgZm9ybWF0LiBUaGVzZVxuLy8gYXJlIHVzZWQgdG8gaG9vayBpbnRvIHRoZSByb3V0ZS5cbmNvbnN0IHsgcmVxdWVzdEFzeW5jU3RvcmFnZSwgc3RhdGljR2VuZXJhdGlvbkFzeW5jU3RvcmFnZSwgc2VydmVySG9va3MgfSA9IHJvdXRlTW9kdWxlO1xuY29uc3Qgb3JpZ2luYWxQYXRobmFtZSA9IFwiL2FwaS9zZXR0aW5ncy9yb3V0ZVwiO1xuZnVuY3Rpb24gcGF0Y2hGZXRjaCgpIHtcbiAgICByZXR1cm4gX3BhdGNoRmV0Y2goe1xuICAgICAgICBzZXJ2ZXJIb29rcyxcbiAgICAgICAgc3RhdGljR2VuZXJhdGlvbkFzeW5jU3RvcmFnZVxuICAgIH0pO1xufVxuZXhwb3J0IHsgcm91dGVNb2R1bGUsIHJlcXVlc3RBc3luY1N0b3JhZ2UsIHN0YXRpY0dlbmVyYXRpb25Bc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzLCBvcmlnaW5hbFBhdGhuYW1lLCBwYXRjaEZldGNoLCAgfTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YXBwLXJvdXRlLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsettings%2Froute&page=%2Fapi%2Fsettings%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsettings%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/settings/route.ts":
/*!***********************************!*\
  !*** ./app/api/settings/route.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst JWT_SECRET = process.env.JWT_SECRET || \"your-secret-key-here-make-it-very-long-and-secure-for-production\";\n// Simple in-memory user storage (replace with database in production)\nconst users = new Map([\n    [\n        \"1\",\n        {\n            id: \"1\",\n            username: \"admin\",\n            email: \"<EMAIL>\",\n            fullName: \"Admin KodeXGuard\",\n            role: \"admin\",\n            plan: \"Elite\",\n            preferences: {\n                notifications: {\n                    email: true,\n                    push: true,\n                    sms: false,\n                    security: true,\n                    marketing: false,\n                    scanComplete: true,\n                    vulnerabilityFound: true,\n                    reportReady: true,\n                    weeklyDigest: true,\n                    communityUpdates: false\n                },\n                privacy: {\n                    profileVisibility: \"public\",\n                    showEmail: false,\n                    showPhone: false,\n                    showLocation: true,\n                    showActivity: true,\n                    showAchievements: true,\n                    allowDirectMessages: true,\n                    showOnlineStatus: false\n                },\n                security: {\n                    twoFactorEnabled: true,\n                    loginAlerts: true,\n                    sessionTimeout: 30,\n                    passwordExpiry: 90,\n                    allowedIPs: [],\n                    trustedDevices: [],\n                    apiKeyEnabled: true,\n                    webhookEnabled: false\n                },\n                appearance: {\n                    theme: \"dark\",\n                    language: \"en\",\n                    timezone: \"Asia/Jakarta\",\n                    dateFormat: \"DD/MM/YYYY\",\n                    timeFormat: \"24h\",\n                    compactMode: false,\n                    animations: true,\n                    soundEffects: true\n                },\n                dashboard: {\n                    defaultView: \"overview\",\n                    showQuickActions: true,\n                    showRecentActivity: true,\n                    showStatistics: true,\n                    autoRefresh: true,\n                    refreshInterval: 30,\n                    widgetLayout: \"grid\",\n                    pinnedTools: [\n                        \"scanner\",\n                        \"reports\",\n                        \"osint\"\n                    ]\n                }\n            }\n        }\n    ]\n]);\nfunction getUserFromToken(request) {\n    try {\n        const cookies = request.headers.get(\"cookie\");\n        if (!cookies) return null;\n        const tokenMatch = cookies.match(/accessToken=([^;]+)/);\n        const token = tokenMatch ? tokenMatch[1] : null;\n        if (!token) return null;\n        const decoded = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().verify(token, JWT_SECRET);\n        return decoded;\n    } catch (error) {\n        console.error(\"Token verification error:\", error);\n        return null;\n    }\n}\nasync function GET(request) {\n    try {\n        console.log(\"⚙️ Settings API: GET request received\");\n        const user = getUserFromToken(request);\n        if (!user) {\n            console.log(\"❌ Settings API: No valid token\");\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        console.log(\"✅ Settings API: User authenticated:\", user.username);\n        const userProfile = users.get(user.id.toString());\n        if (!userProfile) {\n            console.log(\"❌ Settings API: User not found\");\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"User not found\"\n            }, {\n                status: 404\n            });\n        }\n        console.log(\"✅ Settings API: Settings data retrieved successfully\");\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                user: {\n                    id: userProfile.id,\n                    username: userProfile.username,\n                    email: userProfile.email,\n                    fullName: userProfile.fullName,\n                    role: userProfile.role,\n                    plan: userProfile.plan\n                },\n                preferences: userProfile.preferences\n            }\n        });\n    } catch (error) {\n        console.error(\"❌ Settings API Error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function PUT(request) {\n    try {\n        console.log(\"⚙️ Settings API: PUT request received\");\n        const user = getUserFromToken(request);\n        if (!user) {\n            console.log(\"❌ Settings API: No valid token\");\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        const body = await request.json();\n        console.log(\"⚙️ Settings API: Update data:\", body);\n        const userProfile = users.get(user.id.toString());\n        if (!userProfile) {\n            console.log(\"❌ Settings API: User not found\");\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"User not found\"\n            }, {\n                status: 404\n            });\n        }\n        // Update preferences\n        if (body.preferences) {\n            userProfile.preferences = {\n                ...userProfile.preferences,\n                ...body.preferences\n            };\n            // Deep merge for nested objects\n            Object.keys(body.preferences).forEach((category)=>{\n                if (typeof body.preferences[category] === \"object\" && userProfile.preferences[category]) {\n                    userProfile.preferences[category] = {\n                        ...userProfile.preferences[category],\n                        ...body.preferences[category]\n                    };\n                }\n            });\n        }\n        // Save updated profile\n        users.set(user.id.toString(), userProfile);\n        console.log(\"✅ Settings API: Settings updated successfully\");\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                user: {\n                    id: userProfile.id,\n                    username: userProfile.username,\n                    email: userProfile.email,\n                    fullName: userProfile.fullName,\n                    role: userProfile.role,\n                    plan: userProfile.plan\n                },\n                preferences: userProfile.preferences\n            },\n            message: \"Settings updated successfully\"\n        });\n    } catch (error) {\n        console.error(\"❌ Settings API Error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/settings/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry","vendor-chunks/semver","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/jws","vendor-chunks/lodash.once","vendor-chunks/jwa","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/ms","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsettings%2Froute&page=%2Fapi%2Fsettings%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsettings%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();