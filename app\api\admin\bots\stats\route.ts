import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/database'
import { verifyToken } from '@/lib/auth'

export async function GET(request: NextRequest) {
  try {
    const token = request.headers.get('authorization')?.replace('Bearer ', '')
    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const user = await verifyToken(token)
    if (!user || (user.role !== 'admin' && user.role !== 'super_admin')) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const { searchParams } = new URL(request.url)
    const period = searchParams.get('period') || '7d' // 1d, 7d, 30d, 90d

    // Get date range based on period
    let dateCondition = ''
    switch (period) {
      case '1d':
        dateCondition = 'AND created_at >= DATE_SUB(NOW(), INTERVAL 1 DAY)'
        break
      case '7d':
        dateCondition = 'AND created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)'
        break
      case '30d':
        dateCondition = 'AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)'
        break
      case '90d':
        dateCondition = 'AND created_at >= DATE_SUB(NOW(), INTERVAL 90 DAY)'
        break
      default:
        dateCondition = 'AND created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)'
    }

    // Bot instances statistics
    const [botStats] = await db.query(`
      SELECT 
        type,
        status,
        COUNT(*) as count
      FROM bot_instances 
      WHERE 1=1 ${dateCondition}
      GROUP BY type, status
    `)

    // Bot usage statistics
    const [usageStats] = await db.query(`
      SELECT 
        DATE(created_at) as date,
        COUNT(*) as total_commands,
        COUNT(DISTINCT user_id) as unique_users
      FROM bot_logs 
      WHERE 1=1 ${dateCondition}
      GROUP BY DATE(created_at)
      ORDER BY date DESC
    `)

    // Command statistics
    const [commandStats] = await db.query(`
      SELECT 
        command,
        COUNT(*) as usage_count,
        AVG(response_time) as avg_response_time
      FROM bot_logs 
      WHERE 1=1 ${dateCondition}
      GROUP BY command
      ORDER BY usage_count DESC
      LIMIT 10
    `)

    // Error statistics
    const [errorStats] = await db.query(`
      SELECT 
        error_type,
        COUNT(*) as error_count,
        DATE(created_at) as date
      FROM bot_logs 
      WHERE status = 'error' ${dateCondition}
      GROUP BY error_type, DATE(created_at)
      ORDER BY error_count DESC
    `)

    // User engagement statistics
    const [userStats] = await db.query(`
      SELECT 
        u.plan,
        COUNT(DISTINCT bl.user_id) as active_users,
        COUNT(bl.id) as total_commands
      FROM bot_logs bl
      JOIN users u ON bl.user_id = u.id
      WHERE 1=1 ${dateCondition}
      GROUP BY u.plan
    `)

    // Recent activity
    const [recentActivity] = await db.query(`
      SELECT 
        bl.command,
        bl.platform,
        bl.status,
        bl.created_at,
        u.username
      FROM bot_logs bl
      JOIN users u ON bl.user_id = u.id
      WHERE 1=1 ${dateCondition}
      ORDER BY bl.created_at DESC
      LIMIT 20
    `)

    // Performance metrics
    const [performanceMetrics] = await db.query(`
      SELECT 
        platform,
        AVG(response_time) as avg_response_time,
        MIN(response_time) as min_response_time,
        MAX(response_time) as max_response_time,
        COUNT(*) as total_requests
      FROM bot_logs 
      WHERE status = 'success' ${dateCondition}
      GROUP BY platform
    `)

    // Calculate summary statistics
    const totalBots = (botStats as any[]).reduce((sum, stat) => sum + stat.count, 0)
    const activeBots = (botStats as any[])
      .filter(stat => stat.status === 'connected')
      .reduce((sum, stat) => sum + stat.count, 0)
    
    const totalCommands = (usageStats as any[]).reduce((sum, stat) => sum + stat.total_commands, 0)
    const uniqueUsers = Math.max(...(usageStats as any[]).map(stat => stat.unique_users), 0)

    const avgResponseTime = (performanceMetrics as any[]).length > 0
      ? (performanceMetrics as any[]).reduce((sum, metric) => sum + metric.avg_response_time, 0) / (performanceMetrics as any[]).length
      : 0

    return NextResponse.json({
      success: true,
      data: {
        summary: {
          totalBots,
          activeBots,
          totalCommands,
          uniqueUsers,
          avgResponseTime: Math.round(avgResponseTime),
          period
        },
        botStats,
        usageStats,
        commandStats,
        errorStats,
        userStats,
        recentActivity,
        performanceMetrics,
        charts: {
          dailyUsage: (usageStats as any[]).map(stat => ({
            date: stat.date,
            commands: stat.total_commands,
            users: stat.unique_users
          })),
          commandDistribution: (commandStats as any[]).map(stat => ({
            command: stat.command,
            count: stat.usage_count,
            avgTime: Math.round(stat.avg_response_time)
          })),
          errorTrends: (errorStats as any[]).map(stat => ({
            type: stat.error_type,
            count: stat.error_count,
            date: stat.date
          }))
        }
      }
    })

  } catch (error) {
    console.error('Error fetching bot statistics:', error)
    return NextResponse.json(
      { error: 'Failed to fetch bot statistics' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const token = request.headers.get('authorization')?.replace('Bearer ', '')
    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const user = await verifyToken(token)
    if (!user || user.role !== 'super_admin') {
      return NextResponse.json({ error: 'Forbidden - Super Admin only' }, { status: 403 })
    }

    const body = await request.json()
    const { action } = body

    if (action === 'reset_stats') {
      // Reset bot statistics (keep last 30 days)
      await db.query(`
        DELETE FROM bot_logs 
        WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY)
      `)

      return NextResponse.json({
        success: true,
        message: 'Bot statistics reset successfully (kept last 30 days)'
      })
    }

    if (action === 'export_logs') {
      // Export bot logs for analysis
      const [logs] = await db.query(`
        SELECT 
          bl.*,
          u.username,
          u.plan
        FROM bot_logs bl
        JOIN users u ON bl.user_id = u.id
        WHERE bl.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
        ORDER BY bl.created_at DESC
      `)

      return NextResponse.json({
        success: true,
        data: {
          logs,
          exportedAt: new Date().toISOString(),
          totalRecords: (logs as any[]).length
        }
      })
    }

    return NextResponse.json({ error: 'Invalid action' }, { status: 400 })

  } catch (error) {
    console.error('Error processing bot statistics action:', error)
    return NextResponse.json(
      { error: 'Failed to process action' },
      { status: 500 }
    )
  }
}
