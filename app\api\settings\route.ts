import { NextRequest, NextResponse } from 'next/server'
import jwt from 'jsonwebtoken'

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-here-make-it-very-long-and-secure-for-production'

// Simple in-memory user storage (replace with database in production)
const users = new Map([
  ['1', {
    id: '1',
    username: 'admin',
    email: '<EMAIL>',
    fullName: 'Admin KodeXGuard',
    role: 'admin',
    plan: 'Elite',
    preferences: {
      notifications: {
        email: true,
        push: true,
        sms: false,
        security: true,
        marketing: false,
        scanComplete: true,
        vulnerabilityFound: true,
        reportReady: true,
        weeklyDigest: true,
        communityUpdates: false
      },
      privacy: {
        profileVisibility: 'public',
        showEmail: false,
        showPhone: false,
        showLocation: true,
        showActivity: true,
        showAchievements: true,
        allowDirectMessages: true,
        showOnlineStatus: false
      },
      security: {
        twoFactorEnabled: true,
        loginAlerts: true,
        sessionTimeout: 30,
        passwordExpiry: 90,
        allowedIPs: [],
        trustedDevices: [],
        apiKeyEnabled: true,
        webhookEnabled: false
      },
      appearance: {
        theme: 'dark',
        language: 'en',
        timezone: 'Asia/Jakarta',
        dateFormat: 'DD/MM/YYYY',
        timeFormat: '24h',
        compactMode: false,
        animations: true,
        soundEffects: true
      },
      dashboard: {
        defaultView: 'overview',
        showQuickActions: true,
        showRecentActivity: true,
        showStatistics: true,
        autoRefresh: true,
        refreshInterval: 30,
        widgetLayout: 'grid',
        pinnedTools: ['scanner', 'reports', 'osint']
      }
    }
  }]
])

function getUserFromToken(request: NextRequest) {
  try {
    const cookies = request.headers.get('cookie')
    if (!cookies) return null

    const tokenMatch = cookies.match(/accessToken=([^;]+)/)
    const token = tokenMatch ? tokenMatch[1] : null

    if (!token) return null

    const decoded = jwt.verify(token, JWT_SECRET) as any
    return decoded
  } catch (error) {
    console.error('Token verification error:', error)
    return null
  }
}

export async function GET(request: NextRequest) {
  try {
    console.log('⚙️ Settings API: GET request received')
    
    const user = getUserFromToken(request)
    if (!user) {
      console.log('❌ Settings API: No valid token')
      return NextResponse.json({ success: false, error: 'Unauthorized' }, { status: 401 })
    }

    console.log('✅ Settings API: User authenticated:', user.username)

    const userProfile = users.get(user.id.toString())
    if (!userProfile) {
      console.log('❌ Settings API: User not found')
      return NextResponse.json({ success: false, error: 'User not found' }, { status: 404 })
    }

    console.log('✅ Settings API: Settings data retrieved successfully')

    return NextResponse.json({
      success: true,
      data: {
        user: {
          id: userProfile.id,
          username: userProfile.username,
          email: userProfile.email,
          fullName: userProfile.fullName,
          role: userProfile.role,
          plan: userProfile.plan
        },
        preferences: userProfile.preferences
      }
    })

  } catch (error) {
    console.error('❌ Settings API Error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    console.log('⚙️ Settings API: PUT request received')
    
    const user = getUserFromToken(request)
    if (!user) {
      console.log('❌ Settings API: No valid token')
      return NextResponse.json({ success: false, error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    console.log('⚙️ Settings API: Update data:', body)

    const userProfile = users.get(user.id.toString())
    if (!userProfile) {
      console.log('❌ Settings API: User not found')
      return NextResponse.json({ success: false, error: 'User not found' }, { status: 404 })
    }

    // Update preferences
    if (body.preferences) {
      userProfile.preferences = {
        ...userProfile.preferences,
        ...body.preferences
      }

      // Deep merge for nested objects
      Object.keys(body.preferences).forEach(category => {
        if (typeof body.preferences[category] === 'object' && userProfile.preferences[category]) {
          userProfile.preferences[category] = {
            ...userProfile.preferences[category],
            ...body.preferences[category]
          }
        }
      })
    }

    // Save updated profile
    users.set(user.id.toString(), userProfile)

    console.log('✅ Settings API: Settings updated successfully')

    return NextResponse.json({
      success: true,
      data: {
        user: {
          id: userProfile.id,
          username: userProfile.username,
          email: userProfile.email,
          fullName: userProfile.fullName,
          role: userProfile.role,
          plan: userProfile.plan
        },
        preferences: userProfile.preferences
      },
      message: 'Settings updated successfully'
    })

  } catch (error) {
    console.error('❌ Settings API Error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
