'use client'

import { useState, useEffect, ReactNode } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { 
  Shield, 
  User, 
  Settings, 
  LogOut, 
  Menu, 
  X, 
  Bell,
  Search,
  Crown,
  Activity,
  BarChart3,
  Database,
  Bot,
  Users,
  Zap,
  Globe,
  FileText,
  Bug,
  Target,
  Award,
  TrendingUp,
  Calendar,
  Clock,
  Flame,
  Star,
  ChevronDown,
  ChevronRight,
  Home,
  Cpu,
  Server,
  Lock,
  Eye,
  Wifi,
  WifiOff
} from 'lucide-react'

interface DashboardLayoutProps {
  children: ReactNode
}

interface User {
  id: string
  username: string
  email: string
  fullName: string
  avatar?: string
  role: string
  plan: string
  level?: number
  score?: number
  streak?: number
}

interface NavItem {
  name: string
  href: string
  icon: any
  badge?: string
  children?: NavItem[]
}

export default function DashboardLayout({ children }: DashboardLayoutProps) {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false)
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const [isProfileOpen, setIsProfileOpen] = useState(false)
  const [expandedItems, setExpandedItems] = useState<string[]>([])
  const [currentTime, setCurrentTime] = useState(new Date())
  const [systemStatus, setSystemStatus] = useState('online')
  
  const router = useRouter()
  const pathname = usePathname()

  // Update time every minute
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 60000)
    return () => clearInterval(timer)
  }, [])

  // Load user and check auth
  useEffect(() => {
    const checkAuth = async () => {
      try {
        // Get user data from cookies (set by middleware)
        const userCookie = document.cookie
          .split('; ')
          .find(row => row.startsWith('user='))

        if (!userCookie) {
          router.push('/login')
          return
        }

        const userData = JSON.parse(decodeURIComponent(userCookie.split('=')[1]))

        // Transform to match our User interface
        const transformedUser = {
          id: userData.id?.toString() || '1',
          username: userData.username || 'User',
          email: userData.email || '<EMAIL>',
          fullName: userData.username || 'KodeX User',
          role: userData.role || 'user',
          plan: userData.plan || 'Free',
          level: 28,
          score: 8950,
          streak: 12
        }

        setUser(transformedUser)
        setLoading(false)
      } catch (error) {
        console.error('Auth check error:', error)
        router.push('/login')
      }
    }

    checkAuth()
  }, [router])

  const handleLogout = async () => {
    localStorage.removeItem('token')
    localStorage.removeItem('refreshToken')
    localStorage.removeItem('user')
    router.push('/login')
  }

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen)
  }

  const toggleExpanded = (itemName: string) => {
    setExpandedItems(prev => 
      prev.includes(itemName) 
        ? prev.filter(name => name !== itemName)
        : [...prev, itemName]
    )
  }

  const isActive = (href: string) => {
    if (href === '/dashboard') {
      return pathname === '/dashboard'
    }
    return pathname.startsWith(href)
  }

  const isAdmin = pathname.startsWith('/admin')

  const dashboardNavItems: NavItem[] = [
    { name: 'Dashboard', href: '/dashboard', icon: Home },
    { name: 'Profile', href: '/profile', icon: User },
    { name: 'Settings', href: '/settings', icon: Settings },
    {
      name: 'Tools',
      href: '#',
      icon: Zap,
      children: [
        { name: 'OSINT Lookup', href: '/osint', icon: Search, badge: 'Popular' },
        { name: 'Vulnerability Scanner', href: '/scanner', icon: Shield, badge: 'Pro' },
        { name: 'File Analyzer', href: '/file-analyzer', icon: FileText, badge: 'Pro' },
        { name: 'CVE Database', href: '/cve', icon: Database },
        { name: 'Google Dorking', href: '/dorking', icon: Globe }
      ]
    },
    {
      name: 'Resources',
      href: '#',
      icon: Award,
      children: [
        { name: 'Documentation', href: '/docs', icon: FileText },
        { name: 'Community', href: '/community', icon: Users },
        { name: 'Leaderboard', href: '/leaderboard', icon: TrendingUp }
      ]
    }
  ]

  const adminNavItems: NavItem[] = [
    { name: 'Dashboard', href: '/admin/dashboard', icon: BarChart3 },
    { name: 'Users', href: '/admin/users', icon: Users },
    { name: 'Bots', href: '/admin/bots', icon: Bot },
    { name: 'Plans', href: '/admin/plans', icon: Crown },
    { name: 'Settings', href: '/admin/settings', icon: Settings },
    {
      name: 'System',
      href: '#',
      icon: Server,
      children: [
        { name: 'Monitoring', href: '/admin/monitoring', icon: Activity },
        { name: 'Logs', href: '/admin/logs', icon: FileText },
        { name: 'Security', href: '/admin/security', icon: Lock }
      ]
    }
  ]

  const navItems = isAdmin ? adminNavItems : dashboardNavItems

  const getPlanColor = (plan: string) => {
    switch (plan) {
      case 'Elite': return 'text-yellow-400 bg-yellow-400/20'
      case 'Expert': return 'text-purple-400 bg-purple-400/20'
      case 'Pro': return 'text-blue-400 bg-blue-400/20'
      case 'Free': return 'text-gray-400 bg-gray-400/20'
      default: return 'text-gray-400 bg-gray-400/20'
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-cyber-dark flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-2 border-cyber-primary border-t-transparent mx-auto mb-4"></div>
          <div className="text-cyber-primary font-medium">Initializing cyber interface...</div>
        </div>
      </div>
    )
  }

  if (!user) {
    return null
  }

  return (
    <div className="min-h-screen bg-cyber-dark">
      {/* Header */}
      <header className="fixed top-0 left-0 right-0 z-50 bg-cyber-dark/95 backdrop-blur-md border-b border-cyber-border">
        <div className="flex items-center justify-between h-16 px-4">
          {/* Left Side */}
          <div className="flex items-center space-x-4">
            <button
              onClick={toggleSidebar}
              className="p-2 rounded-lg text-gray-300 hover:text-cyber-primary hover:bg-cyber-primary/10 transition-colors lg:hidden"
            >
              {isSidebarOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </button>
            
            <div className="flex items-center space-x-3">
              <div className="relative">
                <Shield className="h-8 w-8 text-cyber-primary animate-cyber-glow" />
                <div className="absolute inset-0 bg-cyber-primary opacity-20 blur-lg animate-cyber-pulse"></div>
              </div>
              <div className="hidden sm:block">
                <h1 className="text-xl font-bold text-cyber-glow">KodeXGuard</h1>
                <p className="text-xs text-cyber-secondary uppercase tracking-wider">
                  {isAdmin ? 'Admin Console' : 'Cyber Platform'}
                </p>
              </div>
            </div>
          </div>

          {/* Center - Search */}
          <div className="hidden md:flex flex-1 max-w-md mx-8">
            <div className="relative w-full">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search..."
                className="w-full pl-10 pr-4 py-2 rounded-lg input-cyber text-sm"
              />
            </div>
          </div>

          {/* Right Side */}
          <div className="flex items-center space-x-4">
            {/* System Status */}
            <div className="hidden lg:flex items-center space-x-2">
              <div className={`w-2 h-2 rounded-full ${systemStatus === 'online' ? 'bg-green-400' : 'bg-red-400'} animate-pulse`}></div>
              <span className="text-xs text-gray-400">System {systemStatus}</span>
            </div>

            {/* Time */}
            <div className="hidden sm:block text-xs text-gray-400 font-mono">
              {currentTime.toLocaleTimeString()}
            </div>

            {/* Notifications */}
            <button className="p-2 rounded-lg text-gray-300 hover:text-cyber-primary hover:bg-cyber-primary/10 transition-colors relative">
              <Bell className="h-5 w-5" />
              <div className="absolute top-1 right-1 w-2 h-2 bg-cyber-secondary rounded-full"></div>
            </button>

            {/* User Menu */}
            <div className="relative">
              <button
                onClick={() => setIsProfileOpen(!isProfileOpen)}
                className="flex items-center space-x-3 p-2 rounded-lg hover:bg-cyber-primary/10 transition-colors"
              >
                <div className="w-8 h-8 rounded-full bg-gradient-to-r from-cyber-primary to-cyber-secondary flex items-center justify-center text-sm font-bold text-black">
                  {user.username.charAt(0)}
                </div>
                <div className="hidden sm:block text-left">
                  <div className="text-sm font-medium text-white">{user.username}</div>
                  <div className={`text-xs px-2 py-0.5 rounded-full ${getPlanColor(user.plan)}`}>
                    {user.plan}
                  </div>
                </div>
                <ChevronDown className="h-4 w-4 text-gray-400" />
              </button>

              {/* User Dropdown */}
              {isProfileOpen && (
                <div className="absolute right-0 top-full mt-2 w-64 bg-cyber-card border border-cyber-border rounded-lg shadow-xl z-50">
                  <div className="p-4 border-b border-cyber-border">
                    <div className="flex items-center space-x-3">
                      <div className="w-12 h-12 rounded-full bg-gradient-to-r from-cyber-primary to-cyber-secondary flex items-center justify-center text-lg font-bold text-black">
                        {user.username.charAt(0)}
                      </div>
                      <div>
                        <div className="font-medium text-white">{user.fullName}</div>
                        <div className="text-sm text-gray-400">{user.email}</div>
                        {user.streak && (
                          <div className="flex items-center space-x-1 text-xs text-cyber-secondary">
                            <Flame className="h-3 w-3" />
                            <span>{user.streak} day streak</span>
                          </div>
                        )}
                      </div>
                    </div>
                    
                    {user.level && user.score && (
                      <div className="mt-3 grid grid-cols-2 gap-3 text-center">
                        <div>
                          <div className="text-lg font-bold text-cyber-primary">{user.level}</div>
                          <div className="text-xs text-gray-400">Level</div>
                        </div>
                        <div>
                          <div className="text-lg font-bold text-cyber-secondary">{user.score.toLocaleString()}</div>
                          <div className="text-xs text-gray-400">Score</div>
                        </div>
                      </div>
                    )}
                  </div>
                  
                  <div className="p-2">
                    <button
                      onClick={() => router.push('/profile')}
                      className="w-full flex items-center space-x-2 px-3 py-2 text-left text-white hover:bg-cyber-primary/10 rounded-lg transition-colors"
                    >
                      <User className="h-4 w-4 text-cyber-primary" />
                      <span>Profile</span>
                    </button>
                    <button
                      onClick={() => router.push('/settings')}
                      className="w-full flex items-center space-x-2 px-3 py-2 text-left text-white hover:bg-cyber-primary/10 rounded-lg transition-colors"
                    >
                      <Settings className="h-4 w-4 text-cyber-primary" />
                      <span>Settings</span>
                    </button>
                    <button
                      onClick={handleLogout}
                      className="w-full flex items-center space-x-2 px-3 py-2 text-left text-red-400 hover:bg-red-500/10 rounded-lg transition-colors"
                    >
                      <LogOut className="h-4 w-4" />
                      <span>Logout</span>
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </header>

      {/* Sidebar */}
      <aside className={`fixed top-16 left-0 z-40 w-64 h-[calc(100vh-4rem)] bg-cyber-card border-r border-cyber-border transform transition-transform duration-300 ease-in-out ${
        isSidebarOpen ? 'translate-x-0' : '-translate-x-full'
      } lg:translate-x-0`}>
        <div className="flex flex-col h-full">
          {/* Navigation */}
          <nav className="flex-1 p-4 space-y-2 overflow-y-auto">
            {navItems.map((item) => {
              const Icon = item.icon
              const hasChildren = item.children && item.children.length > 0
              const isExpanded = expandedItems.includes(item.name)
              const itemIsActive = hasChildren ? item.children?.some(child => isActive(child.href)) : isActive(item.href)

              return (
                <div key={item.name}>
                  {hasChildren ? (
                    <button
                      onClick={() => toggleExpanded(item.name)}
                      className={`w-full flex items-center justify-between px-3 py-2 rounded-lg font-medium transition-colors duration-200 ${
                        itemIsActive
                          ? 'bg-cyber-primary/20 text-cyber-primary border border-cyber-primary/30'
                          : 'text-gray-300 hover:text-white hover:bg-cyber-primary/10'
                      }`}
                    >
                      <div className="flex items-center space-x-3">
                        <Icon className="h-5 w-5" />
                        <span>{item.name}</span>
                      </div>
                      <ChevronRight className={`h-4 w-4 transition-transform duration-200 ${isExpanded ? 'rotate-90' : ''}`} />
                    </button>
                  ) : (
                    <button
                      onClick={() => router.push(item.href)}
                      className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg font-medium transition-colors duration-200 ${
                        itemIsActive
                          ? 'bg-cyber-primary/20 text-cyber-primary border border-cyber-primary/30'
                          : 'text-gray-300 hover:text-white hover:bg-cyber-primary/10'
                      }`}
                    >
                      <Icon className="h-5 w-5" />
                      <span>{item.name}</span>
                      {item.badge && (
                        <span className="ml-auto bg-cyber-secondary/20 text-cyber-secondary px-2 py-0.5 rounded-full text-xs font-bold">
                          {item.badge}
                        </span>
                      )}
                    </button>
                  )}

                  {/* Submenu */}
                  {hasChildren && isExpanded && (
                    <div className="ml-6 mt-2 space-y-1">
                      {item.children?.map((child) => {
                        const ChildIcon = child.icon
                        return (
                          <button
                            key={child.name}
                            onClick={() => router.push(child.href)}
                            className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-sm transition-colors duration-200 ${
                              isActive(child.href)
                                ? 'bg-cyber-primary/20 text-cyber-primary'
                                : 'text-gray-400 hover:text-white hover:bg-cyber-primary/10'
                            }`}
                          >
                            <ChildIcon className="h-4 w-4" />
                            <span>{child.name}</span>
                            {child.badge && (
                              <span className="ml-auto bg-cyber-secondary/20 text-cyber-secondary px-1.5 py-0.5 rounded-full text-xs font-bold">
                                {child.badge}
                              </span>
                            )}
                          </button>
                        )
                      })}
                    </div>
                  )}
                </div>
              )
            })}
          </nav>

          {/* Sidebar Footer */}
          <div className="p-4 border-t border-cyber-border">
            <div className="text-center">
              <div className="text-xs text-gray-400 mb-2">System Status</div>
              <div className="flex items-center justify-center space-x-2">
                <div className={`w-2 h-2 rounded-full ${systemStatus === 'online' ? 'bg-green-400' : 'bg-red-400'} animate-pulse`}></div>
                <span className="text-xs text-gray-300 capitalize">{systemStatus}</span>
              </div>
            </div>
          </div>
        </div>
      </aside>

      {/* Overlay for mobile */}
      {isSidebarOpen && (
        <div
          className="fixed inset-0 z-30 bg-black/50 lg:hidden"
          onClick={() => setIsSidebarOpen(false)}
        />
      )}

      {/* Main Content */}
      <main className={`transition-all duration-300 pt-16 ${isSidebarOpen ? 'lg:ml-64' : 'lg:ml-64'}`}>
        <div className="min-h-[calc(100vh-4rem)]">
          <div className="p-6">
            {children}
          </div>
        </div>

        {/* Footer */}
        <footer className="bg-cyber-card border-t border-cyber-border">
          <div className="max-w-7xl mx-auto px-6 py-8">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
              {/* Brand */}
              <div className="col-span-1 md:col-span-2">
                <div className="flex items-center space-x-3 mb-4">
                  <div className="relative">
                    <Shield className="h-8 w-8 text-cyber-primary animate-cyber-glow" />
                    <div className="absolute inset-0 bg-cyber-primary opacity-20 blur-lg animate-cyber-pulse"></div>
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-cyber-glow">KodeXGuard</h3>
                    <p className="text-xs text-cyber-secondary uppercase tracking-wider">
                      Cyber Security Platform
                    </p>
                  </div>
                </div>
                <p className="text-gray-400 text-sm mb-4">
                  Advanced cybersecurity platform for vulnerability scanning, OSINT intelligence,
                  and comprehensive security analysis.
                </p>
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                    <span className="text-xs text-gray-400">All systems operational</span>
                  </div>
                </div>
              </div>

              {/* Quick Links */}
              <div>
                <h4 className="text-sm font-bold text-white mb-4 uppercase tracking-wider">Quick Links</h4>
                <ul className="space-y-2">
                  <li>
                    <button
                      onClick={() => router.push('/dashboard')}
                      className="text-gray-400 hover:text-cyber-primary transition-colors text-sm"
                    >
                      Dashboard
                    </button>
                  </li>
                  <li>
                    <button
                      onClick={() => router.push('/profile')}
                      className="text-gray-400 hover:text-cyber-primary transition-colors text-sm"
                    >
                      Profile
                    </button>
                  </li>
                  <li>
                    <button
                      onClick={() => router.push('/settings')}
                      className="text-gray-400 hover:text-cyber-primary transition-colors text-sm"
                    >
                      Settings
                    </button>
                  </li>
                  <li>
                    <button
                      onClick={() => router.push('/docs')}
                      className="text-gray-400 hover:text-cyber-primary transition-colors text-sm"
                    >
                      Documentation
                    </button>
                  </li>
                </ul>
              </div>

              {/* System Info */}
              <div>
                <h4 className="text-sm font-bold text-white mb-4 uppercase tracking-wider">System</h4>
                <ul className="space-y-2 text-sm">
                  <li className="flex justify-between">
                    <span className="text-gray-400">Version:</span>
                    <span className="text-cyber-primary">v2.1.0</span>
                  </li>
                  <li className="flex justify-between">
                    <span className="text-gray-400">Uptime:</span>
                    <span className="text-green-400">99.8%</span>
                  </li>
                  <li className="flex justify-between">
                    <span className="text-gray-400">Users:</span>
                    <span className="text-cyber-secondary">15.4K</span>
                  </li>
                  <li className="flex justify-between">
                    <span className="text-gray-400">Scans:</span>
                    <span className="text-cyber-accent">89.4K</span>
                  </li>
                </ul>
              </div>
            </div>

            <div className="mt-8 pt-8 border-t border-cyber-border">
              <div className="flex flex-col md:flex-row justify-between items-center">
                <div className="text-sm text-gray-400">
                  © 2024 KodeXGuard. All rights reserved.
                </div>
                <div className="flex items-center space-x-4 mt-4 md:mt-0">
                  <span className="text-xs text-gray-500">
                    Last updated: {currentTime.toLocaleDateString()}
                  </span>
                  <div className="flex items-center space-x-2">
                    <Wifi className="h-4 w-4 text-green-400" />
                    <span className="text-xs text-green-400">Connected</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </footer>
      </main>
    </div>
  )
}
