"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./components/DashboardSidebar.tsx":
/*!*****************************************!*\
  !*** ./components/DashboardSidebar.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DashboardSidebar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/home.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/terminal.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-cog.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/monitor.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/cog.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield-check.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/webhook.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-check.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction DashboardSidebar(param) {\n    let { user, isCollapsed, onToggle, isMobile = false } = param;\n    var _this = this;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const [expandedItems, setExpandedItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Define sidebar items based on user role and plan\n    const sidebarItems = [\n        // Main Dashboard\n        {\n            id: \"dashboard\",\n            title: \"Dashboard\",\n            icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            href: \"/dashboard\"\n        },\n        // Core Tools (Available to all users)\n        {\n            id: \"tools\",\n            title: \"Security Tools\",\n            icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            children: [\n                {\n                    id: \"osint\",\n                    title: \"OSINT Lookup\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                    href: \"/osint\",\n                    badge: \"Popular\"\n                },\n                {\n                    id: \"scanner\",\n                    title: \"Vulnerability Scanner\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                    href: \"/scanner\",\n                    premium: true\n                },\n                {\n                    id: \"file-analyzer\",\n                    title: \"File Analyzer\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                    href: \"/file-analyzer\",\n                    premium: true\n                },\n                {\n                    id: \"cve\",\n                    title: \"CVE Database\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                    href: \"/cve\"\n                },\n                {\n                    id: \"dorking\",\n                    title: \"Google Dorking\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                    href: \"/dorking\"\n                }\n            ]\n        },\n        // Advanced Tools (Premium plans)\n        {\n            id: \"advanced\",\n            title: \"Advanced Tools\",\n            icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            plans: [\n                \"Hobby\",\n                \"Bughunter\",\n                \"Cybersecurity\"\n            ],\n            children: [\n                {\n                    id: \"playground\",\n                    title: \"API Playground\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                    href: \"/playground\"\n                },\n                {\n                    id: \"tools-advanced\",\n                    title: \"Advanced Tools\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n                    href: \"/tools\"\n                }\n            ]\n        },\n        // Community & Learning\n        {\n            id: \"community\",\n            title: \"Community\",\n            icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n            children: [\n                {\n                    id: \"leaderboard\",\n                    title: \"Leaderboard\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n                    href: \"/leaderboard\"\n                },\n                {\n                    id: \"community-hub\",\n                    title: \"Community Hub\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n                    href: \"/community\"\n                }\n            ]\n        },\n        // Admin Tools (Admin and Super Admin only)\n        ...(user === null || user === void 0 ? void 0 : user.role) === \"admin\" || (user === null || user === void 0 ? void 0 : user.role) === \"super_admin\" ? [\n            {\n                id: \"admin\",\n                title: \"Administration\",\n                icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n                roles: [\n                    \"admin\",\n                    \"super_admin\"\n                ],\n                children: [\n                    {\n                        id: \"user-management\",\n                        title: \"User Management\",\n                        icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n                        href: \"/dashboard?section=users\",\n                        roles: [\n                            \"admin\",\n                            \"super_admin\"\n                        ]\n                    },\n                    {\n                        id: \"analytics\",\n                        title: \"Analytics\",\n                        icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n                        href: \"/dashboard?section=analytics\",\n                        roles: [\n                            \"admin\",\n                            \"super_admin\"\n                        ]\n                    },\n                    {\n                        id: \"monitoring\",\n                        title: \"System Monitor\",\n                        icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n                        href: \"/dashboard?section=monitoring\",\n                        roles: [\n                            \"admin\",\n                            \"super_admin\"\n                        ]\n                    }\n                ]\n            }\n        ] : [],\n        // Super Admin Tools (Super Admin only)\n        ...(user === null || user === void 0 ? void 0 : user.role) === \"super_admin\" ? [\n            {\n                id: \"superadmin\",\n                title: \"Super Admin\",\n                icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n                roles: [\n                    \"super_admin\"\n                ],\n                children: [\n                    {\n                        id: \"website-settings\",\n                        title: \"Website Settings\",\n                        icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n                        href: \"/dashboard?section=website-settings\",\n                        roles: [\n                            \"super_admin\"\n                        ]\n                    },\n                    {\n                        id: \"payment-management\",\n                        title: \"Payment Management\",\n                        icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n                        href: \"/dashboard?section=payments\",\n                        roles: [\n                            \"super_admin\"\n                        ]\n                    },\n                    {\n                        id: \"bot-management\",\n                        title: \"Bot Management\",\n                        icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"],\n                        href: \"/dashboard?section=bots\",\n                        roles: [\n                            \"super_admin\"\n                        ]\n                    },\n                    {\n                        id: \"whatsapp-bot\",\n                        title: \"WhatsApp Bot\",\n                        icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n                        href: \"/dashboard?section=whatsapp\",\n                        roles: [\n                            \"super_admin\"\n                        ]\n                    },\n                    {\n                        id: \"telegram-bot\",\n                        title: \"Telegram Bot\",\n                        icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"],\n                        href: \"/dashboard?section=telegram\",\n                        roles: [\n                            \"super_admin\"\n                        ]\n                    },\n                    {\n                        id: \"system-config\",\n                        title: \"System Config\",\n                        icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"],\n                        href: \"/dashboard?section=system-config\",\n                        roles: [\n                            \"super_admin\"\n                        ]\n                    },\n                    {\n                        id: \"security-center\",\n                        title: \"Security Center\",\n                        icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"],\n                        href: \"/dashboard?section=security\",\n                        roles: [\n                            \"super_admin\"\n                        ]\n                    },\n                    {\n                        id: \"database-admin\",\n                        title: \"Database Admin\",\n                        icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                        href: \"/dashboard?section=database\",\n                        roles: [\n                            \"super_admin\"\n                        ]\n                    },\n                    {\n                        id: \"server-management\",\n                        title: \"Server Management\",\n                        icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"],\n                        href: \"/dashboard?section=server\",\n                        roles: [\n                            \"super_admin\"\n                        ]\n                    },\n                    {\n                        id: \"api-management\",\n                        title: \"API Management\",\n                        icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"],\n                        href: \"/dashboard?section=api\",\n                        roles: [\n                            \"super_admin\"\n                        ]\n                    }\n                ]\n            }\n        ] : [],\n        // Personal Section\n        {\n            id: \"personal\",\n            title: \"Personal\",\n            icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"],\n            children: [\n                {\n                    id: \"profile\",\n                    title: \"Profile\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"],\n                    href: \"/profile\"\n                },\n                {\n                    id: \"plan\",\n                    title: \"Subscription\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n                    href: \"/plan\"\n                },\n                {\n                    id: \"settings\",\n                    title: \"Settings\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n                    href: \"/settings\"\n                }\n            ]\n        }\n    ];\n    // Filter items based on user role and plan\n    const filterItems = (items)=>{\n        return items.filter((item)=>{\n            // Check role permissions\n            if (item.roles && !item.roles.includes((user === null || user === void 0 ? void 0 : user.role) || \"user\")) {\n                return false;\n            }\n            // Check plan permissions\n            if (item.plans && !item.plans.includes((user === null || user === void 0 ? void 0 : user.plan) || \"Free\")) {\n                return false;\n            }\n            // Check premium access\n            if (item.premium && (user === null || user === void 0 ? void 0 : user.plan) === \"Free\") {\n                return false;\n            }\n            // Filter children recursively\n            if (item.children) {\n                item.children = filterItems(item.children);\n            }\n            return true;\n        });\n    };\n    const filteredItems = filterItems(sidebarItems);\n    const toggleExpanded = (itemId)=>{\n        setExpandedItems((prev)=>prev.includes(itemId) ? prev.filter((id)=>id !== itemId) : [\n                ...prev,\n                itemId\n            ]);\n    };\n    const isActive = (href)=>{\n        if (false) {}\n        if (href === \"/dashboard\") {\n            return pathname === \"/dashboard\" && !window.location.search;\n        }\n        // Check for section-based URLs\n        if (href.includes(\"?section=\")) {\n            const currentUrl = \"\".concat(pathname).concat(window.location.search);\n            return currentUrl === href;\n        }\n        return pathname === href || pathname.startsWith(href + \"/\");\n    };\n    const renderSidebarItem = function(item) {\n        let level = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;\n        var _item_children;\n        const Icon = item.icon;\n        const hasChildren = item.children && item.children.length > 0;\n        const isExpanded = expandedItems.includes(item.id);\n        const active = item.href ? isActive(item.href) : false;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"\".concat(level > 0 ? \"ml-4\" : \"\"),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    onClick: ()=>{\n                        if (hasChildren) {\n                            toggleExpanded(item.id);\n                        } else if (item.href) {\n                            router.push(item.href);\n                        }\n                    },\n                    className: \"\\n            flex items-center justify-between p-3 rounded-lg cursor-pointer transition-all duration-200\\n            \".concat(active ? \"bg-cyber-primary/20 text-cyber-primary border-l-4 border-cyber-primary\" : \"text-gray-300 hover:bg-gray-800 hover:text-white\", \"\\n            \").concat(item.comingSoon ? \"opacity-50 cursor-not-allowed\" : \"\", \"\\n          \"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                    className: \"h-5 w-5 \".concat(active ? \"text-cyber-primary\" : \"\")\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                                    lineNumber: 387,\n                                    columnNumber: 13\n                                }, _this),\n                                !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: item.title\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                                            lineNumber: 390,\n                                            columnNumber: 17\n                                        }, _this),\n                                        item.badge && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-2 py-1 text-xs bg-cyber-primary/20 text-cyber-primary rounded-full\",\n                                            children: item.badge\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                                            lineNumber: 392,\n                                            columnNumber: 19\n                                        }, _this),\n                                        item.premium && (user === null || user === void 0 ? void 0 : user.plan) === \"Free\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-2 py-1 text-xs bg-yellow-500/20 text-yellow-400 rounded-full\",\n                                            children: \"PRO\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                                            lineNumber: 397,\n                                            columnNumber: 19\n                                        }, _this),\n                                        item.comingSoon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-2 py-1 text-xs bg-gray-500/20 text-gray-400 rounded-full\",\n                                            children: \"Soon\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                                            lineNumber: 402,\n                                            columnNumber: 19\n                                        }, _this)\n                                    ]\n                                }, void 0, true)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                            lineNumber: 386,\n                            columnNumber: 11\n                        }, _this),\n                        !isCollapsed && hasChildren && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                            className: \"w-4 h-4 transform transition-transform \".concat(isExpanded ? \"rotate-90\" : \"\")\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                            lineNumber: 411,\n                            columnNumber: 13\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                    lineNumber: 369,\n                    columnNumber: 9\n                }, _this),\n                !isCollapsed && hasChildren && isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-2 space-y-1\",\n                    children: (_item_children = item.children) === null || _item_children === void 0 ? void 0 : _item_children.map((child)=>renderSidebarItem(child, level + 1))\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                    lineNumber: 416,\n                    columnNumber: 11\n                }, _this)\n            ]\n        }, item.id, true, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n            lineNumber: 368,\n            columnNumber: 7\n        }, _this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\\n      bg-gray-900 border-r border-gray-800 transition-all duration-300 flex flex-col\\n      \".concat(isCollapsed ? \"w-16\" : \"w-64\", \"\\n    \"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-b border-gray-800\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-lg font-bold text-white\",\n                                    children: \"KodeXGuard\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                                    lineNumber: 434,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400\",\n                                    children: [\n                                        (user === null || user === void 0 ? void 0 : user.role) === \"super_admin\" ? \"\\uD83D\\uDC51 Super Admin\" : (user === null || user === void 0 ? void 0 : user.role) === \"admin\" ? \"\\uD83D\\uDEE1️ Admin\" : \"\\uD83D\\uDD12 User\",\n                                        \" • \",\n                                        user === null || user === void 0 ? void 0 : user.plan\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                                    lineNumber: 435,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                            lineNumber: 433,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onToggle,\n                            className: \"p-2 rounded-lg hover:bg-gray-800 text-gray-400 hover:text-white transition-colors\",\n                            children: isCollapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                className: \"w-5 h-5\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                                lineNumber: 446,\n                                columnNumber: 28\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                className: \"w-5 h-5\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                                lineNumber: 446,\n                                columnNumber: 59\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                            lineNumber: 442,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                    lineNumber: 431,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                lineNumber: 430,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto p-4 space-y-2\",\n                children: filteredItems.map((item)=>renderSidebarItem(item))\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                lineNumber: 452,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-t border-gray-800\",\n                children: !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-xs text-gray-500 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"\\xa9 2024 KodeXGuard\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                            lineNumber: 460,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"Cybersecurity Platform\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                            lineNumber: 461,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                    lineNumber: 459,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                lineNumber: 457,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n        lineNumber: 425,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardSidebar, \"IawfwaZbxNshDbEnUCH2X8OEMKg=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname\n    ];\n});\n_c = DashboardSidebar;\nvar _c;\n$RefreshReg$(_c, \"DashboardSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2NvbXBvbmVudHMvRGFzaGJvYXJkU2lkZWJhci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRTJDO0FBQ2E7QUFpQ25DO0FBZ0NOLFNBQVNnQyxpQkFBaUIsS0FBd0U7UUFBeEUsRUFBRUMsSUFBSSxFQUFFQyxXQUFXLEVBQUVDLFFBQVEsRUFBRUMsV0FBVyxLQUFLLEVBQXlCLEdBQXhFOzs7SUFDdkMsTUFBTUMsU0FBU3BDLDBEQUFTQTtJQUN4QixNQUFNcUMsV0FBV3BDLDREQUFXQTtJQUM1QixNQUFNLENBQUNxQyxlQUFlQyxpQkFBaUIsR0FBR3hDLCtDQUFRQSxDQUFXLEVBQUU7SUFFL0QsbURBQW1EO0lBQ25ELE1BQU15QyxlQUE4QjtRQUNsQyxpQkFBaUI7UUFDakI7WUFDRUMsSUFBSTtZQUNKQyxPQUFPO1lBQ1BDLE1BQU16QyxvU0FBSUE7WUFDVjBDLE1BQU07UUFDUjtRQUVBLHNDQUFzQztRQUN0QztZQUNFSCxJQUFJO1lBQ0pDLE9BQU87WUFDUEMsTUFBTXZDLG9TQUFNQTtZQUNaeUMsVUFBVTtnQkFDUjtvQkFDRUosSUFBSTtvQkFDSkMsT0FBTztvQkFDUEMsTUFBTXhDLG9TQUFNQTtvQkFDWnlDLE1BQU07b0JBQ05FLE9BQU87Z0JBQ1Q7Z0JBQ0E7b0JBQ0VMLElBQUk7b0JBQ0pDLE9BQU87b0JBQ1BDLE1BQU1kLG9TQUFNQTtvQkFDWmUsTUFBTTtvQkFDTkcsU0FBUztnQkFDWDtnQkFDQTtvQkFDRU4sSUFBSTtvQkFDSkMsT0FBTztvQkFDUEMsTUFBTXRDLG9TQUFRQTtvQkFDZHVDLE1BQU07b0JBQ05HLFNBQVM7Z0JBQ1g7Z0JBQ0E7b0JBQ0VOLElBQUk7b0JBQ0pDLE9BQU87b0JBQ1BDLE1BQU1yQyxvU0FBUUE7b0JBQ2RzQyxNQUFNO2dCQUNSO2dCQUNBO29CQUNFSCxJQUFJO29CQUNKQyxPQUFPO29CQUNQQyxNQUFNcEMsb1NBQUtBO29CQUNYcUMsTUFBTTtnQkFDUjthQUNEO1FBQ0g7UUFFQSxpQ0FBaUM7UUFDakM7WUFDRUgsSUFBSTtZQUNKQyxPQUFPO1lBQ1BDLE1BQU1uQyxxU0FBR0E7WUFDVHdDLE9BQU87Z0JBQUM7Z0JBQVM7Z0JBQWE7YUFBZ0I7WUFDOUNILFVBQVU7Z0JBQ1I7b0JBQ0VKLElBQUk7b0JBQ0pDLE9BQU87b0JBQ1BDLE1BQU10QixxU0FBSUE7b0JBQ1Z1QixNQUFNO2dCQUNSO2dCQUNBO29CQUNFSCxJQUFJO29CQUNKQyxPQUFPO29CQUNQQyxNQUFNckIscVNBQVFBO29CQUNkc0IsTUFBTTtnQkFDUjthQUNEO1FBQ0g7UUFFQSx1QkFBdUI7UUFDdkI7WUFDRUgsSUFBSTtZQUNKQyxPQUFPO1lBQ1BDLE1BQU1qQyxxU0FBS0E7WUFDWG1DLFVBQVU7Z0JBQ1I7b0JBQ0VKLElBQUk7b0JBQ0pDLE9BQU87b0JBQ1BDLE1BQU1sQyxxU0FBTUE7b0JBQ1ptQyxNQUFNO2dCQUNSO2dCQUNBO29CQUNFSCxJQUFJO29CQUNKQyxPQUFPO29CQUNQQyxNQUFNM0IscVNBQWFBO29CQUNuQjRCLE1BQU07Z0JBQ1I7YUFDRDtRQUNIO1FBRUEsMkNBQTJDO1dBQ3ZDWixDQUFBQSxpQkFBQUEsMkJBQUFBLEtBQU1pQixJQUFJLE1BQUssV0FBV2pCLENBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTWlCLElBQUksTUFBSyxnQkFBZ0I7WUFBQztnQkFDNURSLElBQUk7Z0JBQ0pDLE9BQU87Z0JBQ1BDLE1BQU1iLHFTQUFPQTtnQkFDYm9CLE9BQU87b0JBQUM7b0JBQVM7aUJBQWM7Z0JBQy9CTCxVQUFVO29CQUNSO3dCQUNFSixJQUFJO3dCQUNKQyxPQUFPO3dCQUNQQyxNQUFNakMscVNBQUtBO3dCQUNYa0MsTUFBTTt3QkFDTk0sT0FBTzs0QkFBQzs0QkFBUzt5QkFBYztvQkFDakM7b0JBQ0E7d0JBQ0VULElBQUk7d0JBQ0pDLE9BQU87d0JBQ1BDLE1BQU01QixxU0FBU0E7d0JBQ2Y2QixNQUFNO3dCQUNOTSxPQUFPOzRCQUFDOzRCQUFTO3lCQUFjO29CQUNqQztvQkFDQTt3QkFDRVQsSUFBSTt3QkFDSkMsT0FBTzt3QkFDUEMsTUFBTTdCLHFTQUFPQTt3QkFDYjhCLE1BQU07d0JBQ05NLE9BQU87NEJBQUM7NEJBQVM7eUJBQWM7b0JBQ2pDO2lCQUNEO1lBQ0g7U0FBRSxHQUFHLEVBQUU7UUFFUCx1Q0FBdUM7V0FDbkNsQixDQUFBQSxpQkFBQUEsMkJBQUFBLEtBQU1pQixJQUFJLE1BQUssZ0JBQWdCO1lBQUM7Z0JBQ2xDUixJQUFJO2dCQUNKQyxPQUFPO2dCQUNQQyxNQUFNMUIscVNBQUtBO2dCQUNYaUMsT0FBTztvQkFBQztpQkFBYztnQkFDdEJMLFVBQVU7b0JBQ1I7d0JBQ0VKLElBQUk7d0JBQ0pDLE9BQU87d0JBQ1BDLE1BQU1oQyxxU0FBUUE7d0JBQ2RpQyxNQUFNO3dCQUNOTSxPQUFPOzRCQUFDO3lCQUFjO29CQUN4QjtvQkFDQTt3QkFDRVQsSUFBSTt3QkFDSkMsT0FBTzt3QkFDUEMsTUFBTS9CLHFTQUFVQTt3QkFDaEJnQyxNQUFNO3dCQUNOTSxPQUFPOzRCQUFDO3lCQUFjO29CQUN4QjtvQkFDQTt3QkFDRVQsSUFBSTt3QkFDSkMsT0FBTzt3QkFDUEMsTUFBTTlCLHFTQUFHQTt3QkFDVCtCLE1BQU07d0JBQ05NLE9BQU87NEJBQUM7eUJBQWM7b0JBQ3hCO29CQUNBO3dCQUNFVCxJQUFJO3dCQUNKQyxPQUFPO3dCQUNQQyxNQUFNM0IscVNBQWFBO3dCQUNuQjRCLE1BQU07d0JBQ05NLE9BQU87NEJBQUM7eUJBQWM7b0JBQ3hCO29CQUNBO3dCQUNFVCxJQUFJO3dCQUNKQyxPQUFPO3dCQUNQQyxNQUFNcEIscVNBQUlBO3dCQUNWcUIsTUFBTTt3QkFDTk0sT0FBTzs0QkFBQzt5QkFBYztvQkFDeEI7b0JBQ0E7d0JBQ0VULElBQUk7d0JBQ0pDLE9BQU87d0JBQ1BDLE1BQU12QixxU0FBR0E7d0JBQ1R3QixNQUFNO3dCQUNOTSxPQUFPOzRCQUFDO3lCQUFjO29CQUN4QjtvQkFDQTt3QkFDRVQsSUFBSTt3QkFDSkMsT0FBTzt3QkFDUEMsTUFBTW5CLHFTQUFXQTt3QkFDakJvQixNQUFNO3dCQUNOTSxPQUFPOzRCQUFDO3lCQUFjO29CQUN4QjtvQkFDQTt3QkFDRVQsSUFBSTt3QkFDSkMsT0FBTzt3QkFDUEMsTUFBTXJDLG9TQUFRQTt3QkFDZHNDLE1BQU07d0JBQ05NLE9BQU87NEJBQUM7eUJBQWM7b0JBQ3hCO29CQUNBO3dCQUNFVCxJQUFJO3dCQUNKQyxPQUFPO3dCQUNQQyxNQUFNekIscVNBQU1BO3dCQUNaMEIsTUFBTTt3QkFDTk0sT0FBTzs0QkFBQzt5QkFBYztvQkFDeEI7b0JBQ0E7d0JBQ0VULElBQUk7d0JBQ0pDLE9BQU87d0JBQ1BDLE1BQU1sQixxU0FBT0E7d0JBQ2JtQixNQUFNO3dCQUNOTSxPQUFPOzRCQUFDO3lCQUFjO29CQUN4QjtpQkFDRDtZQUNIO1NBQUUsR0FBRyxFQUFFO1FBRVAsbUJBQW1CO1FBQ25CO1lBQ0VULElBQUk7WUFDSkMsT0FBTztZQUNQQyxNQUFNeEIscVNBQVNBO1lBQ2YwQixVQUFVO2dCQUNSO29CQUNFSixJQUFJO29CQUNKQyxPQUFPO29CQUNQQyxNQUFNeEIscVNBQVNBO29CQUNmeUIsTUFBTTtnQkFDUjtnQkFDQTtvQkFDRUgsSUFBSTtvQkFDSkMsT0FBTztvQkFDUEMsTUFBTS9CLHFTQUFVQTtvQkFDaEJnQyxNQUFNO2dCQUNSO2dCQUNBO29CQUNFSCxJQUFJO29CQUNKQyxPQUFPO29CQUNQQyxNQUFNaEMscVNBQVFBO29CQUNkaUMsTUFBTTtnQkFDUjthQUNEO1FBQ0g7S0FDRDtJQUVELDJDQUEyQztJQUMzQyxNQUFNTyxjQUFjLENBQUNDO1FBQ25CLE9BQU9BLE1BQU1DLE1BQU0sQ0FBQ0MsQ0FBQUE7WUFDbEIseUJBQXlCO1lBQ3pCLElBQUlBLEtBQUtKLEtBQUssSUFBSSxDQUFDSSxLQUFLSixLQUFLLENBQUNLLFFBQVEsQ0FBQ3ZCLENBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTWlCLElBQUksS0FBSSxTQUFTO2dCQUM1RCxPQUFPO1lBQ1Q7WUFFQSx5QkFBeUI7WUFDekIsSUFBSUssS0FBS04sS0FBSyxJQUFJLENBQUNNLEtBQUtOLEtBQUssQ0FBQ08sUUFBUSxDQUFDdkIsQ0FBQUEsaUJBQUFBLDJCQUFBQSxLQUFNd0IsSUFBSSxLQUFJLFNBQVM7Z0JBQzVELE9BQU87WUFDVDtZQUVBLHVCQUF1QjtZQUN2QixJQUFJRixLQUFLUCxPQUFPLElBQUlmLENBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTXdCLElBQUksTUFBSyxRQUFRO2dCQUN6QyxPQUFPO1lBQ1Q7WUFFQSw4QkFBOEI7WUFDOUIsSUFBSUYsS0FBS1QsUUFBUSxFQUFFO2dCQUNqQlMsS0FBS1QsUUFBUSxHQUFHTSxZQUFZRyxLQUFLVCxRQUFRO1lBQzNDO1lBRUEsT0FBTztRQUNUO0lBQ0Y7SUFFQSxNQUFNWSxnQkFBZ0JOLFlBQVlYO0lBRWxDLE1BQU1rQixpQkFBaUIsQ0FBQ0M7UUFDdEJwQixpQkFBaUJxQixDQUFBQSxPQUNmQSxLQUFLTCxRQUFRLENBQUNJLFVBQ1ZDLEtBQUtQLE1BQU0sQ0FBQ1osQ0FBQUEsS0FBTUEsT0FBT2tCLFVBQ3pCO21CQUFJQztnQkFBTUQ7YUFBTztJQUV6QjtJQUVBLE1BQU1FLFdBQVcsQ0FBQ2pCO1FBQ2hCLElBQUksS0FBa0IsRUFBYSxFQUFPO1FBRTFDLElBQUlBLFNBQVMsY0FBYztZQUN6QixPQUFPUCxhQUFhLGdCQUFnQixDQUFDeUIsT0FBT0MsUUFBUSxDQUFDQyxNQUFNO1FBQzdEO1FBRUEsK0JBQStCO1FBQy9CLElBQUlwQixLQUFLVyxRQUFRLENBQUMsY0FBYztZQUM5QixNQUFNVSxhQUFhLEdBQWNILE9BQVh6QixVQUFrQyxPQUF2QnlCLE9BQU9DLFFBQVEsQ0FBQ0MsTUFBTTtZQUN2RCxPQUFPQyxlQUFlckI7UUFDeEI7UUFFQSxPQUFPUCxhQUFhTyxRQUFRUCxTQUFTNkIsVUFBVSxDQUFDdEIsT0FBTztJQUN6RDtJQUVBLE1BQU11QixvQkFBb0IsU0FBQ2I7WUFBbUJjLHlFQUFnQjtZQXdEbkRkO1FBdkRULE1BQU1lLE9BQU9mLEtBQUtYLElBQUk7UUFDdEIsTUFBTTJCLGNBQWNoQixLQUFLVCxRQUFRLElBQUlTLEtBQUtULFFBQVEsQ0FBQzBCLE1BQU0sR0FBRztRQUM1RCxNQUFNQyxhQUFhbEMsY0FBY2lCLFFBQVEsQ0FBQ0QsS0FBS2IsRUFBRTtRQUNqRCxNQUFNZ0MsU0FBU25CLEtBQUtWLElBQUksR0FBR2lCLFNBQVNQLEtBQUtWLElBQUksSUFBSTtRQUVqRCxxQkFDRSw4REFBQzhCO1lBQWtCQyxXQUFXLEdBQTJCLE9BQXhCUCxRQUFRLElBQUksU0FBUzs7OEJBQ3BELDhEQUFDTTtvQkFDQ0UsU0FBUzt3QkFDUCxJQUFJTixhQUFhOzRCQUNmWixlQUFlSixLQUFLYixFQUFFO3dCQUN4QixPQUFPLElBQUlhLEtBQUtWLElBQUksRUFBRTs0QkFDcEJSLE9BQU95QyxJQUFJLENBQUN2QixLQUFLVixJQUFJO3dCQUN2QjtvQkFDRjtvQkFDQStCLFdBQVcsMEhBTVByQixPQUpBbUIsU0FDRSwyRUFDQSxvREFDSCxrQkFDd0QsT0FBdkRuQixLQUFLd0IsVUFBVSxHQUFHLGtDQUFrQyxJQUFHOztzQ0FHM0QsOERBQUNKOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ047b0NBQUtNLFdBQVcsV0FBOEMsT0FBbkNGLFNBQVMsdUJBQXVCOzs7Ozs7Z0NBQzNELENBQUN4Qyw2QkFDQTs7c0RBQ0UsOERBQUM4Qzs0Q0FBS0osV0FBVTtzREFBZXJCLEtBQUtaLEtBQUs7Ozs7Ozt3Q0FDeENZLEtBQUtSLEtBQUssa0JBQ1QsOERBQUNpQzs0Q0FBS0osV0FBVTtzREFDYnJCLEtBQUtSLEtBQUs7Ozs7Ozt3Q0FHZFEsS0FBS1AsT0FBTyxJQUFJZixDQUFBQSxpQkFBQUEsMkJBQUFBLEtBQU13QixJQUFJLE1BQUssd0JBQzlCLDhEQUFDdUI7NENBQUtKLFdBQVU7c0RBQWtFOzs7Ozs7d0NBSW5GckIsS0FBS3dCLFVBQVUsa0JBQ2QsOERBQUNDOzRDQUFLSixXQUFVO3NEQUE4RDs7Ozs7Ozs7Ozs7Ozs7d0JBUXJGLENBQUMxQyxlQUFlcUMsNkJBQ2YsOERBQUM1QyxxU0FBWUE7NEJBQUNpRCxXQUFXLDBDQUF3RSxPQUE5QkgsYUFBYSxjQUFjOzs7Ozs7Ozs7Ozs7Z0JBSWpHLENBQUN2QyxlQUFlcUMsZUFBZUUsNEJBQzlCLDhEQUFDRTtvQkFBSUMsV0FBVTsrQkFDWnJCLGlCQUFBQSxLQUFLVCxRQUFRLGNBQWJTLHFDQUFBQSxlQUFlMEIsR0FBRyxDQUFDQyxDQUFBQSxRQUFTZCxrQkFBa0JjLE9BQU9iLFFBQVE7Ozs7Ozs7V0FqRDFEZCxLQUFLYixFQUFFOzs7OztJQXNEckI7SUFFQSxxQkFDRSw4REFBQ2lDO1FBQUlDLFdBQVcsaUdBRWtCLE9BQTlCMUMsY0FBYyxTQUFTLFFBQU87OzBCQUdoQyw4REFBQ3lDO2dCQUFJQyxXQUFVOzBCQUNiLDRFQUFDRDtvQkFBSUMsV0FBVTs7d0JBQ1osQ0FBQzFDLDZCQUNBLDhEQUFDeUM7OzhDQUNDLDhEQUFDUTtvQ0FBR1AsV0FBVTs4Q0FBK0I7Ozs7Ozs4Q0FDN0MsOERBQUNRO29DQUFFUixXQUFVOzt3Q0FDVjNDLENBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTWlCLElBQUksTUFBSyxnQkFBZ0IsNkJBQy9CakIsQ0FBQUEsaUJBQUFBLDJCQUFBQSxLQUFNaUIsSUFBSSxNQUFLLFVBQVUsd0JBQ3pCO3dDQUFVO3dDQUFJakIsaUJBQUFBLDJCQUFBQSxLQUFNd0IsSUFBSTs7Ozs7Ozs7Ozs7OztzQ0FJL0IsOERBQUM0Qjs0QkFDQ1IsU0FBUzFDOzRCQUNUeUMsV0FBVTtzQ0FFVDFDLDRCQUFjLDhEQUFDTixxU0FBSUE7Z0NBQUNnRCxXQUFVOzs7OztxREFBZSw4REFBQy9DLHFTQUFDQTtnQ0FBQytDLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBTWpFLDhEQUFDRDtnQkFBSUMsV0FBVTswQkFDWmxCLGNBQWN1QixHQUFHLENBQUMxQixDQUFBQSxPQUFRYSxrQkFBa0JiOzs7Ozs7MEJBSS9DLDhEQUFDb0I7Z0JBQUlDLFdBQVU7MEJBQ1osQ0FBQzFDLDZCQUNBLDhEQUFDeUM7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDUTtzQ0FBRTs7Ozs7O3NDQUNILDhEQUFDQTtzQ0FBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFNZjtHQTlZd0JwRDs7UUFDUC9CLHNEQUFTQTtRQUNQQyx3REFBV0E7OztLQUZOOEIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vY29tcG9uZW50cy9EYXNoYm9hcmRTaWRlYmFyLnRzeD8yNjFlIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgeyB1c2VSb3V0ZXIsIHVzZVBhdGhuYW1lIH0gZnJvbSAnbmV4dC9uYXZpZ2F0aW9uJ1xuaW1wb3J0IHtcbiAgSG9tZSxcbiAgU2VhcmNoLFxuICBTaGllbGQsXG4gIEZpbGVUZXh0LFxuICBEYXRhYmFzZSxcbiAgR2xvYmUsXG4gIFphcCxcbiAgVHJvcGh5LFxuICBVc2VycyxcbiAgU2V0dGluZ3MsXG4gIENyZWRpdENhcmQsXG4gIEJvdCxcbiAgTW9uaXRvcixcbiAgQmFyQ2hhcnQzLFxuICBNZXNzYWdlU3F1YXJlLFxuICBDcm93bixcbiAgU2VydmVyLFxuICBBY3Rpdml0eSxcbiAgVXNlckNoZWNrLFxuICBDb2csXG4gIENvZGUsXG4gIFRlcm1pbmFsLFxuICBTZW5kLFxuICBTaGllbGRDaGVjayxcbiAgV2ViaG9vayxcbiAgU3RhcixcbiAgQ2hldnJvblJpZ2h0LFxuICBNZW51LFxuICBYLFxuICBUYXJnZXQsXG4gIFVzZXJDb2dcbn0gZnJvbSAnbHVjaWRlLXJlYWN0J1xuXG5pbnRlcmZhY2UgVXNlciB7XG4gIGlkOiBzdHJpbmdcbiAgdXNlcm5hbWU6IHN0cmluZ1xuICBlbWFpbDogc3RyaW5nXG4gIHJvbGU6ICd1c2VyJyB8ICdhZG1pbicgfCAnc3VwZXJfYWRtaW4nIHwgJ21vZGVyYXRvcidcbiAgcGxhbjogJ0ZyZWUnIHwgJ1N0dWRlbnQnIHwgJ0hvYmJ5JyB8ICdCdWdodW50ZXInIHwgJ0N5YmVyc2VjdXJpdHknXG4gIGxldmVsOiBudW1iZXJcbiAgc2NvcmU6IG51bWJlclxufVxuXG5pbnRlcmZhY2UgU2lkZWJhckl0ZW0ge1xuICBpZDogc3RyaW5nXG4gIHRpdGxlOiBzdHJpbmdcbiAgaWNvbjogYW55XG4gIGhyZWY/OiBzdHJpbmdcbiAgYmFkZ2U/OiBzdHJpbmdcbiAgY2hpbGRyZW4/OiBTaWRlYmFySXRlbVtdXG4gIHJvbGVzPzogc3RyaW5nW11cbiAgcGxhbnM/OiBzdHJpbmdbXVxuICBwcmVtaXVtPzogYm9vbGVhblxuICBjb21pbmdTb29uPzogYm9vbGVhblxufVxuXG5pbnRlcmZhY2UgRGFzaGJvYXJkU2lkZWJhclByb3BzIHtcbiAgdXNlcjogVXNlciB8IG51bGxcbiAgaXNDb2xsYXBzZWQ6IGJvb2xlYW5cbiAgb25Ub2dnbGU6ICgpID0+IHZvaWRcbiAgaXNNb2JpbGU/OiBib29sZWFuXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIERhc2hib2FyZFNpZGViYXIoeyB1c2VyLCBpc0NvbGxhcHNlZCwgb25Ub2dnbGUsIGlzTW9iaWxlID0gZmFsc2UgfTogRGFzaGJvYXJkU2lkZWJhclByb3BzKSB7XG4gIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpXG4gIGNvbnN0IHBhdGhuYW1lID0gdXNlUGF0aG5hbWUoKVxuICBjb25zdCBbZXhwYW5kZWRJdGVtcywgc2V0RXhwYW5kZWRJdGVtc10gPSB1c2VTdGF0ZTxzdHJpbmdbXT4oW10pXG5cbiAgLy8gRGVmaW5lIHNpZGViYXIgaXRlbXMgYmFzZWQgb24gdXNlciByb2xlIGFuZCBwbGFuXG4gIGNvbnN0IHNpZGViYXJJdGVtczogU2lkZWJhckl0ZW1bXSA9IFtcbiAgICAvLyBNYWluIERhc2hib2FyZFxuICAgIHtcbiAgICAgIGlkOiAnZGFzaGJvYXJkJyxcbiAgICAgIHRpdGxlOiAnRGFzaGJvYXJkJyxcbiAgICAgIGljb246IEhvbWUsXG4gICAgICBocmVmOiAnL2Rhc2hib2FyZCdcbiAgICB9LFxuXG4gICAgLy8gQ29yZSBUb29scyAoQXZhaWxhYmxlIHRvIGFsbCB1c2VycylcbiAgICB7XG4gICAgICBpZDogJ3Rvb2xzJyxcbiAgICAgIHRpdGxlOiAnU2VjdXJpdHkgVG9vbHMnLFxuICAgICAgaWNvbjogU2hpZWxkLFxuICAgICAgY2hpbGRyZW46IFtcbiAgICAgICAge1xuICAgICAgICAgIGlkOiAnb3NpbnQnLFxuICAgICAgICAgIHRpdGxlOiAnT1NJTlQgTG9va3VwJyxcbiAgICAgICAgICBpY29uOiBTZWFyY2gsXG4gICAgICAgICAgaHJlZjogJy9vc2ludCcsXG4gICAgICAgICAgYmFkZ2U6ICdQb3B1bGFyJ1xuICAgICAgICB9LFxuICAgICAgICB7XG4gICAgICAgICAgaWQ6ICdzY2FubmVyJyxcbiAgICAgICAgICB0aXRsZTogJ1Z1bG5lcmFiaWxpdHkgU2Nhbm5lcicsXG4gICAgICAgICAgaWNvbjogVGFyZ2V0LFxuICAgICAgICAgIGhyZWY6ICcvc2Nhbm5lcicsXG4gICAgICAgICAgcHJlbWl1bTogdHJ1ZVxuICAgICAgICB9LFxuICAgICAgICB7XG4gICAgICAgICAgaWQ6ICdmaWxlLWFuYWx5emVyJyxcbiAgICAgICAgICB0aXRsZTogJ0ZpbGUgQW5hbHl6ZXInLFxuICAgICAgICAgIGljb246IEZpbGVUZXh0LFxuICAgICAgICAgIGhyZWY6ICcvZmlsZS1hbmFseXplcicsXG4gICAgICAgICAgcHJlbWl1bTogdHJ1ZVxuICAgICAgICB9LFxuICAgICAgICB7XG4gICAgICAgICAgaWQ6ICdjdmUnLFxuICAgICAgICAgIHRpdGxlOiAnQ1ZFIERhdGFiYXNlJyxcbiAgICAgICAgICBpY29uOiBEYXRhYmFzZSxcbiAgICAgICAgICBocmVmOiAnL2N2ZSdcbiAgICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICAgIGlkOiAnZG9ya2luZycsXG4gICAgICAgICAgdGl0bGU6ICdHb29nbGUgRG9ya2luZycsXG4gICAgICAgICAgaWNvbjogR2xvYmUsXG4gICAgICAgICAgaHJlZjogJy9kb3JraW5nJ1xuICAgICAgICB9XG4gICAgICBdXG4gICAgfSxcblxuICAgIC8vIEFkdmFuY2VkIFRvb2xzIChQcmVtaXVtIHBsYW5zKVxuICAgIHtcbiAgICAgIGlkOiAnYWR2YW5jZWQnLFxuICAgICAgdGl0bGU6ICdBZHZhbmNlZCBUb29scycsXG4gICAgICBpY29uOiBaYXAsXG4gICAgICBwbGFuczogWydIb2JieScsICdCdWdodW50ZXInLCAnQ3liZXJzZWN1cml0eSddLFxuICAgICAgY2hpbGRyZW46IFtcbiAgICAgICAge1xuICAgICAgICAgIGlkOiAncGxheWdyb3VuZCcsXG4gICAgICAgICAgdGl0bGU6ICdBUEkgUGxheWdyb3VuZCcsXG4gICAgICAgICAgaWNvbjogQ29kZSxcbiAgICAgICAgICBocmVmOiAnL3BsYXlncm91bmQnXG4gICAgICAgIH0sXG4gICAgICAgIHtcbiAgICAgICAgICBpZDogJ3Rvb2xzLWFkdmFuY2VkJyxcbiAgICAgICAgICB0aXRsZTogJ0FkdmFuY2VkIFRvb2xzJyxcbiAgICAgICAgICBpY29uOiBUZXJtaW5hbCxcbiAgICAgICAgICBocmVmOiAnL3Rvb2xzJ1xuICAgICAgICB9XG4gICAgICBdXG4gICAgfSxcblxuICAgIC8vIENvbW11bml0eSAmIExlYXJuaW5nXG4gICAge1xuICAgICAgaWQ6ICdjb21tdW5pdHknLFxuICAgICAgdGl0bGU6ICdDb21tdW5pdHknLFxuICAgICAgaWNvbjogVXNlcnMsXG4gICAgICBjaGlsZHJlbjogW1xuICAgICAgICB7XG4gICAgICAgICAgaWQ6ICdsZWFkZXJib2FyZCcsXG4gICAgICAgICAgdGl0bGU6ICdMZWFkZXJib2FyZCcsXG4gICAgICAgICAgaWNvbjogVHJvcGh5LFxuICAgICAgICAgIGhyZWY6ICcvbGVhZGVyYm9hcmQnXG4gICAgICAgIH0sXG4gICAgICAgIHtcbiAgICAgICAgICBpZDogJ2NvbW11bml0eS1odWInLFxuICAgICAgICAgIHRpdGxlOiAnQ29tbXVuaXR5IEh1YicsXG4gICAgICAgICAgaWNvbjogTWVzc2FnZVNxdWFyZSxcbiAgICAgICAgICBocmVmOiAnL2NvbW11bml0eSdcbiAgICAgICAgfVxuICAgICAgXVxuICAgIH0sXG5cbiAgICAvLyBBZG1pbiBUb29scyAoQWRtaW4gYW5kIFN1cGVyIEFkbWluIG9ubHkpXG4gICAgLi4uKHVzZXI/LnJvbGUgPT09ICdhZG1pbicgfHwgdXNlcj8ucm9sZSA9PT0gJ3N1cGVyX2FkbWluJyA/IFt7XG4gICAgICBpZDogJ2FkbWluJyxcbiAgICAgIHRpdGxlOiAnQWRtaW5pc3RyYXRpb24nLFxuICAgICAgaWNvbjogVXNlckNvZyxcbiAgICAgIHJvbGVzOiBbJ2FkbWluJywgJ3N1cGVyX2FkbWluJ10sXG4gICAgICBjaGlsZHJlbjogW1xuICAgICAgICB7XG4gICAgICAgICAgaWQ6ICd1c2VyLW1hbmFnZW1lbnQnLFxuICAgICAgICAgIHRpdGxlOiAnVXNlciBNYW5hZ2VtZW50JyxcbiAgICAgICAgICBpY29uOiBVc2VycyxcbiAgICAgICAgICBocmVmOiAnL2Rhc2hib2FyZD9zZWN0aW9uPXVzZXJzJyxcbiAgICAgICAgICByb2xlczogWydhZG1pbicsICdzdXBlcl9hZG1pbiddXG4gICAgICAgIH0sXG4gICAgICAgIHtcbiAgICAgICAgICBpZDogJ2FuYWx5dGljcycsXG4gICAgICAgICAgdGl0bGU6ICdBbmFseXRpY3MnLFxuICAgICAgICAgIGljb246IEJhckNoYXJ0MyxcbiAgICAgICAgICBocmVmOiAnL2Rhc2hib2FyZD9zZWN0aW9uPWFuYWx5dGljcycsXG4gICAgICAgICAgcm9sZXM6IFsnYWRtaW4nLCAnc3VwZXJfYWRtaW4nXVxuICAgICAgICB9LFxuICAgICAgICB7XG4gICAgICAgICAgaWQ6ICdtb25pdG9yaW5nJyxcbiAgICAgICAgICB0aXRsZTogJ1N5c3RlbSBNb25pdG9yJyxcbiAgICAgICAgICBpY29uOiBNb25pdG9yLFxuICAgICAgICAgIGhyZWY6ICcvZGFzaGJvYXJkP3NlY3Rpb249bW9uaXRvcmluZycsXG4gICAgICAgICAgcm9sZXM6IFsnYWRtaW4nLCAnc3VwZXJfYWRtaW4nXVxuICAgICAgICB9XG4gICAgICBdXG4gICAgfV0gOiBbXSksXG5cbiAgICAvLyBTdXBlciBBZG1pbiBUb29scyAoU3VwZXIgQWRtaW4gb25seSlcbiAgICAuLi4odXNlcj8ucm9sZSA9PT0gJ3N1cGVyX2FkbWluJyA/IFt7XG4gICAgICBpZDogJ3N1cGVyYWRtaW4nLFxuICAgICAgdGl0bGU6ICdTdXBlciBBZG1pbicsXG4gICAgICBpY29uOiBDcm93bixcbiAgICAgIHJvbGVzOiBbJ3N1cGVyX2FkbWluJ10sXG4gICAgICBjaGlsZHJlbjogW1xuICAgICAgICB7XG4gICAgICAgICAgaWQ6ICd3ZWJzaXRlLXNldHRpbmdzJyxcbiAgICAgICAgICB0aXRsZTogJ1dlYnNpdGUgU2V0dGluZ3MnLFxuICAgICAgICAgIGljb246IFNldHRpbmdzLFxuICAgICAgICAgIGhyZWY6ICcvZGFzaGJvYXJkP3NlY3Rpb249d2Vic2l0ZS1zZXR0aW5ncycsXG4gICAgICAgICAgcm9sZXM6IFsnc3VwZXJfYWRtaW4nXVxuICAgICAgICB9LFxuICAgICAgICB7XG4gICAgICAgICAgaWQ6ICdwYXltZW50LW1hbmFnZW1lbnQnLFxuICAgICAgICAgIHRpdGxlOiAnUGF5bWVudCBNYW5hZ2VtZW50JyxcbiAgICAgICAgICBpY29uOiBDcmVkaXRDYXJkLFxuICAgICAgICAgIGhyZWY6ICcvZGFzaGJvYXJkP3NlY3Rpb249cGF5bWVudHMnLFxuICAgICAgICAgIHJvbGVzOiBbJ3N1cGVyX2FkbWluJ11cbiAgICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICAgIGlkOiAnYm90LW1hbmFnZW1lbnQnLFxuICAgICAgICAgIHRpdGxlOiAnQm90IE1hbmFnZW1lbnQnLFxuICAgICAgICAgIGljb246IEJvdCxcbiAgICAgICAgICBocmVmOiAnL2Rhc2hib2FyZD9zZWN0aW9uPWJvdHMnLFxuICAgICAgICAgIHJvbGVzOiBbJ3N1cGVyX2FkbWluJ11cbiAgICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICAgIGlkOiAnd2hhdHNhcHAtYm90JyxcbiAgICAgICAgICB0aXRsZTogJ1doYXRzQXBwIEJvdCcsXG4gICAgICAgICAgaWNvbjogTWVzc2FnZVNxdWFyZSxcbiAgICAgICAgICBocmVmOiAnL2Rhc2hib2FyZD9zZWN0aW9uPXdoYXRzYXBwJyxcbiAgICAgICAgICByb2xlczogWydzdXBlcl9hZG1pbiddXG4gICAgICAgIH0sXG4gICAgICAgIHtcbiAgICAgICAgICBpZDogJ3RlbGVncmFtLWJvdCcsXG4gICAgICAgICAgdGl0bGU6ICdUZWxlZ3JhbSBCb3QnLFxuICAgICAgICAgIGljb246IFNlbmQsXG4gICAgICAgICAgaHJlZjogJy9kYXNoYm9hcmQ/c2VjdGlvbj10ZWxlZ3JhbScsXG4gICAgICAgICAgcm9sZXM6IFsnc3VwZXJfYWRtaW4nXVxuICAgICAgICB9LFxuICAgICAgICB7XG4gICAgICAgICAgaWQ6ICdzeXN0ZW0tY29uZmlnJyxcbiAgICAgICAgICB0aXRsZTogJ1N5c3RlbSBDb25maWcnLFxuICAgICAgICAgIGljb246IENvZyxcbiAgICAgICAgICBocmVmOiAnL2Rhc2hib2FyZD9zZWN0aW9uPXN5c3RlbS1jb25maWcnLFxuICAgICAgICAgIHJvbGVzOiBbJ3N1cGVyX2FkbWluJ11cbiAgICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICAgIGlkOiAnc2VjdXJpdHktY2VudGVyJyxcbiAgICAgICAgICB0aXRsZTogJ1NlY3VyaXR5IENlbnRlcicsXG4gICAgICAgICAgaWNvbjogU2hpZWxkQ2hlY2ssXG4gICAgICAgICAgaHJlZjogJy9kYXNoYm9hcmQ/c2VjdGlvbj1zZWN1cml0eScsXG4gICAgICAgICAgcm9sZXM6IFsnc3VwZXJfYWRtaW4nXVxuICAgICAgICB9LFxuICAgICAgICB7XG4gICAgICAgICAgaWQ6ICdkYXRhYmFzZS1hZG1pbicsXG4gICAgICAgICAgdGl0bGU6ICdEYXRhYmFzZSBBZG1pbicsXG4gICAgICAgICAgaWNvbjogRGF0YWJhc2UsXG4gICAgICAgICAgaHJlZjogJy9kYXNoYm9hcmQ/c2VjdGlvbj1kYXRhYmFzZScsXG4gICAgICAgICAgcm9sZXM6IFsnc3VwZXJfYWRtaW4nXVxuICAgICAgICB9LFxuICAgICAgICB7XG4gICAgICAgICAgaWQ6ICdzZXJ2ZXItbWFuYWdlbWVudCcsXG4gICAgICAgICAgdGl0bGU6ICdTZXJ2ZXIgTWFuYWdlbWVudCcsXG4gICAgICAgICAgaWNvbjogU2VydmVyLFxuICAgICAgICAgIGhyZWY6ICcvZGFzaGJvYXJkP3NlY3Rpb249c2VydmVyJyxcbiAgICAgICAgICByb2xlczogWydzdXBlcl9hZG1pbiddXG4gICAgICAgIH0sXG4gICAgICAgIHtcbiAgICAgICAgICBpZDogJ2FwaS1tYW5hZ2VtZW50JyxcbiAgICAgICAgICB0aXRsZTogJ0FQSSBNYW5hZ2VtZW50JyxcbiAgICAgICAgICBpY29uOiBXZWJob29rLFxuICAgICAgICAgIGhyZWY6ICcvZGFzaGJvYXJkP3NlY3Rpb249YXBpJyxcbiAgICAgICAgICByb2xlczogWydzdXBlcl9hZG1pbiddXG4gICAgICAgIH1cbiAgICAgIF1cbiAgICB9XSA6IFtdKSxcblxuICAgIC8vIFBlcnNvbmFsIFNlY3Rpb25cbiAgICB7XG4gICAgICBpZDogJ3BlcnNvbmFsJyxcbiAgICAgIHRpdGxlOiAnUGVyc29uYWwnLFxuICAgICAgaWNvbjogVXNlckNoZWNrLFxuICAgICAgY2hpbGRyZW46IFtcbiAgICAgICAge1xuICAgICAgICAgIGlkOiAncHJvZmlsZScsXG4gICAgICAgICAgdGl0bGU6ICdQcm9maWxlJyxcbiAgICAgICAgICBpY29uOiBVc2VyQ2hlY2ssXG4gICAgICAgICAgaHJlZjogJy9wcm9maWxlJ1xuICAgICAgICB9LFxuICAgICAgICB7XG4gICAgICAgICAgaWQ6ICdwbGFuJyxcbiAgICAgICAgICB0aXRsZTogJ1N1YnNjcmlwdGlvbicsXG4gICAgICAgICAgaWNvbjogQ3JlZGl0Q2FyZCxcbiAgICAgICAgICBocmVmOiAnL3BsYW4nXG4gICAgICAgIH0sXG4gICAgICAgIHtcbiAgICAgICAgICBpZDogJ3NldHRpbmdzJyxcbiAgICAgICAgICB0aXRsZTogJ1NldHRpbmdzJyxcbiAgICAgICAgICBpY29uOiBTZXR0aW5ncyxcbiAgICAgICAgICBocmVmOiAnL3NldHRpbmdzJ1xuICAgICAgICB9XG4gICAgICBdXG4gICAgfVxuICBdXG5cbiAgLy8gRmlsdGVyIGl0ZW1zIGJhc2VkIG9uIHVzZXIgcm9sZSBhbmQgcGxhblxuICBjb25zdCBmaWx0ZXJJdGVtcyA9IChpdGVtczogU2lkZWJhckl0ZW1bXSk6IFNpZGViYXJJdGVtW10gPT4ge1xuICAgIHJldHVybiBpdGVtcy5maWx0ZXIoaXRlbSA9PiB7XG4gICAgICAvLyBDaGVjayByb2xlIHBlcm1pc3Npb25zXG4gICAgICBpZiAoaXRlbS5yb2xlcyAmJiAhaXRlbS5yb2xlcy5pbmNsdWRlcyh1c2VyPy5yb2xlIHx8ICd1c2VyJykpIHtcbiAgICAgICAgcmV0dXJuIGZhbHNlXG4gICAgICB9XG5cbiAgICAgIC8vIENoZWNrIHBsYW4gcGVybWlzc2lvbnNcbiAgICAgIGlmIChpdGVtLnBsYW5zICYmICFpdGVtLnBsYW5zLmluY2x1ZGVzKHVzZXI/LnBsYW4gfHwgJ0ZyZWUnKSkge1xuICAgICAgICByZXR1cm4gZmFsc2VcbiAgICAgIH1cblxuICAgICAgLy8gQ2hlY2sgcHJlbWl1bSBhY2Nlc3NcbiAgICAgIGlmIChpdGVtLnByZW1pdW0gJiYgdXNlcj8ucGxhbiA9PT0gJ0ZyZWUnKSB7XG4gICAgICAgIHJldHVybiBmYWxzZVxuICAgICAgfVxuXG4gICAgICAvLyBGaWx0ZXIgY2hpbGRyZW4gcmVjdXJzaXZlbHlcbiAgICAgIGlmIChpdGVtLmNoaWxkcmVuKSB7XG4gICAgICAgIGl0ZW0uY2hpbGRyZW4gPSBmaWx0ZXJJdGVtcyhpdGVtLmNoaWxkcmVuKVxuICAgICAgfVxuXG4gICAgICByZXR1cm4gdHJ1ZVxuICAgIH0pXG4gIH1cblxuICBjb25zdCBmaWx0ZXJlZEl0ZW1zID0gZmlsdGVySXRlbXMoc2lkZWJhckl0ZW1zKVxuXG4gIGNvbnN0IHRvZ2dsZUV4cGFuZGVkID0gKGl0ZW1JZDogc3RyaW5nKSA9PiB7XG4gICAgc2V0RXhwYW5kZWRJdGVtcyhwcmV2ID0+XG4gICAgICBwcmV2LmluY2x1ZGVzKGl0ZW1JZClcbiAgICAgICAgPyBwcmV2LmZpbHRlcihpZCA9PiBpZCAhPT0gaXRlbUlkKVxuICAgICAgICA6IFsuLi5wcmV2LCBpdGVtSWRdXG4gICAgKVxuICB9XG5cbiAgY29uc3QgaXNBY3RpdmUgPSAoaHJlZjogc3RyaW5nKSA9PiB7XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgPT09ICd1bmRlZmluZWQnKSByZXR1cm4gZmFsc2VcblxuICAgIGlmIChocmVmID09PSAnL2Rhc2hib2FyZCcpIHtcbiAgICAgIHJldHVybiBwYXRobmFtZSA9PT0gJy9kYXNoYm9hcmQnICYmICF3aW5kb3cubG9jYXRpb24uc2VhcmNoXG4gICAgfVxuXG4gICAgLy8gQ2hlY2sgZm9yIHNlY3Rpb24tYmFzZWQgVVJMc1xuICAgIGlmIChocmVmLmluY2x1ZGVzKCc/c2VjdGlvbj0nKSkge1xuICAgICAgY29uc3QgY3VycmVudFVybCA9IGAke3BhdGhuYW1lfSR7d2luZG93LmxvY2F0aW9uLnNlYXJjaH1gXG4gICAgICByZXR1cm4gY3VycmVudFVybCA9PT0gaHJlZlxuICAgIH1cblxuICAgIHJldHVybiBwYXRobmFtZSA9PT0gaHJlZiB8fCBwYXRobmFtZS5zdGFydHNXaXRoKGhyZWYgKyAnLycpXG4gIH1cblxuICBjb25zdCByZW5kZXJTaWRlYmFySXRlbSA9IChpdGVtOiBTaWRlYmFySXRlbSwgbGV2ZWw6IG51bWJlciA9IDApID0+IHtcbiAgICBjb25zdCBJY29uID0gaXRlbS5pY29uXG4gICAgY29uc3QgaGFzQ2hpbGRyZW4gPSBpdGVtLmNoaWxkcmVuICYmIGl0ZW0uY2hpbGRyZW4ubGVuZ3RoID4gMFxuICAgIGNvbnN0IGlzRXhwYW5kZWQgPSBleHBhbmRlZEl0ZW1zLmluY2x1ZGVzKGl0ZW0uaWQpXG4gICAgY29uc3QgYWN0aXZlID0gaXRlbS5ocmVmID8gaXNBY3RpdmUoaXRlbS5ocmVmKSA6IGZhbHNlXG5cbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBrZXk9e2l0ZW0uaWR9IGNsYXNzTmFtZT17YCR7bGV2ZWwgPiAwID8gJ21sLTQnIDogJyd9YH0+XG4gICAgICAgIDxkaXZcbiAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XG4gICAgICAgICAgICBpZiAoaGFzQ2hpbGRyZW4pIHtcbiAgICAgICAgICAgICAgdG9nZ2xlRXhwYW5kZWQoaXRlbS5pZClcbiAgICAgICAgICAgIH0gZWxzZSBpZiAoaXRlbS5ocmVmKSB7XG4gICAgICAgICAgICAgIHJvdXRlci5wdXNoKGl0ZW0uaHJlZilcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9fVxuICAgICAgICAgIGNsYXNzTmFtZT17YFxuICAgICAgICAgICAgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHAtMyByb3VuZGVkLWxnIGN1cnNvci1wb2ludGVyIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMFxuICAgICAgICAgICAgJHthY3RpdmVcbiAgICAgICAgICAgICAgPyAnYmctY3liZXItcHJpbWFyeS8yMCB0ZXh0LWN5YmVyLXByaW1hcnkgYm9yZGVyLWwtNCBib3JkZXItY3liZXItcHJpbWFyeSdcbiAgICAgICAgICAgICAgOiAndGV4dC1ncmF5LTMwMCBob3ZlcjpiZy1ncmF5LTgwMCBob3Zlcjp0ZXh0LXdoaXRlJ1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgJHtpdGVtLmNvbWluZ1Nvb24gPyAnb3BhY2l0eS01MCBjdXJzb3Itbm90LWFsbG93ZWQnIDogJyd9XG4gICAgICAgICAgYH1cbiAgICAgICAgPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zXCI+XG4gICAgICAgICAgICA8SWNvbiBjbGFzc05hbWU9e2BoLTUgdy01ICR7YWN0aXZlID8gJ3RleHQtY3liZXItcHJpbWFyeScgOiAnJ31gfSAvPlxuICAgICAgICAgICAgeyFpc0NvbGxhcHNlZCAmJiAoXG4gICAgICAgICAgICAgIDw+XG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1tZWRpdW1cIj57aXRlbS50aXRsZX08L3NwYW4+XG4gICAgICAgICAgICAgICAge2l0ZW0uYmFkZ2UgJiYgKFxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwicHgtMiBweS0xIHRleHQteHMgYmctY3liZXItcHJpbWFyeS8yMCB0ZXh0LWN5YmVyLXByaW1hcnkgcm91bmRlZC1mdWxsXCI+XG4gICAgICAgICAgICAgICAgICAgIHtpdGVtLmJhZGdlfVxuICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAge2l0ZW0ucHJlbWl1bSAmJiB1c2VyPy5wbGFuID09PSAnRnJlZScgJiYgKFxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwicHgtMiBweS0xIHRleHQteHMgYmcteWVsbG93LTUwMC8yMCB0ZXh0LXllbGxvdy00MDAgcm91bmRlZC1mdWxsXCI+XG4gICAgICAgICAgICAgICAgICAgIFBST1xuICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAge2l0ZW0uY29taW5nU29vbiAmJiAoXG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJweC0yIHB5LTEgdGV4dC14cyBiZy1ncmF5LTUwMC8yMCB0ZXh0LWdyYXktNDAwIHJvdW5kZWQtZnVsbFwiPlxuICAgICAgICAgICAgICAgICAgICBTb29uXG4gICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgPC8+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgeyFpc0NvbGxhcHNlZCAmJiBoYXNDaGlsZHJlbiAmJiAoXG4gICAgICAgICAgICA8Q2hldnJvblJpZ2h0IGNsYXNzTmFtZT17YHctNCBoLTQgdHJhbnNmb3JtIHRyYW5zaXRpb24tdHJhbnNmb3JtICR7aXNFeHBhbmRlZCA/ICdyb3RhdGUtOTAnIDogJyd9YH0gLz5cbiAgICAgICAgICApfVxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7IWlzQ29sbGFwc2VkICYmIGhhc0NoaWxkcmVuICYmIGlzRXhwYW5kZWQgJiYgKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtMiBzcGFjZS15LTFcIj5cbiAgICAgICAgICAgIHtpdGVtLmNoaWxkcmVuPy5tYXAoY2hpbGQgPT4gcmVuZGVyU2lkZWJhckl0ZW0oY2hpbGQsIGxldmVsICsgMSkpfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuICAgICAgPC9kaXY+XG4gICAgKVxuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT17YFxuICAgICAgYmctZ3JheS05MDAgYm9yZGVyLXIgYm9yZGVyLWdyYXktODAwIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCBmbGV4IGZsZXgtY29sXG4gICAgICAke2lzQ29sbGFwc2VkID8gJ3ctMTYnIDogJ3ctNjQnfVxuICAgIGB9PlxuICAgICAgey8qIEhlYWRlciAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC00IGJvcmRlci1iIGJvcmRlci1ncmF5LTgwMFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgIHshaXNDb2xsYXBzZWQgJiYgKFxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1ib2xkIHRleHQtd2hpdGVcIj5Lb2RlWEd1YXJkPC9oMj5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNDAwXCI+XG4gICAgICAgICAgICAgICAge3VzZXI/LnJvbGUgPT09ICdzdXBlcl9hZG1pbicgPyAn8J+RkSBTdXBlciBBZG1pbicgOlxuICAgICAgICAgICAgICAgICB1c2VyPy5yb2xlID09PSAnYWRtaW4nID8gJ/Cfm6HvuI8gQWRtaW4nIDpcbiAgICAgICAgICAgICAgICAgJ/CflJIgVXNlcid9IOKAoiB7dXNlcj8ucGxhbn1cbiAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKX1cbiAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICBvbkNsaWNrPXtvblRvZ2dsZX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cInAtMiByb3VuZGVkLWxnIGhvdmVyOmJnLWdyYXktODAwIHRleHQtZ3JheS00MDAgaG92ZXI6dGV4dC13aGl0ZSB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgPlxuICAgICAgICAgICAge2lzQ29sbGFwc2VkID8gPE1lbnUgY2xhc3NOYW1lPVwidy01IGgtNVwiIC8+IDogPFggY2xhc3NOYW1lPVwidy01IGgtNVwiIC8+fVxuICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogTmF2aWdhdGlvbiAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIG92ZXJmbG93LXktYXV0byBwLTQgc3BhY2UteS0yXCI+XG4gICAgICAgIHtmaWx0ZXJlZEl0ZW1zLm1hcChpdGVtID0+IHJlbmRlclNpZGViYXJJdGVtKGl0ZW0pKX1cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogRm9vdGVyICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTQgYm9yZGVyLXQgYm9yZGVyLWdyYXktODAwXCI+XG4gICAgICAgIHshaXNDb2xsYXBzZWQgJiYgKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwIHRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICA8cD7CqSAyMDI0IEtvZGVYR3VhcmQ8L3A+XG4gICAgICAgICAgICA8cD5DeWJlcnNlY3VyaXR5IFBsYXRmb3JtPC9wPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZVJvdXRlciIsInVzZVBhdGhuYW1lIiwiSG9tZSIsIlNlYXJjaCIsIlNoaWVsZCIsIkZpbGVUZXh0IiwiRGF0YWJhc2UiLCJHbG9iZSIsIlphcCIsIlRyb3BoeSIsIlVzZXJzIiwiU2V0dGluZ3MiLCJDcmVkaXRDYXJkIiwiQm90IiwiTW9uaXRvciIsIkJhckNoYXJ0MyIsIk1lc3NhZ2VTcXVhcmUiLCJDcm93biIsIlNlcnZlciIsIlVzZXJDaGVjayIsIkNvZyIsIkNvZGUiLCJUZXJtaW5hbCIsIlNlbmQiLCJTaGllbGRDaGVjayIsIldlYmhvb2siLCJDaGV2cm9uUmlnaHQiLCJNZW51IiwiWCIsIlRhcmdldCIsIlVzZXJDb2ciLCJEYXNoYm9hcmRTaWRlYmFyIiwidXNlciIsImlzQ29sbGFwc2VkIiwib25Ub2dnbGUiLCJpc01vYmlsZSIsInJvdXRlciIsInBhdGhuYW1lIiwiZXhwYW5kZWRJdGVtcyIsInNldEV4cGFuZGVkSXRlbXMiLCJzaWRlYmFySXRlbXMiLCJpZCIsInRpdGxlIiwiaWNvbiIsImhyZWYiLCJjaGlsZHJlbiIsImJhZGdlIiwicHJlbWl1bSIsInBsYW5zIiwicm9sZSIsInJvbGVzIiwiZmlsdGVySXRlbXMiLCJpdGVtcyIsImZpbHRlciIsIml0ZW0iLCJpbmNsdWRlcyIsInBsYW4iLCJmaWx0ZXJlZEl0ZW1zIiwidG9nZ2xlRXhwYW5kZWQiLCJpdGVtSWQiLCJwcmV2IiwiaXNBY3RpdmUiLCJ3aW5kb3ciLCJsb2NhdGlvbiIsInNlYXJjaCIsImN1cnJlbnRVcmwiLCJzdGFydHNXaXRoIiwicmVuZGVyU2lkZWJhckl0ZW0iLCJsZXZlbCIsIkljb24iLCJoYXNDaGlsZHJlbiIsImxlbmd0aCIsImlzRXhwYW5kZWQiLCJhY3RpdmUiLCJkaXYiLCJjbGFzc05hbWUiLCJvbkNsaWNrIiwicHVzaCIsImNvbWluZ1Nvb24iLCJzcGFuIiwibWFwIiwiY2hpbGQiLCJoMiIsInAiLCJidXR0b24iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/DashboardSidebar.tsx\n"));

/***/ })

});