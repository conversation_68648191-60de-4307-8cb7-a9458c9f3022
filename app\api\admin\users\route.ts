import { NextRequest, NextResponse } from 'next/server'
import jwt from 'jsonwebtoken'

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-here-make-it-very-long-and-secure-for-production'

// Real users data storage
const usersData = [
  {
    id: '1',
    username: 'admin',
    email: '<EMAIL>',
    fullName: 'Admin KodeXGuard',
    role: 'admin',
    plan: 'Elite',
    status: 'active',
    joinedAt: '2024-01-01T00:00:00Z',
    lastActive: new Date().toISOString(),
    totalScans: 1247,
    vulnerabilitiesFound: 3892,
    reportsGenerated: 156,
    pointsEarned: 8950
  },
  {
    id: '2',
    username: 'cyberhunter',
    email: '<EMAIL>',
    fullName: 'Cyber Hunter',
    role: 'user',
    plan: 'Expert',
    status: 'active',
    joinedAt: '2024-01-15T10:30:00Z',
    lastActive: '2024-01-20T14:22:00Z',
    totalScans: 456,
    vulnerabilitiesFound: 1234,
    reportsGenerated: 67,
    pointsEarned: 5670
  }
]

function getUserFromToken(request: NextRequest) {
  try {
    const cookies = request.headers.get('cookie')
    if (!cookies) return null

    const tokenMatch = cookies.match(/accessToken=([^;]+)/)
    const token = tokenMatch ? tokenMatch[1] : null

    if (!token) return null

    const decoded = jwt.verify(token, JWT_SECRET) as any
    return decoded
  } catch (error) {
    console.error('Token verification error:', error)
    return null
  }
}

export async function GET(request: NextRequest) {
  try {
    console.log('👥 Admin Users API: GET request received')

    const user = getUserFromToken(request)
    if (!user) {
      console.log('❌ Admin Users API: No valid token')
      return NextResponse.json({ success: false, error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user is admin
    if (user.role !== 'admin') {
      console.log('❌ Admin Users API: User is not admin')
      return NextResponse.json({ success: false, error: 'Access denied' }, { status: 403 })
    }

    console.log('✅ Admin Users API: Admin authenticated:', user.username)

    const { searchParams } = new URL(request.url)
    const search = searchParams.get('search') || ''
    const plan = searchParams.get('plan') || ''
    const status = searchParams.get('status') || ''
    const limit = parseInt(searchParams.get('limit') || '20')
    const offset = parseInt(searchParams.get('offset') || '0')

    // Filter users
    let filteredUsers = [...usersData]

    if (search) {
      filteredUsers = filteredUsers.filter(u =>
        u.username.toLowerCase().includes(search.toLowerCase()) ||
        u.email.toLowerCase().includes(search.toLowerCase()) ||
        u.fullName.toLowerCase().includes(search.toLowerCase())
      )
    }

    if (plan) {
      filteredUsers = filteredUsers.filter(u => u.plan === plan)
    }

    if (status) {
      filteredUsers = filteredUsers.filter(u => u.status === status)
    }

    // Sort by join date (newest first)
    filteredUsers.sort((a, b) =>
      new Date(b.joinedAt).getTime() - new Date(a.joinedAt).getTime()
    )

    // Pagination
    const total = filteredUsers.length
    const paginatedUsers = filteredUsers.slice(offset, offset + limit)

    // Calculate statistics
    const stats = {
      total: usersData.length,
      active: usersData.filter(u => u.status === 'active').length,
      inactive: usersData.filter(u => u.status === 'inactive').length,
      banned: usersData.filter(u => u.status === 'banned').length,
      plans: {
        free: usersData.filter(u => u.plan === 'Free').length,
        student: usersData.filter(u => u.plan === 'Student').length,
        hobby: usersData.filter(u => u.plan === 'Hobby').length,
        pro: usersData.filter(u => u.plan === 'Pro').length,
        expert: usersData.filter(u => u.plan === 'Expert').length,
        elite: usersData.filter(u => u.plan === 'Elite').length
      }
    }

    console.log('✅ Admin Users API: Data retrieved successfully')

    return NextResponse.json({
      success: true,
      data: {
        users: paginatedUsers,
        pagination: {
          total,
          limit,
          offset,
          hasMore: offset + limit < total
        },
        stats
      }
    })

  } catch (error) {
    console.error('❌ Admin Users API Error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
