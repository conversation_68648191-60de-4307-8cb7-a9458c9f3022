import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    console.log('🔐 Test login attempt started')

    const body = await request.json()
    const { email, password } = body

    console.log('Test login attempt for email:', email)

    // Simple test credentials
    if (email === '<EMAIL>' && password === 'admin123') {
      const mockUser = {
        id: 1,
        username: 'admin',
        email: '<EMAIL>',
        fullName: 'Administrator',
        role: 'admin',
        plan: 'Elite',
        level: 100,
        score: 10000,
        streak: 365,
        emailVerified: true,
        lastActive: new Date().toISOString(),
        createdAt: new Date().toISOString()
      }

      const mockTokens = {
        accessToken: 'mock-access-token-' + Date.now(),
        refreshToken: 'mock-refresh-token-' + Date.now(),
        expiresIn: 7 * 24 * 60 * 60
      }

      console.log('✅ Test login successful for user:', mockUser.id)

      const response = NextResponse.json({
        success: true,
        message: 'Login successful',
        data: {
          user: mockUser,
          tokens: mockTokens
        }
      })

      // Set mock cookies
      response.cookies.set('accessToken', mockTokens.accessToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        path: '/',
        maxAge: 24 * 60 * 60
      })

      return response
    } else {
      console.log('❌ Test login failed: Invalid credentials')
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid email or password'
        },
        { status: 401 }
      )
    }

  } catch (error) {
    console.error('❌ Test login error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: 'An unexpected error occurred during login'
      },
      { status: 500 }
    )
  }
}

export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}
