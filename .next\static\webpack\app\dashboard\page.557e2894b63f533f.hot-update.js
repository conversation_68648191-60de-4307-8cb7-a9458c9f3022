"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-cog.js":
/*!**************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/user-cog.js ***!
  \**************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ UserCog; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * lucide-react v0.292.0 - ISC\n */ \nconst UserCog = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"UserCog\", [\n    [\n        \"circle\",\n        {\n            cx: \"18\",\n            cy: \"15\",\n            r: \"3\",\n            key: \"gjjjvw\"\n        }\n    ],\n    [\n        \"circle\",\n        {\n            cx: \"9\",\n            cy: \"7\",\n            r: \"4\",\n            key: \"nufk8\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M10 15H6a4 4 0 0 0-4 4v2\",\n            key: \"1nfge6\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m21.7 16.4-.9-.3\",\n            key: \"12j9ji\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m15.2 13.9-.9-.3\",\n            key: \"1fdjdi\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m16.6 18.7.3-.9\",\n            key: \"heedtr\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m19.1 12.2.3-.9\",\n            key: \"1af3ki\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m19.6 18.7-.4-1\",\n            key: \"1x9vze\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m16.8 12.3-.4-1\",\n            key: \"vqeiwj\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m14.3 16.6 1-.4\",\n            key: \"1qlj63\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m20.7 13.8 1-.4\",\n            key: \"1v5t8k\"\n        }\n    ]\n]);\n //# sourceMappingURL=user-cog.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-cog.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/DashboardSidebar.tsx":
/*!*****************************************!*\
  !*** ./components/DashboardSidebar.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DashboardSidebar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/home.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/terminal.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-cog.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/monitor.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/cog.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield-check.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/webhook.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-check.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction DashboardSidebar(param) {\n    let { user, isCollapsed, onToggle } = param;\n    var _this = this;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const [expandedItems, setExpandedItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Define sidebar items based on user role and plan\n    const sidebarItems = [\n        // Main Dashboard\n        {\n            id: \"dashboard\",\n            title: \"Dashboard\",\n            icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            href: \"/dashboard\"\n        },\n        // Core Tools (Available to all users)\n        {\n            id: \"tools\",\n            title: \"Security Tools\",\n            icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            children: [\n                {\n                    id: \"osint\",\n                    title: \"OSINT Lookup\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                    href: \"/osint\",\n                    badge: \"Popular\"\n                },\n                {\n                    id: \"scanner\",\n                    title: \"Vulnerability Scanner\",\n                    icon: Scan,\n                    href: \"/scanner\",\n                    premium: true\n                },\n                {\n                    id: \"file-analyzer\",\n                    title: \"File Analyzer\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                    href: \"/file-analyzer\",\n                    premium: true\n                },\n                {\n                    id: \"cve\",\n                    title: \"CVE Database\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                    href: \"/cve\"\n                },\n                {\n                    id: \"dorking\",\n                    title: \"Google Dorking\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                    href: \"/dorking\"\n                }\n            ]\n        },\n        // Advanced Tools (Premium plans)\n        {\n            id: \"advanced\",\n            title: \"Advanced Tools\",\n            icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            plans: [\n                \"Hobby\",\n                \"Bughunter\",\n                \"Cybersecurity\"\n            ],\n            children: [\n                {\n                    id: \"playground\",\n                    title: \"API Playground\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                    href: \"/playground\"\n                },\n                {\n                    id: \"tools-advanced\",\n                    title: \"Advanced Tools\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                    href: \"/tools\"\n                }\n            ]\n        },\n        // Community & Learning\n        {\n            id: \"community\",\n            title: \"Community\",\n            icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            children: [\n                {\n                    id: \"leaderboard\",\n                    title: \"Leaderboard\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n                    href: \"/leaderboard\"\n                },\n                {\n                    id: \"community-hub\",\n                    title: \"Community Hub\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n                    href: \"/community\"\n                }\n            ]\n        },\n        // Admin Tools (Admin and Super Admin only)\n        ...(user === null || user === void 0 ? void 0 : user.role) === \"admin\" || (user === null || user === void 0 ? void 0 : user.role) === \"super_admin\" ? [\n            {\n                id: \"admin\",\n                title: \"Administration\",\n                icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n                roles: [\n                    \"admin\",\n                    \"super_admin\"\n                ],\n                children: [\n                    {\n                        id: \"user-management\",\n                        title: \"User Management\",\n                        icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n                        href: \"/dashboard?section=users\",\n                        roles: [\n                            \"admin\",\n                            \"super_admin\"\n                        ]\n                    },\n                    {\n                        id: \"analytics\",\n                        title: \"Analytics\",\n                        icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n                        href: \"/dashboard?section=analytics\",\n                        roles: [\n                            \"admin\",\n                            \"super_admin\"\n                        ]\n                    },\n                    {\n                        id: \"monitoring\",\n                        title: \"System Monitor\",\n                        icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n                        href: \"/dashboard?section=monitoring\",\n                        roles: [\n                            \"admin\",\n                            \"super_admin\"\n                        ]\n                    }\n                ]\n            }\n        ] : [],\n        // Super Admin Tools (Super Admin only)\n        ...(user === null || user === void 0 ? void 0 : user.role) === \"super_admin\" ? [\n            {\n                id: \"superadmin\",\n                title: \"Super Admin\",\n                icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n                roles: [\n                    \"super_admin\"\n                ],\n                children: [\n                    {\n                        id: \"website-settings\",\n                        title: \"Website Settings\",\n                        icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n                        href: \"/dashboard?section=website-settings\",\n                        roles: [\n                            \"super_admin\"\n                        ]\n                    },\n                    {\n                        id: \"payment-management\",\n                        title: \"Payment Management\",\n                        icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n                        href: \"/dashboard?section=payments\",\n                        roles: [\n                            \"super_admin\"\n                        ]\n                    },\n                    {\n                        id: \"bot-management\",\n                        title: \"Bot Management\",\n                        icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n                        href: \"/dashboard?section=bots\",\n                        roles: [\n                            \"super_admin\"\n                        ]\n                    },\n                    {\n                        id: \"whatsapp-bot\",\n                        title: \"WhatsApp Bot\",\n                        icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n                        href: \"/dashboard?section=whatsapp\",\n                        roles: [\n                            \"super_admin\"\n                        ]\n                    },\n                    {\n                        id: \"telegram-bot\",\n                        title: \"Telegram Bot\",\n                        icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"],\n                        href: \"/dashboard?section=telegram\",\n                        roles: [\n                            \"super_admin\"\n                        ]\n                    },\n                    {\n                        id: \"system-config\",\n                        title: \"System Config\",\n                        icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"],\n                        href: \"/dashboard?section=system-config\",\n                        roles: [\n                            \"super_admin\"\n                        ]\n                    },\n                    {\n                        id: \"security-center\",\n                        title: \"Security Center\",\n                        icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"],\n                        href: \"/dashboard?section=security\",\n                        roles: [\n                            \"super_admin\"\n                        ]\n                    },\n                    {\n                        id: \"database-admin\",\n                        title: \"Database Admin\",\n                        icon: DatabaseIcon,\n                        href: \"/dashboard?section=database\",\n                        roles: [\n                            \"super_admin\"\n                        ]\n                    },\n                    {\n                        id: \"server-management\",\n                        title: \"Server Management\",\n                        icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"],\n                        href: \"/dashboard?section=server\",\n                        roles: [\n                            \"super_admin\"\n                        ]\n                    },\n                    {\n                        id: \"api-management\",\n                        title: \"API Management\",\n                        icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"],\n                        href: \"/dashboard?section=api\",\n                        roles: [\n                            \"super_admin\"\n                        ]\n                    }\n                ]\n            }\n        ] : [],\n        // Personal Section\n        {\n            id: \"personal\",\n            title: \"Personal\",\n            icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"],\n            children: [\n                {\n                    id: \"profile\",\n                    title: \"Profile\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"],\n                    href: \"/profile\"\n                },\n                {\n                    id: \"plan\",\n                    title: \"Subscription\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n                    href: \"/plan\"\n                },\n                {\n                    id: \"settings\",\n                    title: \"Settings\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n                    href: \"/settings\"\n                }\n            ]\n        }\n    ];\n    // Filter items based on user role and plan\n    const filterItems = (items)=>{\n        return items.filter((item)=>{\n            // Check role permissions\n            if (item.roles && !item.roles.includes((user === null || user === void 0 ? void 0 : user.role) || \"user\")) {\n                return false;\n            }\n            // Check plan permissions\n            if (item.plans && !item.plans.includes((user === null || user === void 0 ? void 0 : user.plan) || \"Free\")) {\n                return false;\n            }\n            // Check premium access\n            if (item.premium && (user === null || user === void 0 ? void 0 : user.plan) === \"Free\") {\n                return false;\n            }\n            // Filter children recursively\n            if (item.children) {\n                item.children = filterItems(item.children);\n            }\n            return true;\n        });\n    };\n    const filteredItems = filterItems(sidebarItems);\n    const toggleExpanded = (itemId)=>{\n        setExpandedItems((prev)=>prev.includes(itemId) ? prev.filter((id)=>id !== itemId) : [\n                ...prev,\n                itemId\n            ]);\n    };\n    const isActive = (href)=>{\n        if (false) {}\n        if (href === \"/dashboard\") {\n            return pathname === \"/dashboard\" && !window.location.search;\n        }\n        // Check for section-based URLs\n        if (href.includes(\"?section=\")) {\n            const currentUrl = \"\".concat(pathname).concat(window.location.search);\n            return currentUrl === href;\n        }\n        return pathname === href || pathname.startsWith(href + \"/\");\n    };\n    const renderSidebarItem = function(item) {\n        let level = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;\n        var _item_children;\n        const Icon = item.icon;\n        const hasChildren = item.children && item.children.length > 0;\n        const isExpanded = expandedItems.includes(item.id);\n        const active = item.href ? isActive(item.href) : false;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"\".concat(level > 0 ? \"ml-4\" : \"\"),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    onClick: ()=>{\n                        if (hasChildren) {\n                            toggleExpanded(item.id);\n                        } else if (item.href) {\n                            router.push(item.href);\n                        }\n                    },\n                    className: \"\\n            flex items-center justify-between p-3 rounded-lg cursor-pointer transition-all duration-200\\n            \".concat(active ? \"bg-cyber-primary/20 text-cyber-primary border-l-4 border-cyber-primary\" : \"text-gray-300 hover:bg-gray-800 hover:text-white\", \"\\n            \").concat(item.comingSoon ? \"opacity-50 cursor-not-allowed\" : \"\", \"\\n          \"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                    className: \"h-5 w-5 \".concat(active ? \"text-cyber-primary\" : \"\")\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                                    lineNumber: 386,\n                                    columnNumber: 13\n                                }, _this),\n                                !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: item.title\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                                            lineNumber: 389,\n                                            columnNumber: 17\n                                        }, _this),\n                                        item.badge && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-2 py-1 text-xs bg-cyber-primary/20 text-cyber-primary rounded-full\",\n                                            children: item.badge\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                                            lineNumber: 391,\n                                            columnNumber: 19\n                                        }, _this),\n                                        item.premium && (user === null || user === void 0 ? void 0 : user.plan) === \"Free\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-2 py-1 text-xs bg-yellow-500/20 text-yellow-400 rounded-full\",\n                                            children: \"PRO\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                                            lineNumber: 396,\n                                            columnNumber: 19\n                                        }, _this),\n                                        item.comingSoon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-2 py-1 text-xs bg-gray-500/20 text-gray-400 rounded-full\",\n                                            children: \"Soon\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                                            lineNumber: 401,\n                                            columnNumber: 19\n                                        }, _this)\n                                    ]\n                                }, void 0, true)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                            lineNumber: 385,\n                            columnNumber: 11\n                        }, _this),\n                        !isCollapsed && hasChildren && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"transform transition-transform \".concat(isExpanded ? \"rotate-90\" : \"\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-4 h-4\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M9 5l7 7-7 7\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                                    lineNumber: 412,\n                                    columnNumber: 17\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                                lineNumber: 411,\n                                columnNumber: 15\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                            lineNumber: 410,\n                            columnNumber: 13\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                    lineNumber: 368,\n                    columnNumber: 9\n                }, _this),\n                !isCollapsed && hasChildren && isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-2 space-y-1\",\n                    children: (_item_children = item.children) === null || _item_children === void 0 ? void 0 : _item_children.map((child)=>renderSidebarItem(child, level + 1))\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                    lineNumber: 419,\n                    columnNumber: 11\n                }, _this)\n            ]\n        }, item.id, true, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n            lineNumber: 367,\n            columnNumber: 7\n        }, _this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\\n      bg-gray-900 border-r border-gray-800 transition-all duration-300 flex flex-col\\n      \".concat(isCollapsed ? \"w-16\" : \"w-64\", \"\\n    \"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-b border-gray-800\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-lg font-bold text-white\",\n                                    children: \"KodeXGuard\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                                    lineNumber: 437,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400\",\n                                    children: [\n                                        (user === null || user === void 0 ? void 0 : user.role) === \"super_admin\" ? \"\\uD83D\\uDC51 Super Admin\" : (user === null || user === void 0 ? void 0 : user.role) === \"admin\" ? \"\\uD83D\\uDEE1️ Admin\" : \"\\uD83D\\uDD12 User\",\n                                        \" • \",\n                                        user === null || user === void 0 ? void 0 : user.plan\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                                    lineNumber: 438,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                            lineNumber: 436,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onToggle,\n                            className: \"p-2 rounded-lg hover:bg-gray-800 text-gray-400 hover:text-white transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-5 h-5\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M4 6h16M4 12h16M4 18h16\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                                    lineNumber: 450,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                                lineNumber: 449,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                            lineNumber: 445,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                    lineNumber: 434,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                lineNumber: 433,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto p-4 space-y-2\",\n                children: filteredItems.map((item)=>renderSidebarItem(item))\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                lineNumber: 457,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-t border-gray-800\",\n                children: !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-xs text-gray-500 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"\\xa9 2024 KodeXGuard\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                            lineNumber: 465,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"Cybersecurity Platform\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                            lineNumber: 466,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                    lineNumber: 464,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                lineNumber: 462,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n        lineNumber: 428,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardSidebar, \"IawfwaZbxNshDbEnUCH2X8OEMKg=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname\n    ];\n});\n_c = DashboardSidebar;\nvar _c;\n$RefreshReg$(_c, \"DashboardSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/DashboardSidebar.tsx\n"));

/***/ })

});