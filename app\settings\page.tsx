'use client'

import { useState, useEffect } from 'react'
import DashboardLayout from '@/components/DashboardLayout'
import {
  Settings,
  User,
  Lock,
  Bell,
  Shield,
  Eye,
  EyeOff,
  Globe,
  Palette,
  Key,
  Mail,
  Phone,
  Smartphone,
  Monitor,
  Moon,
  Sun,
  Save,
  RefreshCw,
  Trash2,
  Download,
  Upload,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Info
} from 'lucide-react'

interface UserSettings {
  profile: {
    username: string
    email: string
    fullName: string
    bio: string
    location: string
    website: string
    publicProfile: boolean
    showStats: boolean
  }
  security: {
    twoFactorEnabled: boolean
    passwordLastChanged: string
    loginNotifications: boolean
    sessionTimeout: number
    trustedDevices: number
  }
  notifications: {
    emailNotifications: boolean
    pushNotifications: boolean
    securityAlerts: boolean
    weeklyReports: boolean
    marketingEmails: boolean
  }
  privacy: {
    profileVisibility: 'public' | 'private' | 'friends'
    activityTracking: boolean
    dataCollection: boolean
    thirdPartySharing: boolean
  }
  appearance: {
    theme: 'dark' | 'light' | 'auto'
    language: string
    timezone: string
    dateFormat: string
  }
}

export default function SettingsPage() {
  const [settings, setSettings] = useState<UserSettings | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [activeTab, setActiveTab] = useState('profile')
  const [showPassword, setShowPassword] = useState(false)

  useEffect(() => {
    loadSettings()
  }, [])

  const loadSettings = async () => {
    try {
      console.log('⚙️ Loading settings data...')
      setLoading(true)

      const response = await fetch('/api/settings')
      const data = await response.json()

      if (data.success) {
        console.log('✅ Settings data loaded:', data.data)

        // Transform API data to match component interface
        const transformedSettings: UserSettings = {
          profile: {
            username: data.data.user.username,
            email: data.data.user.email,
            fullName: data.data.user.fullName,
            bio: '',
            location: '',
            website: '',
            publicProfile: data.data.preferences.privacy.profileVisibility === 'public',
            showStats: data.data.preferences.privacy.showActivity
          },
          security: {
            twoFactorEnabled: data.data.preferences.security.twoFactorEnabled,
            passwordLastChanged: '2024-12-01',
            loginNotifications: data.data.preferences.security.loginAlerts,
            sessionTimeout: data.data.preferences.security.sessionTimeout,
            trustedDevices: data.data.preferences.security.trustedDevices.length
          },
          notifications: {
            emailNotifications: data.data.preferences.notifications.email,
            pushNotifications: data.data.preferences.notifications.push,
            securityAlerts: data.data.preferences.notifications.security,
            weeklyReports: data.data.preferences.notifications.weeklyDigest,
            marketingEmails: data.data.preferences.notifications.marketing
          },
          privacy: {
            profileVisibility: data.data.preferences.privacy.profileVisibility,
            activityTracking: data.data.preferences.privacy.showActivity,
            dataCollection: false,
            thirdPartySharing: false
          },
          appearance: {
            theme: data.data.preferences.appearance.theme,
            language: data.data.preferences.appearance.language,
            timezone: data.data.preferences.appearance.timezone,
            dateFormat: data.data.preferences.appearance.dateFormat
          }
        }

        setSettings(transformedSettings)
      } else {
        console.error('❌ Failed to load settings:', data.error)
      }
    } catch (error) {
      console.error('❌ Error loading settings:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSaveSettings = async () => {
    if (!settings) return

    setSaving(true)
    try {
      console.log('💾 Saving settings...')

      // Transform settings back to API format
      const apiData = {
        preferences: {
          notifications: {
            email: settings.notifications.emailNotifications,
            push: settings.notifications.pushNotifications,
            security: settings.notifications.securityAlerts,
            weeklyDigest: settings.notifications.weeklyReports,
            marketing: settings.notifications.marketingEmails
          },
          privacy: {
            profileVisibility: settings.privacy.profileVisibility,
            showActivity: settings.privacy.activityTracking
          },
          security: {
            twoFactorEnabled: settings.security.twoFactorEnabled,
            loginAlerts: settings.security.loginNotifications,
            sessionTimeout: settings.security.sessionTimeout
          },
          appearance: {
            theme: settings.appearance.theme,
            language: settings.appearance.language,
            timezone: settings.appearance.timezone,
            dateFormat: settings.appearance.dateFormat
          }
        }
      }

      const response = await fetch('/api/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(apiData),
      })

      const data = await response.json()

      if (data.success) {
        console.log('✅ Settings saved successfully')
      } else {
        console.error('❌ Failed to save settings:', data.error)
      }
    } catch (error) {
      console.error('❌ Error saving settings:', error)
    } finally {
      setSaving(false)
    }
  }

  const handleSettingChange = (category: keyof UserSettings, key: string, value: any) => {
    if (!settings) return

    setSettings(prev => ({
      ...prev!,
      [category]: {
        ...prev![category],
        [key]: value
      }
    }))
  }

  if (loading) {
    return (
      <DashboardLayout>
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-2 border-cyber-primary border-t-transparent mx-auto mb-4"></div>
            <div className="text-cyber-primary font-medium">Loading settings...</div>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  if (!settings) {
    return (
      <DashboardLayout>
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <XCircle className="h-12 w-12 text-red-400 mx-auto mb-4" />
            <div className="text-red-400 font-medium">Failed to load settings</div>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-8">
        {/* Header */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
          <div>
            <h1 className="text-4xl font-bold mb-2">
              <span className="text-cyber-glow">Account</span>{' '}
              <span className="text-cyber-pink">Settings</span>
            </h1>
            <p className="text-gray-300 text-lg">
              Manage your account preferences and security
            </p>
          </div>

          <div className="mt-6 lg:mt-0 flex items-center space-x-4">
            <button className="btn-cyber-secondary">
              <RefreshCw className="h-4 w-4 mr-2" />
              Reset to Default
            </button>
            <button
              onClick={handleSaveSettings}
              disabled={saving}
              className="btn-cyber-primary"
            >
              {saving ? (
                <div className="animate-spin rounded-full h-4 w-4 border-2 border-black border-t-transparent mr-2"></div>
              ) : (
                <Save className="h-4 w-4 mr-2" />
              )}
              Save Changes
            </button>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="flex space-x-1 bg-cyber-dark/50 p-1 rounded-lg overflow-x-auto">
          {[
            { id: 'profile', label: 'Profile', icon: User },
            { id: 'security', label: 'Security', icon: Lock },
            { id: 'notifications', label: 'Notifications', icon: Bell },
            { id: 'privacy', label: 'Privacy', icon: Shield },
            { id: 'appearance', label: 'Appearance', icon: Palette }
          ].map((tab) => {
            const Icon = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all duration-200 whitespace-nowrap ${
                  activeTab === tab.id
                    ? 'bg-cyber-primary/20 text-cyber-primary border border-cyber-primary/30'
                    : 'text-gray-400 hover:text-white hover:bg-cyber-primary/10'
                }`}
              >
                <Icon className="h-4 w-4" />
                <span>{tab.label}</span>
              </button>
            )
          })}
        </div>

        {/* Profile Settings */}
        {activeTab === 'profile' && (
          <div className="space-y-6">
            <div className="card-cyber">
              <h2 className="text-2xl font-bold text-white mb-6">
                <span className="text-cyber-glow">Profile</span>{' '}
                <span className="text-cyber-pink">Information</span>
              </h2>

              <div className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Username
                    </label>
                    <input
                      type="text"
                      value={settings.profile.username}
                      onChange={(e) => handleSettingChange('profile', 'username', e.target.value)}
                      className="w-full px-4 py-3 rounded-lg input-cyber"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Email Address
                    </label>
                    <input
                      type="email"
                      value={settings.profile.email}
                      onChange={(e) => handleSettingChange('profile', 'email', e.target.value)}
                      className="w-full px-4 py-3 rounded-lg input-cyber"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Full Name
                    </label>
                    <input
                      type="text"
                      value={settings.profile.fullName}
                      onChange={(e) => handleSettingChange('profile', 'fullName', e.target.value)}
                      className="w-full px-4 py-3 rounded-lg input-cyber"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Location
                    </label>
                    <input
                      type="text"
                      value={settings.profile.location}
                      onChange={(e) => handleSettingChange('profile', 'location', e.target.value)}
                      className="w-full px-4 py-3 rounded-lg input-cyber"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Bio
                  </label>
                  <textarea
                    value={settings.profile.bio}
                    onChange={(e) => handleSettingChange('profile', 'bio', e.target.value)}
                    rows={3}
                    className="w-full px-4 py-3 rounded-lg input-cyber"
                    placeholder="Tell us about yourself..."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Website
                  </label>
                  <input
                    type="url"
                    value={settings.profile.website}
                    onChange={(e) => handleSettingChange('profile', 'website', e.target.value)}
                    className="w-full px-4 py-3 rounded-lg input-cyber"
                    placeholder="https://your-website.com"
                  />
                </div>

                <div className="space-y-4">
                  <div className="flex items-center justify-between p-4 rounded-lg bg-cyber-secondary/5">
                    <div>
                      <h3 className="text-white font-medium">Public Profile</h3>
                      <p className="text-gray-400 text-sm">Make your profile visible to other users</p>
                    </div>
                    <button
                      onClick={() => handleSettingChange('profile', 'publicProfile', !settings.profile.publicProfile)}
                      className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                        settings.profile.publicProfile ? 'bg-cyber-primary' : 'bg-gray-600'
                      }`}
                    >
                      <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                        settings.profile.publicProfile ? 'translate-x-6' : 'translate-x-1'
                      }`} />
                    </button>
                  </div>

                  <div className="flex items-center justify-between p-4 rounded-lg bg-cyber-secondary/5">
                    <div>
                      <h3 className="text-white font-medium">Show Statistics</h3>
                      <p className="text-gray-400 text-sm">Display your stats on your public profile</p>
                    </div>
                    <button
                      onClick={() => handleSettingChange('profile', 'showStats', !settings.profile.showStats)}
                      className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                        settings.profile.showStats ? 'bg-cyber-primary' : 'bg-gray-600'
                      }`}
                    >
                      <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                        settings.profile.showStats ? 'translate-x-6' : 'translate-x-1'
                      }`} />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Security Settings */}
        {activeTab === 'security' && (
          <div className="space-y-6">
            <div className="card-cyber">
              <h2 className="text-2xl font-bold text-white mb-6">
                <span className="text-cyber-glow">Security</span>{' '}
                <span className="text-cyber-pink">Settings</span>
              </h2>

              <div className="space-y-6">
                <div className="p-4 rounded-lg bg-green-500/10 border border-green-500/30">
                  <div className="flex items-center space-x-2 mb-2">
                    <CheckCircle className="h-5 w-5 text-green-400" />
                    <span className="text-green-400 font-medium">Security Status: Good</span>
                  </div>
                  <p className="text-gray-300 text-sm">Your account is well protected with 2FA enabled.</p>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center justify-between p-4 rounded-lg bg-cyber-secondary/5">
                    <div>
                      <h3 className="text-white font-medium">Two-Factor Authentication</h3>
                      <p className="text-gray-400 text-sm">Add an extra layer of security to your account</p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <CheckCircle className="h-4 w-4 text-green-400" />
                      <span className="text-green-400 text-sm">Enabled</span>
                    </div>
                  </div>

                  <div className="flex items-center justify-between p-4 rounded-lg bg-cyber-secondary/5">
                    <div>
                      <h3 className="text-white font-medium">Login Notifications</h3>
                      <p className="text-gray-400 text-sm">Get notified when someone logs into your account</p>
                    </div>
                    <button
                      onClick={() => handleSettingChange('security', 'loginNotifications', !settings.security.loginNotifications)}
                      className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                        settings.security.loginNotifications ? 'bg-cyber-primary' : 'bg-gray-600'
                      }`}
                    >
                      <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                        settings.security.loginNotifications ? 'translate-x-6' : 'translate-x-1'
                      }`} />
                    </button>
                  </div>

                  <div className="p-4 rounded-lg bg-cyber-secondary/5">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-white font-medium">Password</h3>
                      <button className="btn-cyber-primary text-sm">
                        Change Password
                      </button>
                    </div>
                    <p className="text-gray-400 text-sm">
                      Last changed: {settings.security.passwordLastChanged}
                    </p>
                  </div>

                  <div className="p-4 rounded-lg bg-cyber-secondary/5">
                    <h3 className="text-white font-medium mb-2">Session Timeout</h3>
                    <select
                      value={settings.security.sessionTimeout}
                      onChange={(e) => handleSettingChange('security', 'sessionTimeout', parseInt(e.target.value))}
                      className="w-full px-4 py-2 rounded-lg input-cyber"
                    >
                      <option value={15}>15 minutes</option>
                      <option value={30}>30 minutes</option>
                      <option value={60}>1 hour</option>
                      <option value={120}>2 hours</option>
                      <option value={480}>8 hours</option>
                    </select>
                  </div>

                  <div className="p-4 rounded-lg bg-cyber-secondary/5">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="text-white font-medium">Trusted Devices</h3>
                        <p className="text-gray-400 text-sm">{settings.security.trustedDevices} devices trusted</p>
                      </div>
                      <button className="btn-cyber-secondary text-sm">
                        Manage Devices
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </DashboardLayout>
  )
}