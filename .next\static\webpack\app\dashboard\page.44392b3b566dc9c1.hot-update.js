"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./app/dashboard/page.tsx":
/*!********************************!*\
  !*** ./app/dashboard/page.tsx ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DashboardPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_DashboardSidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/DashboardSidebar */ \"(app-pages-browser)/./components/DashboardSidebar.tsx\");\n/* harmony import */ var _components_dashboard_UserDashboard__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/dashboard/UserDashboard */ \"(app-pages-browser)/./components/dashboard/UserDashboard.tsx\");\n/* harmony import */ var _components_dashboard_AdminSections__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/dashboard/AdminSections */ \"(app-pages-browser)/./components/dashboard/AdminSections.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction DashboardPage() {\n    var _user_username;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const section = searchParams.get(\"section\");\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [sidebarCollapsed, setSidebarCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadUserData();\n        // Check if mobile\n        const checkMobile = ()=>{\n            setIsMobile(window.innerWidth < 768);\n            if (window.innerWidth < 768) {\n                setSidebarCollapsed(true);\n            }\n        };\n        checkMobile();\n        window.addEventListener(\"resize\", checkMobile);\n        return ()=>window.removeEventListener(\"resize\", checkMobile);\n    }, []);\n    const loadUserData = async ()=>{\n        try {\n            setLoading(true);\n            console.log(\"\\uD83D\\uDD04 Loading user data...\");\n            const token = localStorage.getItem(\"token\");\n            if (!token) {\n                router.push(\"/login\");\n                return;\n            }\n            const response = await fetch(\"/api/auth/me\", {\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(token)\n                }\n            });\n            if (response.ok) {\n                const userData = await response.json();\n                setUser(userData);\n                console.log(\"✅ User data loaded:\", userData);\n            } else {\n                console.error(\"❌ Failed to load user data\");\n                router.push(\"/login\");\n            }\n        } catch (error) {\n            console.error(\"❌ Error loading user data:\", error);\n            router.push(\"/login\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const toggleSidebar = ()=>{\n        setSidebarCollapsed(!sidebarCollapsed);\n    };\n    // Close sidebar when clicking outside on mobile\n    const handleOverlayClick = ()=>{\n        if (isMobile && !sidebarCollapsed) {\n            setSidebarCollapsed(true);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-cyber-dark flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-2 border-cyber-primary border-t-transparent mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-300\",\n                        children: \"Loading dashboard...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 94,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 93,\n            columnNumber: 7\n        }, this);\n    }\n    const renderContent = ()=>{\n        // If no section specified, show user dashboard\n        if (!section) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_UserDashboard__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                user: user\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 105,\n                columnNumber: 14\n            }, this);\n        }\n        // Check if user has permission for admin sections\n        const adminSections = [\n            \"users\",\n            \"analytics\",\n            \"monitoring\"\n        ];\n        const superAdminSections = [\n            \"website-settings\",\n            \"payments\",\n            \"bots\",\n            \"whatsapp\",\n            \"telegram\",\n            \"system-config\",\n            \"security\",\n            \"database\",\n            \"server\",\n            \"api\"\n        ];\n        if (adminSections.includes(section)) {\n            if ((user === null || user === void 0 ? void 0 : user.role) !== \"admin\" && (user === null || user === void 0 ? void 0 : user.role) !== \"super_admin\") {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-white mb-4\",\n                            children: \"Access Denied\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400 mb-6\",\n                            children: \"You don't have permission to access this section.\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>router.push(\"/dashboard\"),\n                            className: \"btn-cyber-primary\",\n                            children: \"Back to Dashboard\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 118,\n                    columnNumber: 11\n                }, this);\n            }\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_AdminSections__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                section: section,\n                user: user\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 130,\n                columnNumber: 14\n            }, this);\n        }\n        if (superAdminSections.includes(section)) {\n            if ((user === null || user === void 0 ? void 0 : user.role) !== \"super_admin\") {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-white mb-4\",\n                            children: \"Super Admin Access Required\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400 mb-6\",\n                            children: \"This section requires super admin privileges.\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>router.push(\"/dashboard\"),\n                            className: \"btn-cyber-primary\",\n                            children: \"Back to Dashboard\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 11\n                }, this);\n            }\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_AdminSections__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                section: section,\n                user: user\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 148,\n                columnNumber: 14\n            }, this);\n        }\n        // Unknown section\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-2xl font-bold text-white mb-4\",\n                    children: \"Section Not Found\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 154,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-400 mb-6\",\n                    children: [\n                        'The requested section \"',\n                        section,\n                        '\" is not available.'\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 155,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>router.push(\"/dashboard\"),\n                    className: \"btn-cyber-primary\",\n                    children: \"Back to Dashboard\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 156,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 153,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-cyber-dark flex\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardSidebar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                user: user,\n                isCollapsed: sidebarCollapsed,\n                onToggle: toggleSidebar\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 169,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"bg-gray-900 border-b border-gray-800 px-6 py-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-xl font-bold text-white\",\n                                            children: section ? section.split(\"-\").map((word)=>word.charAt(0).toUpperCase() + word.slice(1)).join(\" \") : \"Dashboard\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400 text-sm\",\n                                            children: (user === null || user === void 0 ? void 0 : user.role) === \"super_admin\" ? \"\\uD83D\\uDC51 Super Admin Panel\" : (user === null || user === void 0 ? void 0 : user.role) === \"admin\" ? \"\\uD83D\\uDEE1️ Admin Panel\" : \"\\uD83D\\uDD12 User Dashboard\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-white font-medium\",\n                                                    children: user === null || user === void 0 ? void 0 : user.username\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 197,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-400 text-sm\",\n                                                    children: [\n                                                        user === null || user === void 0 ? void 0 : user.plan,\n                                                        \" Plan\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-cyber-primary/20 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-cyber-primary font-bold\",\n                                                children: user === null || user === void 0 ? void 0 : (_user_username = user.username) === null || _user_username === void 0 ? void 0 : _user_username.charAt(0).toUpperCase()\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 178,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 p-6 overflow-y-auto\",\n                        children: renderContent()\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 210,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 176,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 167,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardPage, \"7BqhCUcdNPPJYIxa26BOOET1LW0=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c = DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/page.tsx\n"));

/***/ })

});