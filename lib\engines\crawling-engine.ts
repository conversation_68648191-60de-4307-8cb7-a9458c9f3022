import axios from 'axios'
import * as cheerio from 'cheerio'
import { URL } from 'url'
import { db } from '../database'
import crypto from 'crypto'
import fs from 'fs'
import path from 'path'

// Enhanced interfaces for advanced crawling capabilities
export interface CrawlTarget {
  id: string
  url: string
  depth: number
  maxPages: number
  respectRobots: boolean
  userAgent: string
  delay: number
  filters: string[]
  extractors: string[]
  // Advanced options
  followRedirects: boolean
  maxRedirects: number
  timeout: number
  retryAttempts: number
  proxyRotation: boolean
  screenshotCapture: boolean
  jsRendering: boolean
  cookieHandling: boolean
  customHeaders: Record<string, string>
  authCredentials?: {
    username: string
    password: string
    type: 'basic' | 'digest' | 'bearer'
  }
}

export interface CrawlResult {
  url: string
  title: string
  content: string
  links: string[]
  emails: string[]
  phones: string[]
  technologies: string[]
  headers: Record<string, string>
  statusCode: number
  responseTime: number
  timestamp: Date
  metadata: Record<string, any>
  // Enhanced data extraction
  socialMedia: string[]
  ipAddresses: string[]
  domains: string[]
  fileHashes: string[]
  apiEndpoints: string[]
  credentials: string[]
  vulnerabilities: string[]
  geolocation?: {
    country: string
    city: string
    coordinates: [number, number]
  }
  screenshot?: string // Base64 encoded screenshot
  networkTraffic: {
    requests: number
    responses: number
    dataTransferred: number
  }
}

export interface CrawlSession {
  id: string
  target: CrawlTarget
  status: 'pending' | 'running' | 'completed' | 'failed' | 'paused' | 'cancelled'
  startTime: Date
  endTime?: Date
  pagesFound: number
  pagesCrawled: number
  errors: string[]
  results: CrawlResult[]
  // Enhanced session tracking
  performance: {
    avgResponseTime: number
    successRate: number
    errorRate: number
    dataExtracted: number
  }
  resources: {
    memoryUsage: number
    cpuUsage: number
    networkUsage: number
  }
}

export class CrawlingEngine {
  private sessions: Map<string, CrawlSession> = new Map()
  private queue: string[] = []
  private visited: Set<string> = new Set()
  private running: boolean = false
  private robotsCache: Map<string, any> = new Map()
  private proxyList: string[] = []
  private currentProxyIndex: number = 0
  private maxConcurrentCrawls: number = 5
  private activeCrawls: number = 0

  // Advanced regex patterns for data extraction
  private patterns = {
    email: /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g,
    phone: /(\+?1?[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})/g,
    ipAddress: /\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b/g,
    domain: /(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}/g,
    socialMedia: {
      twitter: /(?:https?:\/\/)?(?:www\.)?twitter\.com\/[a-zA-Z0-9_]+/g,
      facebook: /(?:https?:\/\/)?(?:www\.)?facebook\.com\/[a-zA-Z0-9.]+/g,
      linkedin: /(?:https?:\/\/)?(?:www\.)?linkedin\.com\/in\/[a-zA-Z0-9-]+/g,
      instagram: /(?:https?:\/\/)?(?:www\.)?instagram\.com\/[a-zA-Z0-9_.]+/g,
      github: /(?:https?:\/\/)?(?:www\.)?github\.com\/[a-zA-Z0-9-]+/g
    },
    credentials: {
      apiKey: /(?:api[_-]?key|apikey)["\s]*[:=]["\s]*([a-zA-Z0-9_-]+)/gi,
      token: /(?:token|access[_-]?token)["\s]*[:=]["\s]*([a-zA-Z0-9._-]+)/gi,
      password: /(?:password|pwd|pass)["\s]*[:=]["\s]*["']([^"']+)["']/gi,
      secret: /(?:secret|private[_-]?key)["\s]*[:=]["\s]*["']([^"']+)["']/gi
    },
    vulnerabilities: {
      sqli: /(?:union|select|insert|update|delete|drop|create|alter)\s+/gi,
      xss: /<script[^>]*>.*?<\/script>/gi,
      lfi: /(?:\.\.\/|\.\.\\|\/etc\/passwd|\/proc\/version)/gi,
      rce: /(?:system|exec|shell_exec|passthru|eval)\s*\(/gi
    }
  }

  constructor() {
    this.initializeEngine()
  }

  private async initializeEngine() {
    console.log('🕷️ Advanced Crawling Engine initialized')

    // Create enhanced tables
    await this.createTables()

    // Load proxy list for rotation
    await this.loadProxyList()

    // Resume any pending sessions
    await this.resumePendingSessions()

    // Initialize performance monitoring
    this.startPerformanceMonitoring()
  }

  private async loadProxyList() {
    // Load proxy list from database or config
    try {
      const result = await db.query('SELECT proxy_url FROM proxy_list WHERE is_active = 1')
      this.proxyList = result.map((row: any) => row.proxy_url)
      console.log(`📡 Loaded ${this.proxyList.length} active proxies`)
    } catch (error) {
      console.log('📡 No proxy configuration found, using direct connection')
    }
  }

  private startPerformanceMonitoring() {
    setInterval(() => {
      this.updatePerformanceMetrics()
    }, 30000) // Update every 30 seconds
  }

  private updatePerformanceMetrics() {
    const memUsage = process.memoryUsage()
    const cpuUsage = process.cpuUsage()

    // Update performance metrics for active sessions
    this.sessions.forEach(session => {
      if (session.status === 'running') {
        session.resources = {
          memoryUsage: memUsage.heapUsed / 1024 / 1024, // MB
          cpuUsage: (cpuUsage.user + cpuUsage.system) / 1000000, // seconds
          networkUsage: session.results.reduce((total, result) =>
            total + (result.networkTraffic?.dataTransferred || 0), 0)
        }
      }
    })
  }

  private async createTables() {
    try {
      // Enhanced crawl_sessions table
      await db.query(`
        CREATE TABLE IF NOT EXISTS crawl_sessions (
          id VARCHAR(36) PRIMARY KEY,
          user_id INT,
          target_url VARCHAR(2048) NOT NULL,
          config JSON,
          status ENUM('pending', 'running', 'completed', 'failed', 'paused', 'cancelled') DEFAULT 'pending',
          pages_found INT DEFAULT 0,
          pages_crawled INT DEFAULT 0,
          errors JSON,
          results JSON,
          performance_metrics JSON,
          resource_usage JSON,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          completed_at TIMESTAMP NULL,
          INDEX idx_user_id (user_id),
          INDEX idx_status (status),
          INDEX idx_created_at (created_at)
        )
      `)

      // Enhanced crawl_results table with OSINT data
      await db.query(`
        CREATE TABLE IF NOT EXISTS crawl_results (
          id VARCHAR(36) PRIMARY KEY,
          session_id VARCHAR(36),
          url VARCHAR(2048) NOT NULL,
          title TEXT,
          content LONGTEXT,
          links JSON,
          emails JSON,
          phones JSON,
          technologies JSON,
          headers JSON,
          status_code INT,
          response_time INT,
          metadata JSON,
          social_media JSON,
          ip_addresses JSON,
          domains JSON,
          file_hashes JSON,
          api_endpoints JSON,
          credentials JSON,
          vulnerabilities JSON,
          geolocation JSON,
          screenshot LONGTEXT,
          network_traffic JSON,
          crawled_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (session_id) REFERENCES crawl_sessions(id) ON DELETE CASCADE,
          INDEX idx_session_id (session_id),
          INDEX idx_url (url(255)),
          INDEX idx_crawled_at (crawled_at)
        )
      `)

      // Proxy management table
      await db.query(`
        CREATE TABLE IF NOT EXISTS proxy_list (
          id INT AUTO_INCREMENT PRIMARY KEY,
          proxy_url VARCHAR(255) NOT NULL,
          proxy_type ENUM('http', 'https', 'socks4', 'socks5') DEFAULT 'http',
          is_active BOOLEAN DEFAULT TRUE,
          last_used TIMESTAMP NULL,
          success_rate DECIMAL(5,2) DEFAULT 100.00,
          response_time INT DEFAULT 0,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
      `)

      console.log('✅ Enhanced Crawling Engine tables created')
    } catch (error) {
      console.error('❌ Error creating crawling tables:', error)
    }
  }

  // Advanced data extraction methods
  private extractAdvancedData(content: string, url: string): Partial<CrawlResult> {
    const extracted: Partial<CrawlResult> = {
      socialMedia: this.extractSocialMedia(content),
      ipAddresses: this.extractMatches(content, this.patterns.ipAddress),
      domains: this.extractMatches(content, this.patterns.domain),
      apiEndpoints: this.extractApiEndpoints(content),
      credentials: this.extractCredentials(content),
      vulnerabilities: this.detectVulnerabilities(content)
    }

    return extracted
  }

  private extractSocialMedia(content: string): string[] {
    const socialMedia: string[] = []

    Object.entries(this.patterns.socialMedia).forEach(([platform, pattern]) => {
      const matches = content.match(pattern) || []
      socialMedia.push(...matches.map(match => `${platform}: ${match}`))
    })

    return [...new Set(socialMedia)] // Remove duplicates
  }

  private extractMatches(content: string, pattern: RegExp): string[] {
    const matches = content.match(pattern) || []
    return [...new Set(matches)] // Remove duplicates
  }

  private extractApiEndpoints(content: string): string[] {
    const apiPatterns = [
      /\/api\/[a-zA-Z0-9\/\-_]+/g,
      /\/v[0-9]+\/[a-zA-Z0-9\/\-_]+/g,
      /\/rest\/[a-zA-Z0-9\/\-_]+/g,
      /\/graphql/g
    ]

    const endpoints: string[] = []
    apiPatterns.forEach(pattern => {
      const matches = content.match(pattern) || []
      endpoints.push(...matches)
    })

    return [...new Set(endpoints)]
  }

  private extractCredentials(content: string): string[] {
    const credentials: string[] = []

    Object.entries(this.patterns.credentials).forEach(([type, pattern]) => {
      const matches = content.match(pattern) || []
      matches.forEach(match => {
        credentials.push(`${type}: ${match}`)
      })
    })

    return credentials
  }

  private detectVulnerabilities(content: string): string[] {
    const vulnerabilities: string[] = []

    Object.entries(this.patterns.vulnerabilities).forEach(([type, pattern]) => {
      if (pattern.test(content)) {
        vulnerabilities.push(type.toUpperCase())
      }
    })

    return vulnerabilities
  }

  private async getGeolocation(ip: string): Promise<any> {
    try {
      // Use a geolocation service (example with ipapi.co)
      const response = await axios.get(`https://ipapi.co/${ip}/json/`, {
        timeout: 5000
      })

      return {
        country: response.data.country_name,
        city: response.data.city,
        coordinates: [response.data.latitude, response.data.longitude]
      }
    } catch (error) {
      return null
    }
  }

  private getNextProxy(): string | null {
    if (this.proxyList.length === 0) return null

    const proxy = this.proxyList[this.currentProxyIndex]
    this.currentProxyIndex = (this.currentProxyIndex + 1) % this.proxyList.length

    return proxy
  }

  // Enhanced page crawling with advanced OSINT capabilities
  private async crawlPageEnhanced(url: string, target: CrawlTarget): Promise<CrawlResult | null> {
    const startTime = Date.now()

    try {
      // Setup request configuration with advanced options
      const config: any = {
        url,
        method: 'GET',
        timeout: target.timeout || 30000,
        maxRedirects: target.maxRedirects || 5,
        headers: {
          'User-Agent': target.userAgent || 'KodeXGuard-Crawler/1.0',
          ...target.customHeaders
        },
        validateStatus: () => true // Accept all status codes
      }

      // Add proxy if available and enabled
      if (target.proxyRotation) {
        const proxy = this.getNextProxy()
        if (proxy) {
          const proxyUrl = new URL(proxy)
          config.proxy = {
            host: proxyUrl.hostname,
            port: parseInt(proxyUrl.port),
            protocol: proxyUrl.protocol.replace(':', '')
          }
        }
      }

      // Add authentication if provided
      if (target.authCredentials) {
        if (target.authCredentials.type === 'basic') {
          config.auth = {
            username: target.authCredentials.username,
            password: target.authCredentials.password
          }
        } else if (target.authCredentials.type === 'bearer') {
          config.headers['Authorization'] = `Bearer ${target.authCredentials.password}`
        }
      }

      // Make the request
      const response = await axios(config)
      const responseTime = Date.now() - startTime

      // Parse HTML content
      const $ = cheerio.load(response.data)

      // Extract basic information
      const title = $('title').text().trim()
      const content = $.text()

      // Extract links
      const links: string[] = []
      $('a[href]').each((_, element) => {
        const href = $(element).attr('href')
        if (href) {
          try {
            const absoluteUrl = new URL(href, url).toString()
            links.push(absoluteUrl)
          } catch (e) {
            // Invalid URL, skip
          }
        }
      })

      // Extract advanced OSINT data
      const advancedData = this.extractAdvancedData(content, url)

      // Detect technologies
      const technologies = this.detectTechnologies(response.headers, content, $)

      // Get geolocation for extracted IPs
      const geolocation = advancedData.ipAddresses && advancedData.ipAddresses.length > 0
        ? await this.getGeolocation(advancedData.ipAddresses[0])
        : null

      // Capture screenshot if enabled (placeholder for now)
      let screenshot: string | undefined
      if (target.screenshotCapture) {
        screenshot = await this.captureScreenshot(url)
      }

      // Build comprehensive result
      const result: CrawlResult = {
        url,
        title,
        content: content.substring(0, 10000), // Limit content size
        links,
        emails: this.extractMatches(content, this.patterns.email),
        phones: this.extractMatches(content, this.patterns.phone),
        technologies,
        headers: response.headers,
        statusCode: response.status,
        responseTime,
        timestamp: new Date(),
        metadata: {
          contentLength: response.data.length,
          contentType: response.headers['content-type'],
          server: response.headers['server'],
          lastModified: response.headers['last-modified']
        },
        socialMedia: advancedData.socialMedia || [],
        ipAddresses: advancedData.ipAddresses || [],
        domains: advancedData.domains || [],
        fileHashes: [], // Will be populated if files are found
        apiEndpoints: advancedData.apiEndpoints || [],
        credentials: advancedData.credentials || [],
        vulnerabilities: advancedData.vulnerabilities || [],
        geolocation,
        screenshot,
        networkTraffic: {
          requests: 1,
          responses: 1,
          dataTransferred: response.data.length
        }
      }

      return result

    } catch (error: any) {
      console.error(`❌ Error crawling ${url}:`, error.message)

      // Return error result
      return {
        url,
        title: '',
        content: '',
        links: [],
        emails: [],
        phones: [],
        technologies: [],
        headers: {},
        statusCode: error.response?.status || 0,
        responseTime: Date.now() - startTime,
        timestamp: new Date(),
        metadata: { error: error.message },
        socialMedia: [],
        ipAddresses: [],
        domains: [],
        fileHashes: [],
        apiEndpoints: [],
        credentials: [],
        vulnerabilities: [],
        networkTraffic: {
          requests: 1,
          responses: 0,
          dataTransferred: 0
        }
      }
    }
  }

  // Technology detection based on headers, content, and DOM
  private detectTechnologies(headers: any, content: string, $: cheerio.CheerioAPI): string[] {
    const technologies: string[] = []

    // Server detection
    if (headers.server) {
      technologies.push(`Server: ${headers.server}`)
    }

    // Framework detection
    const frameworks = {
      'React': /react/i,
      'Vue.js': /vue\.js|vuejs/i,
      'Angular': /angular/i,
      'jQuery': /jquery/i,
      'Bootstrap': /bootstrap/i,
      'WordPress': /wp-content|wordpress/i,
      'Drupal': /drupal/i,
      'Joomla': /joomla/i,
      'Laravel': /laravel/i,
      'Django': /django/i,
      'Rails': /rails/i,
      'Express': /express/i,
      'Next.js': /next\.js|_next/i,
      'Nuxt.js': /nuxt\.js|_nuxt/i
    }

    Object.entries(frameworks).forEach(([name, pattern]) => {
      if (pattern.test(content) || pattern.test(headers['x-powered-by'] || '')) {
        technologies.push(name)
      }
    })

    // Meta tag detection
    $('meta[name="generator"]').each((_, element) => {
      const generator = $(element).attr('content')
      if (generator) {
        technologies.push(`Generator: ${generator}`)
      }
    })

    // CDN detection
    const cdnPatterns = {
      'Cloudflare': /cloudflare/i,
      'AWS CloudFront': /cloudfront/i,
      'Fastly': /fastly/i,
      'MaxCDN': /maxcdn/i,
      'KeyCDN': /keycdn/i
    }

    Object.entries(cdnPatterns).forEach(([name, pattern]) => {
      if (pattern.test(headers['server'] || '') || pattern.test(headers['cf-ray'] || '')) {
        technologies.push(`CDN: ${name}`)
      }
    })

    return [...new Set(technologies)]
  }

  // Screenshot capture (placeholder - would need puppeteer for real implementation)
  private async captureScreenshot(url: string): Promise<string | undefined> {
    try {
      // This is a placeholder - in real implementation, you'd use puppeteer
      // const browser = await puppeteer.launch()
      // const page = await browser.newPage()
      // await page.goto(url)
      // const screenshot = await page.screenshot({ encoding: 'base64' })
      // await browser.close()
      // return screenshot

      return undefined // Placeholder
    } catch (error) {
      console.error('Screenshot capture failed:', error)
      return undefined
    }
  }

  // Update session performance metrics
  private updateSessionPerformance(session: CrawlSession, result: CrawlResult) {
    const results = session.results
    const totalResults = results.length

    if (totalResults > 0) {
      // Calculate average response time
      const totalResponseTime = results.reduce((sum, r) => sum + r.responseTime, 0)
      session.performance.avgResponseTime = totalResponseTime / totalResults

      // Calculate success rate
      const successfulResults = results.filter(r => r.statusCode >= 200 && r.statusCode < 400).length
      session.performance.successRate = (successfulResults / totalResults) * 100

      // Calculate error rate
      session.performance.errorRate = 100 - session.performance.successRate

      // Calculate data extracted
      session.performance.dataExtracted = results.reduce((sum, r) => {
        return sum + (r.emails.length + r.phones.length + r.socialMedia.length +
                     r.ipAddresses.length + r.credentials.length + r.vulnerabilities.length)
      }, 0)
    }
  }

  // Save crawl result to database
  private async saveResult(sessionId: string, result: CrawlResult) {
    try {
      const resultId = this.generateId()

      await db.query(`
        INSERT INTO crawl_results (
          id, session_id, url, title, content, links, emails, phones,
          technologies, headers, status_code, response_time, metadata,
          social_media, ip_addresses, domains, file_hashes, api_endpoints,
          credentials, vulnerabilities, geolocation, screenshot, network_traffic,
          crawled_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
      `, [
        resultId, sessionId, result.url, result.title, result.content,
        JSON.stringify(result.links), JSON.stringify(result.emails), JSON.stringify(result.phones),
        JSON.stringify(result.technologies), JSON.stringify(result.headers), result.statusCode,
        result.responseTime, JSON.stringify(result.metadata), JSON.stringify(result.socialMedia),
        JSON.stringify(result.ipAddresses), JSON.stringify(result.domains), JSON.stringify(result.fileHashes),
        JSON.stringify(result.apiEndpoints), JSON.stringify(result.credentials), JSON.stringify(result.vulnerabilities),
        JSON.stringify(result.geolocation), result.screenshot, JSON.stringify(result.networkTraffic)
      ])

    } catch (error) {
      console.error('Error saving crawl result:', error)
    }
  }

  // Filter links based on target filters
  private filterLinks(links: string[], filters: string[]): string[] {
    if (!filters || filters.length === 0) return links

    return links.filter(link => {
      // Apply filters (exclude patterns)
      return !filters.some(filter => {
        try {
          const regex = new RegExp(filter, 'i')
          return regex.test(link)
        } catch {
          return link.toLowerCase().includes(filter.toLowerCase())
        }
      })
    })
  }

  // Extract URLs from links with domain filtering
  private extractUrls(links: string[], baseUrl: string): string[] {
    const baseDomain = new URL(baseUrl).hostname

    return links.filter(link => {
      try {
        const url = new URL(link)
        return url.hostname === baseDomain &&
               url.protocol.startsWith('http') &&
               !link.includes('#') &&
               !link.match(/\.(pdf|doc|docx|xls|xlsx|ppt|pptx|zip|rar|tar|gz)$/i)
      } catch {
        return false
      }
    })
  }

  // Check robots.txt compliance
  private async checkRobotsTxt(url: string): Promise<boolean> {
    try {
      const domain = new URL(url).origin

      // Check cache first
      if (this.robotsCache.has(domain)) {
        return this.robotsCache.get(domain)
      }

      const robotsUrl = `${domain}/robots.txt`
      const response = await axios.get(robotsUrl, {
        timeout: 5000,
        validateStatus: () => true
      })

      if (response.status === 200) {
        const robotsTxt = response.data.toLowerCase()
        const allowed = !robotsTxt.includes('disallow: /')
        this.robotsCache.set(domain, allowed)
        return allowed
      }

      // If robots.txt not found, allow crawling
      this.robotsCache.set(domain, true)
      return true
    } catch {
      return true
    }
  }

  // Utility function for sleep/delay
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  // Generate unique ID
  private generateId(): string {
    return crypto.randomUUID()
  }

  async startCrawl(target: CrawlTarget, userId?: number): Promise<string> {
    const sessionId = this.generateId()

    const session: CrawlSession = {
      id: sessionId,
      target,
      status: 'pending',
      startTime: new Date(),
      pagesFound: 0,
      pagesCrawled: 0,
      errors: [],
      results: [],
      performance: {
        avgResponseTime: 0,
        successRate: 0,
        errorRate: 0,
        dataExtracted: 0
      },
      resources: {
        memoryUsage: 0,
        cpuUsage: 0,
        networkUsage: 0
      }
    }

    this.sessions.set(sessionId, session)

    // Save to database
    await db.query(`
      INSERT INTO crawl_sessions (id, user_id, target_url, config, status, created_at)
      VALUES (?, ?, ?, ?, 'pending', NOW())
    `, [sessionId, userId || null, target.url, JSON.stringify(target)])

    // Start crawling
    this.crawlSession(sessionId)

    return sessionId
  }

  private async crawlSession(sessionId: string) {
    const session = this.sessions.get(sessionId)
    if (!session) return

    try {
      session.status = 'running'
      await this.updateSessionStatus(sessionId, 'running')

      console.log(`🕷️ Starting crawl session ${sessionId} for ${session.target.url}`)

      // Initialize crawl queue
      this.queue = [session.target.url]
      this.visited.clear()

      let currentDepth = 0
      let pagesCrawled = 0

      while (this.queue.length > 0 && pagesCrawled < session.target.maxPages && currentDepth <= session.target.depth) {
        const url = this.queue.shift()!
        
        if (this.visited.has(url)) continue
        this.visited.add(url)

        try {
          // Respect robots.txt if enabled
          if (session.target.respectRobots && !(await this.checkRobotsTxt(url))) {
            console.log(`🚫 Robots.txt disallows crawling: ${url}`)
            continue
          }

          // Enhanced page crawling with OSINT capabilities
          const result = await this.crawlPageEnhanced(url, session.target)

          if (result) {
            session.results.push(result)
            session.pagesCrawled++

            // Extract and queue new links
            const newLinks = this.filterLinks(result.links, session.target.filters)
            this.queue.push(...newLinks.filter(link => !this.visited.has(link)))

            session.pagesFound = this.queue.length + this.visited.size

            // Save result to database
            await this.saveResult(sessionId, result)

            // Update session performance metrics
            this.updateSessionPerformance(session, result)

            console.log(`✅ Crawled: ${url} (${result.statusCode}) - Found ${newLinks.length} new links`)
          }

          // Apply delay between requests
          if (session.target.delay > 0) {
            await this.sleep(session.target.delay)
          }

          pagesCrawled++

        } catch (error) {
          const errorMsg = `Error crawling ${url}: ${error}`
          session.errors.push(errorMsg)
          console.error(`❌ ${errorMsg}`)
        }
      }

      session.status = 'completed'
      session.endTime = new Date()
      session.pagesCrawled = pagesCrawled
      session.pagesFound = this.visited.size

      await this.updateSessionStatus(sessionId, 'completed')
      console.log(`🎉 Crawl session ${sessionId} completed: ${pagesCrawled} pages crawled`)

    } catch (error) {
      session.status = 'failed'
      session.errors.push(`Session failed: ${error}`)
      await this.updateSessionStatus(sessionId, 'failed')
      console.error(`❌ Crawl session ${sessionId} failed:`, error)
    }
  }

  // Public API methods for session management

  private async updateSessionStatus(sessionId: string, status: string) {
    try {
      await db.query(`
        UPDATE crawl_sessions
        SET status = ?, updated_at = NOW(), completed_at = ${status === 'completed' ? 'NOW()' : 'NULL'}
        WHERE id = ?
      `, [status, sessionId])
    } catch (error) {
      console.error('Error updating session status:', error)
    }
  }

  private async resumePendingSessions() {
    try {
      const sessions = await db.query(`
        SELECT * FROM crawl_sessions
        WHERE status IN ('pending', 'running')
        ORDER BY created_at ASC
      `)

      for (const session of sessions) {
        console.log(`🔄 Resuming crawl session ${session.id}`)
        // Resume crawling logic here
      }
    } catch (error) {
      console.error('Error resuming sessions:', error)
    }
  }

  async getSession(sessionId: string): Promise<CrawlSession | null> {
    return this.sessions.get(sessionId) || null
  }

  async getAllSessions(): Promise<CrawlSession[]> {
    return Array.from(this.sessions.values())
  }

  async pauseSession(sessionId: string): Promise<boolean> {
    const session = this.sessions.get(sessionId)
    if (session && session.status === 'running') {
      session.status = 'paused'
      await this.updateSessionStatus(sessionId, 'paused')
      return true
    }
    return false
  }

  async stopSession(sessionId: string): Promise<boolean> {
    const session = this.sessions.get(sessionId)
    if (session) {
      session.status = 'cancelled'
      session.endTime = new Date()
      await this.updateSessionStatus(sessionId, 'cancelled')
      return true
    }
    return false
  }

  // Get session statistics
  async getSessionStats(sessionId: string): Promise<any> {
    const session = this.sessions.get(sessionId)
    if (!session) return null

    return {
      id: session.id,
      status: session.status,
      startTime: session.startTime,
      endTime: session.endTime,
      pagesFound: session.pagesFound,
      pagesCrawled: session.pagesCrawled,
      errors: session.errors.length,
      performance: session.performance,
      resources: session.resources
    }
  }

  // Get crawl results for a session
  async getSessionResults(sessionId: string): Promise<CrawlResult[]> {
    const session = this.sessions.get(sessionId)
    return session ? session.results : []
  }
}

// Export singleton instance
export const crawlingEngine = new CrawlingEngine()
