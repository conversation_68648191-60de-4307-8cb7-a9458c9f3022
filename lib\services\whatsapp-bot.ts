import { create, Whatsapp, Message } from 'venom-bot'
import { db } from '../database'
import { OSINTService } from './osint'
import { VulnerabilityScanner } from './scanner'
import qrcode from 'qrcode'
import crypto from 'crypto'

export interface BotSession {
  id: string
  client: Whatsapp | null
  status: 'disconnected' | 'connecting' | 'connected' | 'error' | 'qr_ready'
  qrCode?: string
  qrCodeImage?: string // Base64 encoded QR code image
  lastActivity: Date
  connectedAt?: Date
  error?: string
  // Enhanced session info
  sessionName: string
  createdBy: number // User ID who created the session
  phoneNumber?: string
  deviceInfo?: {
    platform: string
    version: string
    battery?: number
  }
  statistics: {
    messagesReceived: number
    messagesSent: number
    commandsExecuted: number
    activeUsers: number
  }
}

export interface BotCommand {
  command: string
  description: string
  usage: string
  adminOnly?: boolean
  category: 'general' | 'osint' | 'security' | 'admin'
  examples?: string[]
}

export interface BotUser {
  phoneNumber: string
  name?: string
  userId?: number // Linked KodeXGuard user ID
  plan: 'Free' | 'Pro' | 'Expert' | 'Elite'
  dailyUsage: {
    scans: number
    osintQueries: number
    lastReset: Date
  }
  isBlocked: boolean
  joinedAt: Date
}

export class WhatsAppBotService {
  private sessions: Map<string, BotSession> = new Map()
  private botUsers: Map<string, BotUser> = new Map()
  private osintService: OSINTService
  private scannerService: VulnerabilityScanner
  private commands: BotCommand[]
  private adminNumbers: string[] = [] // Admin phone numbers

  constructor() {
    this.osintService = new OSINTService()
    this.scannerService = new VulnerabilityScanner()
    this.initializeCommands()
    this.loadAdminNumbers()
    this.initializeTables()
  }

  private initializeCommands() {
    this.commands = [
      // General Commands
      {
        command: '/help',
        description: 'Show available commands',
        usage: '/help [category]',
        category: 'general',
        examples: ['/help', '/help osint', '/help security']
      },
      {
        command: '/start',
        description: 'Start using KodeXGuard bot',
        usage: '/start',
        category: 'general'
      },
      {
        command: '/status',
        description: 'Check your account status and usage',
        usage: '/status',
        category: 'general'
      },
      {
        command: '/plan',
        description: 'Check your current plan and limits',
        usage: '/plan',
        category: 'general'
      },

      // OSINT Commands
      {
        command: '/osint',
        description: 'General OSINT investigation',
        usage: '/osint <type> <value>',
        category: 'osint',
        examples: ['/<NAME_EMAIL>', '/osint domain example.com']
      },
      {
        command: '/nik',
        description: 'Check NIK (Indonesian ID) information',
        usage: '/nik <16-digit-nik>',
        category: 'osint',
        examples: ['/nik ****************']
      },
      {
        command: '/npwp',
        description: 'Check NPWP (Indonesian Tax ID) information',
        usage: '/npwp <15-digit-npwp>',
        category: 'osint',
        examples: ['/npwp ***************']
      },
      {
        command: '/imei',
        description: 'Check IMEI device information',
        usage: '/imei <15-digit-imei>',
        category: 'osint',
        examples: ['/imei ***************']
      },
      {
        command: '/phone',
        description: 'Investigate phone number',
        usage: '/phone <phone-number>',
        category: 'osint',
        examples: ['/phone +6281234567890']
      },
      {
        command: '/email',
        description: 'Investigate email address',
        usage: '/email <email-address>',
        category: 'osint',
        examples: ['/email <EMAIL>']
      },

      // Security Commands
      {
        command: '/scan',
        description: 'Start vulnerability scan',
        usage: '/scan <url>',
        category: 'security',
        examples: ['/scan https://example.com']
      },
      {
        command: '/dorking',
        description: 'Google dorking search',
        usage: '/dorking <search-query>',
        category: 'security',
        examples: ['/dorking site:example.com filetype:pdf']
      },

      // Admin Commands
      {
        command: '/stats',
        description: 'Show bot statistics',
        usage: '/stats',
        category: 'admin',
        adminOnly: true
      },
      {
        command: '/users',
        description: 'List bot users',
        usage: '/users',
        category: 'admin',
        adminOnly: true
      },
      {
        command: '/broadcast',
        description: 'Send broadcast message',
        usage: '/broadcast <message>',
        category: 'admin',
        adminOnly: true
      }
        usage: '/nik <16-digit-nik>'
      },
      {
        command: '/npwp',
        description: 'Check NPWP information',
        usage: '/npwp <15-digit-npwp>'
      },
      {
        command: '/imei',
        description: 'Check IMEI information',
        usage: '/imei <15-digit-imei>'
      },
      {
        command: '/status',
        description: 'Check your account status',
        usage: '/status'
      },
      {
        command: '/plan',
        description: 'Check your subscription plan',
        usage: '/plan'
      }
    ]
  }

  private async loadAdminNumbers() {
    try {
      const admins = await db.query(`
        SELECT phone_number FROM users
        WHERE role IN ('admin', 'super_admin')
        AND phone_number IS NOT NULL
      `)
      this.adminNumbers = admins.map((admin: any) => admin.phone_number)
      console.log(`📱 Loaded ${this.adminNumbers.length} admin numbers`)
    } catch (error) {
      console.error('Error loading admin numbers:', error)
    }
  }

  private async initializeTables() {
    try {
      // Bot sessions table
      await db.query(`
        CREATE TABLE IF NOT EXISTS bot_sessions (
          id VARCHAR(36) PRIMARY KEY,
          session_name VARCHAR(100) NOT NULL,
          created_by INT,
          status ENUM('disconnected', 'connecting', 'connected', 'error', 'qr_ready') DEFAULT 'disconnected',
          phone_number VARCHAR(20),
          device_info JSON,
          statistics JSON,
          qr_code TEXT,
          qr_code_image LONGTEXT,
          last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          connected_at TIMESTAMP NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
        )
      `)

      // Bot users table
      await db.query(`
        CREATE TABLE IF NOT EXISTS bot_users (
          phone_number VARCHAR(20) PRIMARY KEY,
          name VARCHAR(255),
          user_id INT,
          plan ENUM('Free', 'Pro', 'Expert', 'Elite') DEFAULT 'Free',
          daily_usage JSON,
          is_blocked BOOLEAN DEFAULT FALSE,
          joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
          INDEX idx_user_id (user_id),
          INDEX idx_plan (plan),
          INDEX idx_joined_at (joined_at)
        )
      `)

      // Bot messages log table
      await db.query(`
        CREATE TABLE IF NOT EXISTS bot_messages (
          id VARCHAR(36) PRIMARY KEY,
          session_id VARCHAR(36),
          phone_number VARCHAR(20),
          message_type ENUM('incoming', 'outgoing') NOT NULL,
          content TEXT,
          command VARCHAR(50),
          response_time INT,
          success BOOLEAN DEFAULT TRUE,
          error_message TEXT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (session_id) REFERENCES bot_sessions(id) ON DELETE CASCADE,
          INDEX idx_session_id (session_id),
          INDEX idx_phone_number (phone_number),
          INDEX idx_created_at (created_at)
        )
      `)

      console.log('✅ WhatsApp Bot tables initialized')
    } catch (error) {
      console.error('❌ Error initializing bot tables:', error)
    }
  }

  // Create new bot session with QR code
  async createSession(sessionName: string, createdBy: number): Promise<string> {
    const sessionId = crypto.randomUUID()

    const session: BotSession = {
      id: sessionId,
      client: null,
      status: 'connecting',
      sessionName,
      createdBy,
      lastActivity: new Date(),
      statistics: {
        messagesReceived: 0,
        messagesSent: 0,
        commandsExecuted: 0,
        activeUsers: 0
      }
    }

    this.sessions.set(sessionId, session)

    // Save to database
    await db.query(`
      INSERT INTO bot_sessions (id, session_name, created_by, status, statistics, created_at)
      VALUES (?, ?, ?, 'connecting', ?, NOW())
    `, [sessionId, sessionName, createdBy, JSON.stringify(session.statistics)])

    // Start WhatsApp client
    this.startWhatsAppClient(sessionId)

    return sessionId
  }

  // Start WhatsApp client with enhanced QR code handling
  private async startWhatsAppClient(sessionId: string) {
    const session = this.sessions.get(sessionId)
    if (!session) return

    try {
      console.log(`📱 Starting WhatsApp client for session ${sessionId}`)

      const client = await create({
        session: session.sessionName,
        multiDevice: true,
        folderNameToken: 'kodexguard-tokens',
        mkdirFolderToken: './tokens',
        headless: true,
        devtools: false,
        useChrome: true,
        debug: false,
        logQR: false,
        browserArgs: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--single-process',
          '--disable-gpu'
        ],
        refreshQR: 15000, // Refresh QR every 15 seconds
        autoClose: 60000, // Auto close after 1 minute if not connected
        qrTimeout: 0, // No timeout for QR
        authTimeout: 0, // No timeout for auth
        waitForLogin: true,
        logger: console,
        browserRevision: '',
        addProxy: [],
        userProxy: '',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        onQRCode: async (base64Qr: string, asciiQR: string, attempts: number, urlCode?: string) => {
          console.log(`📱 QR Code generated for session ${sessionId} (attempt ${attempts})`)

          // Generate QR code image
          try {
            const qrCodeImage = await qrcode.toDataURL(base64Qr, {
              errorCorrectionLevel: 'M',
              type: 'image/png',
              quality: 0.92,
              margin: 1,
              color: {
                dark: '#000000',
                light: '#FFFFFF'
              },
              width: 256
            })

            session.qrCode = base64Qr
            session.qrCodeImage = qrCodeImage
            session.status = 'qr_ready'
            session.lastActivity = new Date()

            // Update database
            await db.query(`
              UPDATE bot_sessions
              SET qr_code = ?, qr_code_image = ?, status = 'qr_ready', last_activity = NOW()
              WHERE id = ?
            `, [base64Qr, qrCodeImage, sessionId])

            console.log(`✅ QR Code ready for session ${sessionId}`)
          } catch (error) {
            console.error('Error generating QR code image:', error)
          }
        },
        statusFind: (statusSession: string, session: string) => {
          console.log(`📱 Status update for ${session}: ${statusSession}`)

          if (statusSession === 'isLogged') {
            this.handleClientConnected(sessionId)
          } else if (statusSession === 'notLogged') {
            this.handleClientDisconnected(sessionId, 'Not logged in')
          } else if (statusSession === 'browserClose') {
            this.handleClientDisconnected(sessionId, 'Browser closed')
          } else if (statusSession === 'qrReadSuccess') {
            console.log(`✅ QR Code scanned for session ${sessionId}`)
          }
        }
      })

      session.client = client
      session.status = 'connected'
      session.connectedAt = new Date()
      session.lastActivity = new Date()

      // Get device info
      const deviceInfo = await this.getDeviceInfo(client)
      session.deviceInfo = deviceInfo

      // Setup message handlers
      this.setupMessageHandlers(sessionId, client)

      // Update database
      await db.query(`
        UPDATE bot_sessions
        SET status = 'connected', connected_at = NOW(), device_info = ?, last_activity = NOW()
        WHERE id = ?
      `, [JSON.stringify(deviceInfo), sessionId])

      console.log(`✅ WhatsApp client connected for session ${sessionId}`)

    } catch (error: any) {
      console.error(`❌ Error starting WhatsApp client for session ${sessionId}:`, error)
      session.status = 'error'
      session.error = error.message
      session.lastActivity = new Date()

      await db.query(`
        UPDATE bot_sessions
        SET status = 'error', last_activity = NOW()
        WHERE id = ?
      `, [sessionId])
    }
  }

  // Setup message handlers for the WhatsApp client
  private setupMessageHandlers(sessionId: string, client: Whatsapp) {
    const session = this.sessions.get(sessionId)
    if (!session) return

    client.onMessage(async (message: Message) => {
      try {
        const startTime = Date.now()

        // Update statistics
        session.statistics.messagesReceived++
        session.lastActivity = new Date()

        // Skip group messages and status updates
        if (message.isGroupMsg || message.type === 'chat' && message.body === '') {
          return
        }

        const phoneNumber = message.from.replace('@c.us', '')
        const messageBody = message.body.trim()

        console.log(`📱 Message from ${phoneNumber}: ${messageBody}`)

        // Get or create bot user
        const botUser = await this.getOrCreateBotUser(phoneNumber, message.sender.pushname)

        // Check if user is blocked
        if (botUser.isBlocked) {
          await client.sendText(message.from, '🚫 Your access has been restricted. Please contact support.')
          return
        }

        // Check daily usage limits
        const usageCheck = this.checkDailyUsage(botUser)
        if (!usageCheck.allowed) {
          await client.sendText(message.from, usageCheck.message)
          return
        }

        // Process command or regular message
        let response = ''
        let success = true
        let command = ''

        if (messageBody.startsWith('/')) {
          // Handle command
          command = messageBody.split(' ')[0].toLowerCase()
          response = await this.processCommand(messageBody, botUser, client, message)
          session.statistics.commandsExecuted++
        } else {
          // Handle regular message
          response = await this.processRegularMessage(messageBody, botUser)
        }

        // Send response
        if (response) {
          await client.sendText(message.from, response)
          session.statistics.messagesSent++
        }

        // Log message
        const responseTime = Date.now() - startTime
        await this.logMessage(sessionId, phoneNumber, 'incoming', messageBody, command, responseTime, success)

        // Update user activity
        await this.updateUserActivity(phoneNumber)

      } catch (error: any) {
        console.error('Error handling message:', error)
        await client.sendText(message.from, '❌ An error occurred while processing your message. Please try again.')
      }
    })

    // Handle connection events
    client.onStateChange((state: any) => {
      console.log(`📱 State change for session ${sessionId}:`, state)
      session.lastActivity = new Date()
    })

    client.onStreamChange((state: any) => {
      console.log(`📱 Stream change for session ${sessionId}:`, state)
    })

    client.onIncomingCall(async (call: any) => {
      console.log(`📞 Incoming call for session ${sessionId}:`, call)
      // Reject incoming calls
      await client.rejectCall(call.id)
    })
  }

  // Process bot commands
  private async processCommand(messageBody: string, botUser: BotUser, client: Whatsapp, message: Message): Promise<string> {
    const parts = messageBody.split(' ')
    const command = parts[0].toLowerCase()
    const args = parts.slice(1)

    // Find command definition
    const commandDef = this.commands.find(cmd => cmd.command === command)
    if (!commandDef) {
      return `❌ Unknown command: ${command}\n\nType /help to see available commands.`
    }

    // Check admin permissions
    if (commandDef.adminOnly && !this.isAdmin(botUser.phoneNumber)) {
      return '🚫 This command requires admin privileges.'
    }

    try {
      switch (command) {
        case '/help':
          return this.handleHelpCommand(args, botUser)

        case '/start':
          return this.handleStartCommand(botUser)

        case '/status':
          return this.handleStatusCommand(botUser)

        case '/plan':
          return this.handlePlanCommand(botUser)

        case '/nik':
          return await this.handleNIKCommand(args, botUser)

        case '/npwp':
          return await this.handleNPWPCommand(args, botUser)

        case '/imei':
          return await this.handleIMEICommand(args, botUser)

        case '/phone':
          return await this.handlePhoneCommand(args, botUser)

        case '/email':
          return await this.handleEmailCommand(args, botUser)

        case '/osint':
          return await this.handleOSINTCommand(args, botUser)

        case '/scan':
          return await this.handleScanCommand(args, botUser)

        case '/dorking':
          return await this.handleDorkingCommand(args, botUser)

        case '/stats':
          return await this.handleStatsCommand(botUser)

        case '/users':
          return await this.handleUsersCommand(botUser)

        case '/broadcast':
          return await this.handleBroadcastCommand(args, botUser, client)

        default:
          return `❌ Command ${command} is not implemented yet.`
      }
    } catch (error: any) {
      console.error(`Error processing command ${command}:`, error)
      return `❌ Error processing command: ${error.message}`
    }
  }

  // Handle help command
  private handleHelpCommand(args: string[], botUser: BotUser): string {
    const category = args[0]?.toLowerCase()

    let response = '🤖 *KodeXGuard Bot Commands*\n\n'

    if (category) {
      const categoryCommands = this.commands.filter(cmd =>
        cmd.category === category &&
        (!cmd.adminOnly || this.isAdmin(botUser.phoneNumber))
      )

      if (categoryCommands.length === 0) {
        return `❌ No commands found for category: ${category}\n\nAvailable categories: general, osint, security, admin`
      }

      response += `*${category.toUpperCase()} Commands:*\n\n`
      categoryCommands.forEach(cmd => {
        response += `${cmd.command} - ${cmd.description}\n`
        response += `Usage: ${cmd.usage}\n`
        if (cmd.examples) {
          response += `Examples: ${cmd.examples.join(', ')}\n`
        }
        response += '\n'
      })
    } else {
      // Show categories
      const categories = ['general', 'osint', 'security']
      if (this.isAdmin(botUser.phoneNumber)) {
        categories.push('admin')
      }

      response += '*Available Categories:*\n\n'
      categories.forEach(cat => {
        const count = this.commands.filter(cmd =>
          cmd.category === cat &&
          (!cmd.adminOnly || this.isAdmin(botUser.phoneNumber))
        ).length
        response += `📁 ${cat} (${count} commands)\n`
      })

      response += '\n*Usage:* /help [category]\n'
      response += 'Example: /help osint'
    }

    return response
  }

  // Handle start command
  private handleStartCommand(botUser: BotUser): string {
    return `🚀 *Welcome to KodeXGuard Bot!*

🛡️ Your cybersecurity companion for OSINT investigations, vulnerability scanning, and security analysis.

*Your Account:*
📱 Phone: ${botUser.phoneNumber}
👤 Name: ${botUser.name || 'Not set'}
💎 Plan: ${botUser.plan}
📅 Joined: ${botUser.joinedAt.toLocaleDateString()}

*Quick Start:*
• Type /help to see all commands
• Type /status to check your usage
• Type /plan to see your limits

*Popular Commands:*
• /nik <number> - Check Indonesian ID
• /imei <number> - Check device info
• /scan <url> - Vulnerability scan
• /email <email> - Email investigation

Need help? Contact our support team!`
  }

  // Handle status command
  private handleStatusCommand(botUser: BotUser): string {
    const today = new Date().toDateString()
    const lastReset = botUser.dailyUsage.lastReset.toDateString()

    // Reset daily usage if needed
    if (today !== lastReset) {
      botUser.dailyUsage = {
        scans: 0,
        osintQueries: 0,
        lastReset: new Date()
      }
    }

    const limits = this.getPlanLimits(botUser.plan)

    return `📊 *Account Status*

*Account Info:*
👤 Name: ${botUser.name || 'Not set'}
📱 Phone: ${botUser.phoneNumber}
💎 Plan: ${botUser.plan}
📅 Member since: ${botUser.joinedAt.toLocaleDateString()}

*Today's Usage:*
🔍 OSINT Queries: ${botUser.dailyUsage.osintQueries}/${limits.osintQueries === -1 ? '∞' : limits.osintQueries}
🛡️ Vulnerability Scans: ${botUser.dailyUsage.scans}/${limits.scans === -1 ? '∞' : limits.scans}

*Status:* ${botUser.isBlocked ? '🚫 Blocked' : '✅ Active'}
*Last Activity:* ${new Date().toLocaleString()}`
  }

  async createSession(sessionName: string = 'kodexguard-wa'): Promise<string> {
    const sessionId = `wa_${sessionName}_${Date.now()}`
    
    const session: BotSession = {
      id: sessionId,
      client: null,
      status: 'connecting',
      lastActivity: new Date()
    }

    this.sessions.set(sessionId, session)

    try {
      // Create WhatsApp client
      const client = await create({
        session: sessionName,
        multiDevice: true,
        folderNameToken: process.env.WHATSAPP_SESSION_PATH || './sessions/whatsapp',
        mkdirFolderToken: '',
        headless: true,
        devtools: false,
        useChrome: true,
        debug: false,
        logQR: false,
        browserWS: '',
        browserArgs: ['--no-sandbox', '--disable-setuid-sandbox'],
        puppeteerOptions: {},
        disableWelcome: true,
        updatesLog: false,
        autoClose: 60000,
        createPathFileToken: true,
        waitForLogin: true
      }, 
      (base64Qr: string, asciiQR: string, attempts: number, urlCode?: string) => {
        // QR Code callback
        session.qrCode = base64Qr
        console.log('QR Code generated for session:', sessionId)
        console.log('Scan attempts:', attempts)
      },
      (statusSession: string, session: string) => {
        // Status callback
        console.log('Session status:', statusSession, 'for session:', session)
        
        if (statusSession === 'isLogged') {
          this.sessions.get(sessionId)!.status = 'connected'
          this.sessions.get(sessionId)!.connectedAt = new Date()
        } else if (statusSession === 'notLogged') {
          this.sessions.get(sessionId)!.status = 'disconnected'
        }
      },
      undefined,
      (browserSessionToken: any) => {
        console.log('Browser session token:', browserSessionToken)
      })

      session.client = client
      session.status = 'connected'
      session.connectedAt = new Date()

      // Set up message handler
      client.onMessage(async (message: Message) => {
        await this.handleMessage(sessionId, message)
      })

      // Save session to database
      await this.saveSessionToDatabase(sessionId, sessionName)

      console.log(`WhatsApp bot session ${sessionId} created successfully`)
      return sessionId

    } catch (error) {
      session.status = 'error'
      session.error = error instanceof Error ? error.message : 'Unknown error'
      console.error('Failed to create WhatsApp session:', error)
      throw error
    }
  }

  async getSession(sessionId: string): Promise<BotSession | null> {
    return this.sessions.get(sessionId) || null
  }

  async getAllSessions(): Promise<BotSession[]> {
    return Array.from(this.sessions.values())
  }

  async disconnectSession(sessionId: string): Promise<boolean> {
    const session = this.sessions.get(sessionId)
    if (!session || !session.client) {
      return false
    }

    try {
      await session.client.close()
      session.status = 'disconnected'
      session.client = null
      
      // Update database
      await this.updateSessionStatus(sessionId, 'disconnected')
      
      return true
    } catch (error) {
      console.error('Failed to disconnect WhatsApp session:', error)
      return false
    }
  }

  private async handleMessage(sessionId: string, message: Message): Promise<void> {
    try {
      const session = this.sessions.get(sessionId)
      if (!session || !session.client) return

      // Update last activity
      session.lastActivity = new Date()

      // Only process text messages that start with /
      if (message.type !== 'chat' || !message.body.startsWith('/')) {
        return
      }

      const [command, ...args] = message.body.split(' ')
      const userId = await this.getUserByPhone(message.from)

      if (!userId) {
        await session.client.sendText(
          message.from,
          '❌ You need to register on KodeXGuard platform first.\n\nVisit: https://kodexguard.com/register'
        )
        return
      }

      // Process command
      await this.processCommand(sessionId, message.from, command, args, userId)

    } catch (error) {
      console.error('Error handling WhatsApp message:', error)
    }
  }

  private async processCommand(
    sessionId: string, 
    from: string, 
    command: string, 
    args: string[], 
    userId: number
  ): Promise<void> {
    const session = this.sessions.get(sessionId)
    if (!session || !session.client) return

    switch (command.toLowerCase()) {
      case '/help':
        await this.sendHelpMessage(session.client, from)
        break

      case '/scan':
        if (args.length === 0) {
          await session.client.sendText(from, '❌ Usage: /scan <url>')
          return
        }
        await this.handleScanCommand(session.client, from, args[0], userId)
        break

      case '/nik':
        if (args.length === 0) {
          await session.client.sendText(from, '❌ Usage: /nik <16-digit-nik>')
          return
        }
        await this.handleNikCommand(session.client, from, args[0], userId)
        break

      case '/npwp':
        if (args.length === 0) {
          await session.client.sendText(from, '❌ Usage: /npwp <15-digit-npwp>')
          return
        }
        await this.handleNpwpCommand(session.client, from, args[0], userId)
        break

      case '/imei':
        if (args.length === 0) {
          await session.client.sendText(from, '❌ Usage: /imei <15-digit-imei>')
          return
        }
        await this.handleImeiCommand(session.client, from, args[0], userId)
        break

      case '/status':
        await this.handleStatusCommand(session.client, from, userId)
        break

      case '/plan':
        await this.handlePlanCommand(session.client, from, userId)
        break

      default:
        await session.client.sendText(
          from,
          '❌ Unknown command. Type /help to see available commands.'
        )
    }
  }

  private async sendHelpMessage(client: Whatsapp, to: string): Promise<void> {
    let helpText = '🤖 *KodeXGuard Bot Commands*\n\n'
    
    this.commands.forEach(cmd => {
      helpText += `*${cmd.command}*\n${cmd.description}\nUsage: \`${cmd.usage}\`\n\n`
    })
    
    helpText += '📱 *Need help?* Visit: https://kodexguard.com/docs'
    
    await client.sendText(to, helpText)
  }

  private async handleScanCommand(client: Whatsapp, from: string, url: string, userId: number): Promise<void> {
    try {
      await client.sendText(from, '🔍 Starting vulnerability scan...')
      
      // Start scan (simplified)
      const scanId = await this.scannerService.startScan({
        id: `bot_${Date.now()}`,
        targetUrl: url,
        scanType: 'basic',
        options: {
          checkSQLi: true,
          checkXSS: true,
          checkLFI: false,
          checkRCE: false
        }
      }, userId)

      await client.sendText(
        from,
        `✅ Scan started!\nScan ID: ${scanId}\n\nYou'll receive results when scan completes.`
      )
    } catch (error) {
      await client.sendText(from, `❌ Scan failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  private async handleNikCommand(client: Whatsapp, from: string, nik: string, userId: number): Promise<void> {
    try {
      await client.sendText(from, '🔍 Investigating NIK...')
      
      const results = await this.osintService.investigate({
        id: Date.now(),
        type: 'nik',
        value: nik,
        sources: ['all']
      })

      let resultText = '📋 *NIK Investigation Results*\n\n'
      results.forEach(result => {
        resultText += `*${result.source}*: ${result.found ? '✅ Found' : '❌ Not Found'}\n`
        if (result.data && typeof result.data === 'object') {
          resultText += `Confidence: ${Math.round(result.confidence * 100)}%\n`
        }
        resultText += '\n'
      })

      await client.sendText(from, resultText)
    } catch (error) {
      await client.sendText(from, `❌ NIK investigation failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  private async handleNpwpCommand(client: Whatsapp, from: string, npwp: string, userId: number): Promise<void> {
    try {
      await client.sendText(from, '🔍 Investigating NPWP...')

      const results = await this.osintService.investigate({
        id: Date.now(),
        type: 'npwp',
        value: npwp,
        sources: ['all']
      })

      let resultText = '📋 *NPWP Investigation Results*\n\n'
      results.forEach(result => {
        resultText += `*${result.source}*: ${result.found ? '✅ Found' : '❌ Not Found'}\n`
        if (result.data && typeof result.data === 'object') {
          resultText += `Confidence: ${Math.round(result.confidence * 100)}%\n`

          // Add specific NPWP data if available
          if (result.data.npwp) {
            resultText += `NPWP: ${result.data.npwp}\n`
          }
          if (result.data.name) {
            resultText += `Name: ${result.data.name}\n`
          }
          if (result.data.address) {
            resultText += `Address: ${result.data.address}\n`
          }
          if (result.data.riskLevel) {
            resultText += `Risk Level: ${result.data.riskLevel}\n`
          }
        }
        resultText += '\n'
      })

      resultText += '⚠️ *Disclaimer*: This information is for educational purposes only.'

      await client.sendText(from, resultText)
    } catch (error) {
      await client.sendText(from, `❌ NPWP investigation failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  private async handleImeiCommand(client: Whatsapp, from: string, imei: string, userId: number): Promise<void> {
    try {
      await client.sendText(from, '🔍 Investigating IMEI...')

      const results = await this.osintService.investigate({
        id: Date.now(),
        type: 'imei',
        value: imei,
        sources: ['all']
      })

      let resultText = '📋 *IMEI Investigation Results*\n\n'
      results.forEach(result => {
        resultText += `*${result.source}*: ${result.found ? '✅ Found' : '❌ Not Found'}\n`
        if (result.data && typeof result.data === 'object') {
          resultText += `Confidence: ${Math.round(result.confidence * 100)}%\n`

          // Add specific IMEI data if available
          if (result.data.imei) {
            resultText += `IMEI: ${result.data.imei}\n`
          }
          if (result.data.manufacturer) {
            resultText += `Manufacturer: ${result.data.manufacturer}\n`
          }
          if (result.data.model) {
            resultText += `Model: ${result.data.model}\n`
          }
          if (result.data.riskLevel) {
            resultText += `Risk Level: ${result.data.riskLevel}\n`
          }
          if (result.data.operators && result.data.operators.length > 0) {
            resultText += `Operators: ${result.data.operators.map((op: any) => op.operator).join(', ')}\n`
          }
        }
        resultText += '\n'
      })

      resultText += '⚠️ *Disclaimer*: This information is for educational purposes only.'

      await client.sendText(from, resultText)
    } catch (error) {
      await client.sendText(from, `❌ IMEI investigation failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  private async handleStatusCommand(client: Whatsapp, from: string, userId: number): Promise<void> {
    try {
      const [userRows] = await db.query(
        'SELECT username, plan, score, level FROM users WHERE id = ?',
        [userId]
      )
      
      if (!userRows || (userRows as any[]).length === 0) {
        await client.sendText(from, '❌ User not found')
        return
      }
      
      const user = (userRows as any[])[0]
      
      const statusText = `👤 *Account Status*\n\n` +
        `Username: ${user.username}\n` +
        `Plan: ${user.plan}\n` +
        `Score: ${user.score}\n` +
        `Level: ${user.level}`
      
      await client.sendText(from, statusText)
    } catch (error) {
      await client.sendText(from, '❌ Failed to get status')
    }
  }

  private async handlePlanCommand(client: Whatsapp, from: string, userId: number): Promise<void> {
    await client.sendText(from, '💼 Plan management feature coming soon!')
  }

  private async getUserByPhone(phone: string): Promise<number | null> {
    try {
      // Clean phone number
      const cleanPhone = phone.replace(/\D/g, '')
      
      const [rows] = await db.query(
        'SELECT id FROM users WHERE phone = ? OR phone = ? OR phone = ?',
        [phone, cleanPhone, `+${cleanPhone}`]
      )
      
      if (rows && (rows as any[]).length > 0) {
        return (rows as any[])[0].id
      }
      
      return null
    } catch (error) {
      console.error('Error getting user by phone:', error)
      return null
    }
  }

  private async saveSessionToDatabase(sessionId: string, sessionName: string): Promise<void> {
    try {
      await db.query(
        `INSERT INTO bot_instances (id, type, name, status, created_at) 
         VALUES (?, 'whatsapp', ?, 'connected', NOW())
         ON DUPLICATE KEY UPDATE status = 'connected', updated_at = NOW()`,
        [sessionId, sessionName]
      )
    } catch (error) {
      console.error('Error saving session to database:', error)
    }
  }

  private async updateSessionStatus(sessionId: string, status: string): Promise<void> {
    try {
      await db.query(
        'UPDATE bot_instances SET status = ?, updated_at = NOW() WHERE id = ?',
        [status, sessionId]
      )
    } catch (error) {
      console.error('Error updating session status:', error)
    }
  }
}
