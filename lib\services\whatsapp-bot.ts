import { create, Whatsapp, Message } from 'venom-bot'
import { db } from '../database'
import { OSINTService } from './osint'
import { VulnerabilityScanner } from './scanner'

export interface BotSession {
  id: string
  client: Whatsapp | null
  status: 'disconnected' | 'connecting' | 'connected' | 'error'
  qrCode?: string
  lastActivity: Date
  connectedAt?: Date
  error?: string
}

export interface BotCommand {
  command: string
  description: string
  usage: string
  adminOnly?: boolean
}

export class WhatsAppBotService {
  private sessions: Map<string, BotSession> = new Map()
  private osintService: OSINTService
  private scannerService: VulnerabilityScanner
  private commands: BotCommand[]

  constructor() {
    this.osintService = new OSINTService()
    this.scannerService = new VulnerabilityScanner()
    this.commands = [
      {
        command: '/help',
        description: 'Show available commands',
        usage: '/help'
      },
      {
        command: '/scan',
        description: 'Start vulnerability scan',
        usage: '/scan <url>'
      },
      {
        command: '/osint',
        description: 'OSINT investigation',
        usage: '/osint <type> <value>'
      },
      {
        command: '/nik',
        description: 'Check NIK information',
        usage: '/nik <16-digit-nik>'
      },
      {
        command: '/npwp',
        description: 'Check NPWP information',
        usage: '/npwp <15-digit-npwp>'
      },
      {
        command: '/imei',
        description: 'Check IMEI information',
        usage: '/imei <15-digit-imei>'
      },
      {
        command: '/status',
        description: 'Check your account status',
        usage: '/status'
      },
      {
        command: '/plan',
        description: 'Check your subscription plan',
        usage: '/plan'
      }
    ]
  }

  async createSession(sessionName: string = 'kodexguard-wa'): Promise<string> {
    const sessionId = `wa_${sessionName}_${Date.now()}`
    
    const session: BotSession = {
      id: sessionId,
      client: null,
      status: 'connecting',
      lastActivity: new Date()
    }

    this.sessions.set(sessionId, session)

    try {
      // Create WhatsApp client
      const client = await create({
        session: sessionName,
        multiDevice: true,
        folderNameToken: process.env.WHATSAPP_SESSION_PATH || './sessions/whatsapp',
        mkdirFolderToken: '',
        headless: true,
        devtools: false,
        useChrome: true,
        debug: false,
        logQR: false,
        browserWS: '',
        browserArgs: ['--no-sandbox', '--disable-setuid-sandbox'],
        puppeteerOptions: {},
        disableWelcome: true,
        updatesLog: false,
        autoClose: 60000,
        createPathFileToken: true,
        waitForLogin: true
      }, 
      (base64Qr: string, asciiQR: string, attempts: number, urlCode?: string) => {
        // QR Code callback
        session.qrCode = base64Qr
        console.log('QR Code generated for session:', sessionId)
        console.log('Scan attempts:', attempts)
      },
      (statusSession: string, session: string) => {
        // Status callback
        console.log('Session status:', statusSession, 'for session:', session)
        
        if (statusSession === 'isLogged') {
          this.sessions.get(sessionId)!.status = 'connected'
          this.sessions.get(sessionId)!.connectedAt = new Date()
        } else if (statusSession === 'notLogged') {
          this.sessions.get(sessionId)!.status = 'disconnected'
        }
      },
      undefined,
      (browserSessionToken: any) => {
        console.log('Browser session token:', browserSessionToken)
      })

      session.client = client
      session.status = 'connected'
      session.connectedAt = new Date()

      // Set up message handler
      client.onMessage(async (message: Message) => {
        await this.handleMessage(sessionId, message)
      })

      // Save session to database
      await this.saveSessionToDatabase(sessionId, sessionName)

      console.log(`WhatsApp bot session ${sessionId} created successfully`)
      return sessionId

    } catch (error) {
      session.status = 'error'
      session.error = error instanceof Error ? error.message : 'Unknown error'
      console.error('Failed to create WhatsApp session:', error)
      throw error
    }
  }

  async getSession(sessionId: string): Promise<BotSession | null> {
    return this.sessions.get(sessionId) || null
  }

  async getAllSessions(): Promise<BotSession[]> {
    return Array.from(this.sessions.values())
  }

  async disconnectSession(sessionId: string): Promise<boolean> {
    const session = this.sessions.get(sessionId)
    if (!session || !session.client) {
      return false
    }

    try {
      await session.client.close()
      session.status = 'disconnected'
      session.client = null
      
      // Update database
      await this.updateSessionStatus(sessionId, 'disconnected')
      
      return true
    } catch (error) {
      console.error('Failed to disconnect WhatsApp session:', error)
      return false
    }
  }

  private async handleMessage(sessionId: string, message: Message): Promise<void> {
    try {
      const session = this.sessions.get(sessionId)
      if (!session || !session.client) return

      // Update last activity
      session.lastActivity = new Date()

      // Only process text messages that start with /
      if (message.type !== 'chat' || !message.body.startsWith('/')) {
        return
      }

      const [command, ...args] = message.body.split(' ')
      const userId = await this.getUserByPhone(message.from)

      if (!userId) {
        await session.client.sendText(
          message.from,
          '❌ You need to register on KodeXGuard platform first.\n\nVisit: https://kodexguard.com/register'
        )
        return
      }

      // Process command
      await this.processCommand(sessionId, message.from, command, args, userId)

    } catch (error) {
      console.error('Error handling WhatsApp message:', error)
    }
  }

  private async processCommand(
    sessionId: string, 
    from: string, 
    command: string, 
    args: string[], 
    userId: number
  ): Promise<void> {
    const session = this.sessions.get(sessionId)
    if (!session || !session.client) return

    switch (command.toLowerCase()) {
      case '/help':
        await this.sendHelpMessage(session.client, from)
        break

      case '/scan':
        if (args.length === 0) {
          await session.client.sendText(from, '❌ Usage: /scan <url>')
          return
        }
        await this.handleScanCommand(session.client, from, args[0], userId)
        break

      case '/nik':
        if (args.length === 0) {
          await session.client.sendText(from, '❌ Usage: /nik <16-digit-nik>')
          return
        }
        await this.handleNikCommand(session.client, from, args[0], userId)
        break

      case '/npwp':
        if (args.length === 0) {
          await session.client.sendText(from, '❌ Usage: /npwp <15-digit-npwp>')
          return
        }
        await this.handleNpwpCommand(session.client, from, args[0], userId)
        break

      case '/imei':
        if (args.length === 0) {
          await session.client.sendText(from, '❌ Usage: /imei <15-digit-imei>')
          return
        }
        await this.handleImeiCommand(session.client, from, args[0], userId)
        break

      case '/status':
        await this.handleStatusCommand(session.client, from, userId)
        break

      case '/plan':
        await this.handlePlanCommand(session.client, from, userId)
        break

      default:
        await session.client.sendText(
          from,
          '❌ Unknown command. Type /help to see available commands.'
        )
    }
  }

  private async sendHelpMessage(client: Whatsapp, to: string): Promise<void> {
    let helpText = '🤖 *KodeXGuard Bot Commands*\n\n'
    
    this.commands.forEach(cmd => {
      helpText += `*${cmd.command}*\n${cmd.description}\nUsage: \`${cmd.usage}\`\n\n`
    })
    
    helpText += '📱 *Need help?* Visit: https://kodexguard.com/docs'
    
    await client.sendText(to, helpText)
  }

  private async handleScanCommand(client: Whatsapp, from: string, url: string, userId: number): Promise<void> {
    try {
      await client.sendText(from, '🔍 Starting vulnerability scan...')
      
      // Start scan (simplified)
      const scanId = await this.scannerService.startScan({
        id: `bot_${Date.now()}`,
        targetUrl: url,
        scanType: 'basic',
        options: {
          checkSQLi: true,
          checkXSS: true,
          checkLFI: false,
          checkRCE: false
        }
      }, userId)

      await client.sendText(
        from,
        `✅ Scan started!\nScan ID: ${scanId}\n\nYou'll receive results when scan completes.`
      )
    } catch (error) {
      await client.sendText(from, `❌ Scan failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  private async handleNikCommand(client: Whatsapp, from: string, nik: string, userId: number): Promise<void> {
    try {
      await client.sendText(from, '🔍 Investigating NIK...')
      
      const results = await this.osintService.investigate({
        id: Date.now(),
        type: 'nik',
        value: nik,
        sources: ['all']
      })

      let resultText = '📋 *NIK Investigation Results*\n\n'
      results.forEach(result => {
        resultText += `*${result.source}*: ${result.found ? '✅ Found' : '❌ Not Found'}\n`
        if (result.data && typeof result.data === 'object') {
          resultText += `Confidence: ${Math.round(result.confidence * 100)}%\n`
        }
        resultText += '\n'
      })

      await client.sendText(from, resultText)
    } catch (error) {
      await client.sendText(from, `❌ NIK investigation failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  private async handleNpwpCommand(client: Whatsapp, from: string, npwp: string, userId: number): Promise<void> {
    try {
      await client.sendText(from, '🔍 Investigating NPWP...')

      const results = await this.osintService.investigate({
        id: Date.now(),
        type: 'npwp',
        value: npwp,
        sources: ['all']
      })

      let resultText = '📋 *NPWP Investigation Results*\n\n'
      results.forEach(result => {
        resultText += `*${result.source}*: ${result.found ? '✅ Found' : '❌ Not Found'}\n`
        if (result.data && typeof result.data === 'object') {
          resultText += `Confidence: ${Math.round(result.confidence * 100)}%\n`

          // Add specific NPWP data if available
          if (result.data.npwp) {
            resultText += `NPWP: ${result.data.npwp}\n`
          }
          if (result.data.name) {
            resultText += `Name: ${result.data.name}\n`
          }
          if (result.data.address) {
            resultText += `Address: ${result.data.address}\n`
          }
          if (result.data.riskLevel) {
            resultText += `Risk Level: ${result.data.riskLevel}\n`
          }
        }
        resultText += '\n'
      })

      resultText += '⚠️ *Disclaimer*: This information is for educational purposes only.'

      await client.sendText(from, resultText)
    } catch (error) {
      await client.sendText(from, `❌ NPWP investigation failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  private async handleImeiCommand(client: Whatsapp, from: string, imei: string, userId: number): Promise<void> {
    try {
      await client.sendText(from, '🔍 Investigating IMEI...')

      const results = await this.osintService.investigate({
        id: Date.now(),
        type: 'imei',
        value: imei,
        sources: ['all']
      })

      let resultText = '📋 *IMEI Investigation Results*\n\n'
      results.forEach(result => {
        resultText += `*${result.source}*: ${result.found ? '✅ Found' : '❌ Not Found'}\n`
        if (result.data && typeof result.data === 'object') {
          resultText += `Confidence: ${Math.round(result.confidence * 100)}%\n`

          // Add specific IMEI data if available
          if (result.data.imei) {
            resultText += `IMEI: ${result.data.imei}\n`
          }
          if (result.data.manufacturer) {
            resultText += `Manufacturer: ${result.data.manufacturer}\n`
          }
          if (result.data.model) {
            resultText += `Model: ${result.data.model}\n`
          }
          if (result.data.riskLevel) {
            resultText += `Risk Level: ${result.data.riskLevel}\n`
          }
          if (result.data.operators && result.data.operators.length > 0) {
            resultText += `Operators: ${result.data.operators.map((op: any) => op.operator).join(', ')}\n`
          }
        }
        resultText += '\n'
      })

      resultText += '⚠️ *Disclaimer*: This information is for educational purposes only.'

      await client.sendText(from, resultText)
    } catch (error) {
      await client.sendText(from, `❌ IMEI investigation failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  private async handleStatusCommand(client: Whatsapp, from: string, userId: number): Promise<void> {
    try {
      const [userRows] = await db.query(
        'SELECT username, plan, score, level FROM users WHERE id = ?',
        [userId]
      )
      
      if (!userRows || (userRows as any[]).length === 0) {
        await client.sendText(from, '❌ User not found')
        return
      }
      
      const user = (userRows as any[])[0]
      
      const statusText = `👤 *Account Status*\n\n` +
        `Username: ${user.username}\n` +
        `Plan: ${user.plan}\n` +
        `Score: ${user.score}\n` +
        `Level: ${user.level}`
      
      await client.sendText(from, statusText)
    } catch (error) {
      await client.sendText(from, '❌ Failed to get status')
    }
  }

  private async handlePlanCommand(client: Whatsapp, from: string, userId: number): Promise<void> {
    await client.sendText(from, '💼 Plan management feature coming soon!')
  }

  private async getUserByPhone(phone: string): Promise<number | null> {
    try {
      // Clean phone number
      const cleanPhone = phone.replace(/\D/g, '')
      
      const [rows] = await db.query(
        'SELECT id FROM users WHERE phone = ? OR phone = ? OR phone = ?',
        [phone, cleanPhone, `+${cleanPhone}`]
      )
      
      if (rows && (rows as any[]).length > 0) {
        return (rows as any[])[0].id
      }
      
      return null
    } catch (error) {
      console.error('Error getting user by phone:', error)
      return null
    }
  }

  private async saveSessionToDatabase(sessionId: string, sessionName: string): Promise<void> {
    try {
      await db.query(
        `INSERT INTO bot_instances (id, type, name, status, created_at) 
         VALUES (?, 'whatsapp', ?, 'connected', NOW())
         ON DUPLICATE KEY UPDATE status = 'connected', updated_at = NOW()`,
        [sessionId, sessionName]
      )
    } catch (error) {
      console.error('Error saving session to database:', error)
    }
  }

  private async updateSessionStatus(sessionId: string, status: string): Promise<void> {
    try {
      await db.query(
        'UPDATE bot_instances SET status = ?, updated_at = NOW() WHERE id = ?',
        [status, sessionId]
      )
    } catch (error) {
      console.error('Error updating session status:', error)
    }
  }
}
