"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./components/DashboardSidebar.tsx":
/*!*****************************************!*\
  !*** ./components/DashboardSidebar.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DashboardSidebar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Terminal,Trophy,UserCheck,Users,Webhook,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/home.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Terminal,Trophy,UserCheck,Users,Webhook,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Terminal,Trophy,UserCheck,Users,Webhook,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Terminal,Trophy,UserCheck,Users,Webhook,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Terminal,Trophy,UserCheck,Users,Webhook,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Terminal,Trophy,UserCheck,Users,Webhook,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Terminal,Trophy,UserCheck,Users,Webhook,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Terminal,Trophy,UserCheck,Users,Webhook,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Terminal,Trophy,UserCheck,Users,Webhook,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/terminal.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Terminal,Trophy,UserCheck,Users,Webhook,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Terminal,Trophy,UserCheck,Users,Webhook,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Terminal,Trophy,UserCheck,Users,Webhook,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Terminal,Trophy,UserCheck,Users,Webhook,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Terminal,Trophy,UserCheck,Users,Webhook,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/monitor.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Terminal,Trophy,UserCheck,Users,Webhook,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Terminal,Trophy,UserCheck,Users,Webhook,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Terminal,Trophy,UserCheck,Users,Webhook,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Terminal,Trophy,UserCheck,Users,Webhook,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Terminal,Trophy,UserCheck,Users,Webhook,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Terminal,Trophy,UserCheck,Users,Webhook,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/cog.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Terminal,Trophy,UserCheck,Users,Webhook,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield-check.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Terminal,Trophy,UserCheck,Users,Webhook,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Terminal,Trophy,UserCheck,Users,Webhook,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/webhook.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Terminal,Trophy,UserCheck,Users,Webhook,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-check.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction DashboardSidebar(param) {\n    let { user, isCollapsed, onToggle } = param;\n    var _this = this;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const [expandedItems, setExpandedItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Define sidebar items based on user role and plan\n    const sidebarItems = [\n        // Main Dashboard\n        {\n            id: \"dashboard\",\n            title: \"Dashboard\",\n            icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            href: \"/dashboard\"\n        },\n        // Core Tools (Available to all users)\n        {\n            id: \"tools\",\n            title: \"Security Tools\",\n            icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            children: [\n                {\n                    id: \"osint\",\n                    title: \"OSINT Lookup\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                    href: \"/osint\",\n                    badge: \"Popular\"\n                },\n                {\n                    id: \"scanner\",\n                    title: \"Vulnerability Scanner\",\n                    icon: Scan,\n                    href: \"/scanner\",\n                    premium: true\n                },\n                {\n                    id: \"file-analyzer\",\n                    title: \"File Analyzer\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                    href: \"/file-analyzer\",\n                    premium: true\n                },\n                {\n                    id: \"cve\",\n                    title: \"CVE Database\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                    href: \"/cve\"\n                },\n                {\n                    id: \"dorking\",\n                    title: \"Google Dorking\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                    href: \"/dorking\"\n                }\n            ]\n        },\n        // Advanced Tools (Premium plans)\n        {\n            id: \"advanced\",\n            title: \"Advanced Tools\",\n            icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            plans: [\n                \"Hobby\",\n                \"Bughunter\",\n                \"Cybersecurity\"\n            ],\n            children: [\n                {\n                    id: \"playground\",\n                    title: \"API Playground\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                    href: \"/playground\"\n                },\n                {\n                    id: \"tools-advanced\",\n                    title: \"Advanced Tools\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                    href: \"/tools\"\n                }\n            ]\n        },\n        // Community & Learning\n        {\n            id: \"community\",\n            title: \"Community\",\n            icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            children: [\n                {\n                    id: \"leaderboard\",\n                    title: \"Leaderboard\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n                    href: \"/leaderboard\"\n                },\n                {\n                    id: \"community-hub\",\n                    title: \"Community Hub\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n                    href: \"/community\"\n                }\n            ]\n        },\n        // Admin Tools (Admin and Super Admin only)\n        ...(user === null || user === void 0 ? void 0 : user.role) === \"admin\" || (user === null || user === void 0 ? void 0 : user.role) === \"super_admin\" ? [\n            {\n                id: \"admin\",\n                title: \"Administration\",\n                icon: UserCog,\n                roles: [\n                    \"admin\",\n                    \"super_admin\"\n                ],\n                children: [\n                    {\n                        id: \"user-management\",\n                        title: \"User Management\",\n                        icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n                        href: \"/dashboard?section=users\",\n                        roles: [\n                            \"admin\",\n                            \"super_admin\"\n                        ]\n                    },\n                    {\n                        id: \"analytics\",\n                        title: \"Analytics\",\n                        icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n                        href: \"/dashboard?section=analytics\",\n                        roles: [\n                            \"admin\",\n                            \"super_admin\"\n                        ]\n                    },\n                    {\n                        id: \"monitoring\",\n                        title: \"System Monitor\",\n                        icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n                        href: \"/dashboard?section=monitoring\",\n                        roles: [\n                            \"admin\",\n                            \"super_admin\"\n                        ]\n                    }\n                ]\n            }\n        ] : [],\n        // Super Admin Tools (Super Admin only)\n        ...(user === null || user === void 0 ? void 0 : user.role) === \"super_admin\" ? [\n            {\n                id: \"superadmin\",\n                title: \"Super Admin\",\n                icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n                roles: [\n                    \"super_admin\"\n                ],\n                children: [\n                    {\n                        id: \"website-settings\",\n                        title: \"Website Settings\",\n                        icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n                        href: \"/dashboard?section=website-settings\",\n                        roles: [\n                            \"super_admin\"\n                        ]\n                    },\n                    {\n                        id: \"payment-management\",\n                        title: \"Payment Management\",\n                        icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n                        href: \"/dashboard?section=payments\",\n                        roles: [\n                            \"super_admin\"\n                        ]\n                    },\n                    {\n                        id: \"bot-management\",\n                        title: \"Bot Management\",\n                        icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n                        href: \"/dashboard?section=bots\",\n                        roles: [\n                            \"super_admin\"\n                        ]\n                    },\n                    {\n                        id: \"whatsapp-bot\",\n                        title: \"WhatsApp Bot\",\n                        icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n                        href: \"/dashboard?section=whatsapp\",\n                        roles: [\n                            \"super_admin\"\n                        ]\n                    },\n                    {\n                        id: \"telegram-bot\",\n                        title: \"Telegram Bot\",\n                        icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n                        href: \"/dashboard?section=telegram\",\n                        roles: [\n                            \"super_admin\"\n                        ]\n                    },\n                    {\n                        id: \"system-config\",\n                        title: \"System Config\",\n                        icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"],\n                        href: \"/dashboard?section=system-config\",\n                        roles: [\n                            \"super_admin\"\n                        ]\n                    },\n                    {\n                        id: \"security-center\",\n                        title: \"Security Center\",\n                        icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"],\n                        href: \"/dashboard?section=security\",\n                        roles: [\n                            \"super_admin\"\n                        ]\n                    },\n                    {\n                        id: \"database-admin\",\n                        title: \"Database Admin\",\n                        icon: DatabaseIcon,\n                        href: \"/dashboard?section=database\",\n                        roles: [\n                            \"super_admin\"\n                        ]\n                    },\n                    {\n                        id: \"server-management\",\n                        title: \"Server Management\",\n                        icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"],\n                        href: \"/dashboard?section=server\",\n                        roles: [\n                            \"super_admin\"\n                        ]\n                    },\n                    {\n                        id: \"api-management\",\n                        title: \"API Management\",\n                        icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"],\n                        href: \"/dashboard?section=api\",\n                        roles: [\n                            \"super_admin\"\n                        ]\n                    }\n                ]\n            }\n        ] : [],\n        // Personal Section\n        {\n            id: \"personal\",\n            title: \"Personal\",\n            icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"],\n            children: [\n                {\n                    id: \"profile\",\n                    title: \"Profile\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"],\n                    href: \"/profile\"\n                },\n                {\n                    id: \"plan\",\n                    title: \"Subscription\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n                    href: \"/plan\"\n                },\n                {\n                    id: \"settings\",\n                    title: \"Settings\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n                    href: \"/settings\"\n                }\n            ]\n        }\n    ];\n    // Filter items based on user role and plan\n    const filterItems = (items)=>{\n        return items.filter((item)=>{\n            // Check role permissions\n            if (item.roles && !item.roles.includes((user === null || user === void 0 ? void 0 : user.role) || \"user\")) {\n                return false;\n            }\n            // Check plan permissions\n            if (item.plans && !item.plans.includes((user === null || user === void 0 ? void 0 : user.plan) || \"Free\")) {\n                return false;\n            }\n            // Check premium access\n            if (item.premium && (user === null || user === void 0 ? void 0 : user.plan) === \"Free\") {\n                return false;\n            }\n            // Filter children recursively\n            if (item.children) {\n                item.children = filterItems(item.children);\n            }\n            return true;\n        });\n    };\n    const filteredItems = filterItems(sidebarItems);\n    const toggleExpanded = (itemId)=>{\n        setExpandedItems((prev)=>prev.includes(itemId) ? prev.filter((id)=>id !== itemId) : [\n                ...prev,\n                itemId\n            ]);\n    };\n    const isActive = (href)=>{\n        if (false) {}\n        if (href === \"/dashboard\") {\n            return pathname === \"/dashboard\" && !window.location.search;\n        }\n        // Check for section-based URLs\n        if (href.includes(\"?section=\")) {\n            const currentUrl = \"\".concat(pathname).concat(window.location.search);\n            return currentUrl === href;\n        }\n        return pathname === href || pathname.startsWith(href + \"/\");\n    };\n    const renderSidebarItem = function(item) {\n        let level = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;\n        var _item_children;\n        const Icon = item.icon;\n        const hasChildren = item.children && item.children.length > 0;\n        const isExpanded = expandedItems.includes(item.id);\n        const active = item.href ? isActive(item.href) : false;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"\".concat(level > 0 ? \"ml-4\" : \"\"),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    onClick: ()=>{\n                        if (hasChildren) {\n                            toggleExpanded(item.id);\n                        } else if (item.href) {\n                            router.push(item.href);\n                        }\n                    },\n                    className: \"\\n            flex items-center justify-between p-3 rounded-lg cursor-pointer transition-all duration-200\\n            \".concat(active ? \"bg-cyber-primary/20 text-cyber-primary border-l-4 border-cyber-primary\" : \"text-gray-300 hover:bg-gray-800 hover:text-white\", \"\\n            \").concat(item.comingSoon ? \"opacity-50 cursor-not-allowed\" : \"\", \"\\n          \"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                    className: \"h-5 w-5 \".concat(active ? \"text-cyber-primary\" : \"\")\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                                    lineNumber: 384,\n                                    columnNumber: 13\n                                }, _this),\n                                !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: item.title\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                                            lineNumber: 387,\n                                            columnNumber: 17\n                                        }, _this),\n                                        item.badge && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-2 py-1 text-xs bg-cyber-primary/20 text-cyber-primary rounded-full\",\n                                            children: item.badge\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                                            lineNumber: 389,\n                                            columnNumber: 19\n                                        }, _this),\n                                        item.premium && (user === null || user === void 0 ? void 0 : user.plan) === \"Free\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-2 py-1 text-xs bg-yellow-500/20 text-yellow-400 rounded-full\",\n                                            children: \"PRO\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                                            lineNumber: 394,\n                                            columnNumber: 19\n                                        }, _this),\n                                        item.comingSoon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-2 py-1 text-xs bg-gray-500/20 text-gray-400 rounded-full\",\n                                            children: \"Soon\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                                            lineNumber: 399,\n                                            columnNumber: 19\n                                        }, _this)\n                                    ]\n                                }, void 0, true)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                            lineNumber: 383,\n                            columnNumber: 11\n                        }, _this),\n                        !isCollapsed && hasChildren && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"transform transition-transform \".concat(isExpanded ? \"rotate-90\" : \"\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-4 h-4\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M9 5l7 7-7 7\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                                    lineNumber: 410,\n                                    columnNumber: 17\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                                lineNumber: 409,\n                                columnNumber: 15\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                            lineNumber: 408,\n                            columnNumber: 13\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                    lineNumber: 366,\n                    columnNumber: 9\n                }, _this),\n                !isCollapsed && hasChildren && isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-2 space-y-1\",\n                    children: (_item_children = item.children) === null || _item_children === void 0 ? void 0 : _item_children.map((child)=>renderSidebarItem(child, level + 1))\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                    lineNumber: 417,\n                    columnNumber: 11\n                }, _this)\n            ]\n        }, item.id, true, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n            lineNumber: 365,\n            columnNumber: 7\n        }, _this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\\n      bg-gray-900 border-r border-gray-800 transition-all duration-300 flex flex-col\\n      \".concat(isCollapsed ? \"w-16\" : \"w-64\", \"\\n    \"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-b border-gray-800\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-lg font-bold text-white\",\n                                    children: \"KodeXGuard\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                                    lineNumber: 435,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400\",\n                                    children: [\n                                        (user === null || user === void 0 ? void 0 : user.role) === \"super_admin\" ? \"\\uD83D\\uDC51 Super Admin\" : (user === null || user === void 0 ? void 0 : user.role) === \"admin\" ? \"\\uD83D\\uDEE1️ Admin\" : \"\\uD83D\\uDD12 User\",\n                                        \" • \",\n                                        user === null || user === void 0 ? void 0 : user.plan\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                                    lineNumber: 436,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                            lineNumber: 434,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onToggle,\n                            className: \"p-2 rounded-lg hover:bg-gray-800 text-gray-400 hover:text-white transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-5 h-5\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M4 6h16M4 12h16M4 18h16\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                                    lineNumber: 448,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                                lineNumber: 447,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                            lineNumber: 443,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                    lineNumber: 432,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                lineNumber: 431,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto p-4 space-y-2\",\n                children: filteredItems.map((item)=>renderSidebarItem(item))\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                lineNumber: 455,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-t border-gray-800\",\n                children: !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-xs text-gray-500 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"\\xa9 2024 KodeXGuard\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                            lineNumber: 463,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"Cybersecurity Platform\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                            lineNumber: 464,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                    lineNumber: 462,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                lineNumber: 460,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n        lineNumber: 426,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardSidebar, \"IawfwaZbxNshDbEnUCH2X8OEMKg=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname\n    ];\n});\n_c = DashboardSidebar;\nvar _c;\n$RefreshReg$(_c, \"DashboardSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/DashboardSidebar.tsx\n"));

/***/ })

});