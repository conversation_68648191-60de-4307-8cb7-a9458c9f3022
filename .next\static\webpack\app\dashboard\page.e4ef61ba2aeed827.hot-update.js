"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js":
/*!**********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/menu.js ***!
  \**********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Menu; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * lucide-react v0.292.0 - ISC\n */ \nconst Menu = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Menu\", [\n    [\n        \"line\",\n        {\n            x1: \"4\",\n            x2: \"20\",\n            y1: \"12\",\n            y2: \"12\",\n            key: \"1e0a9i\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"4\",\n            x2: \"20\",\n            y1: \"6\",\n            y2: \"6\",\n            key: \"1owob3\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"4\",\n            x2: \"20\",\n            y1: \"18\",\n            y2: \"18\",\n            key: \"yk5zj1\"\n        }\n    ]\n]);\n //# sourceMappingURL=menu.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js":
/*!*******************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/x.js ***!
  \*******************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ X; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * lucide-react v0.292.0 - ISC\n */ \nconst X = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"X\", [\n    [\n        \"path\",\n        {\n            d: \"M18 6 6 18\",\n            key: \"1bl5f8\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m6 6 12 12\",\n            key: \"d8bk6v\"\n        }\n    ]\n]);\n //# sourceMappingURL=x.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMveC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQWFNLE1BQUFBLElBQUlDLGdFQUFnQkEsQ0FBQyxLQUFLO0lBQzlCO1FBQUM7UUFBUTtZQUFFQyxHQUFHO1lBQWNDLEtBQUs7UUFBQTtLQUFVO0lBQzNDO1FBQUM7UUFBUTtZQUFFRCxHQUFHO1lBQWNDLEtBQUs7UUFBQTtLQUFVO0NBQzVDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi8uLi9zcmMvaWNvbnMveC50cz9iYzM4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBjcmVhdGVMdWNpZGVJY29uIGZyb20gJy4uL2NyZWF0ZUx1Y2lkZUljb24nO1xuXG4vKipcbiAqIEBjb21wb25lbnQgQG5hbWUgWFxuICogQGRlc2NyaXB0aW9uIEx1Y2lkZSBTVkcgaWNvbiBjb21wb25lbnQsIHJlbmRlcnMgU1ZHIEVsZW1lbnQgd2l0aCBjaGlsZHJlbi5cbiAqXG4gKiBAcHJldmlldyAhW2ltZ10oZGF0YTppbWFnZS9zdmcreG1sO2Jhc2U2NCxQSE4yWnlBZ2VHMXNibk05SW1oMGRIQTZMeTkzZDNjdWR6TXViM0puTHpJd01EQXZjM1puSWdvZ0lIZHBaSFJvUFNJeU5DSUtJQ0JvWldsbmFIUTlJakkwSWdvZ0lIWnBaWGRDYjNnOUlqQWdNQ0F5TkNBeU5DSUtJQ0JtYVd4c1BTSnViMjVsSWdvZ0lITjBjbTlyWlQwaUl6QXdNQ0lnYzNSNWJHVTlJbUpoWTJ0bmNtOTFibVF0WTI5c2IzSTZJQ05tWm1ZN0lHSnZjbVJsY2kxeVlXUnBkWE02SURKd2VDSUtJQ0J6ZEhKdmEyVXRkMmxrZEdnOUlqSWlDaUFnYzNSeWIydGxMV3hwYm1WallYQTlJbkp2ZFc1a0lnb2dJSE4wY205clpTMXNhVzVsYW05cGJqMGljbTkxYm1RaUNqNEtJQ0E4Y0dGMGFDQmtQU0pOTVRnZ05pQTJJREU0SWlBdlBnb2dJRHh3WVhSb0lHUTlJbTAySURZZ01USWdNVElpSUM4K0Nqd3ZjM1puUGdvPSkgLSBodHRwczovL2x1Y2lkZS5kZXYvaWNvbnMveFxuICogQHNlZSBodHRwczovL2x1Y2lkZS5kZXYvZ3VpZGUvcGFja2FnZXMvbHVjaWRlLXJlYWN0IC0gRG9jdW1lbnRhdGlvblxuICpcbiAqIEBwYXJhbSB7T2JqZWN0fSBwcm9wcyAtIEx1Y2lkZSBpY29ucyBwcm9wcyBhbmQgYW55IHZhbGlkIFNWRyBhdHRyaWJ1dGVcbiAqIEByZXR1cm5zIHtKU1guRWxlbWVudH0gSlNYIEVsZW1lbnRcbiAqXG4gKi9cbmNvbnN0IFggPSBjcmVhdGVMdWNpZGVJY29uKCdYJywgW1xuICBbJ3BhdGgnLCB7IGQ6ICdNMTggNiA2IDE4Jywga2V5OiAnMWJsNWY4JyB9XSxcbiAgWydwYXRoJywgeyBkOiAnbTYgNiAxMiAxMicsIGtleTogJ2Q4Yms2dicgfV0sXG5dKTtcblxuZXhwb3J0IGRlZmF1bHQgWDtcbiJdLCJuYW1lcyI6WyJYIiwiY3JlYXRlTHVjaWRlSWNvbiIsImQiLCJrZXkiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/DashboardSidebar.tsx":
/*!*****************************************!*\
  !*** ./components/DashboardSidebar.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DashboardSidebar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/home.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/terminal.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-cog.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/monitor.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/cog.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield-check.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/webhook.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-check.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction DashboardSidebar(param) {\n    let { user, isCollapsed, onToggle } = param;\n    var _this = this;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const [expandedItems, setExpandedItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Define sidebar items based on user role and plan\n    const sidebarItems = [\n        // Main Dashboard\n        {\n            id: \"dashboard\",\n            title: \"Dashboard\",\n            icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            href: \"/dashboard\"\n        },\n        // Core Tools (Available to all users)\n        {\n            id: \"tools\",\n            title: \"Security Tools\",\n            icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            children: [\n                {\n                    id: \"osint\",\n                    title: \"OSINT Lookup\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                    href: \"/osint\",\n                    badge: \"Popular\"\n                },\n                {\n                    id: \"scanner\",\n                    title: \"Vulnerability Scanner\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                    href: \"/scanner\",\n                    premium: true\n                },\n                {\n                    id: \"file-analyzer\",\n                    title: \"File Analyzer\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                    href: \"/file-analyzer\",\n                    premium: true\n                },\n                {\n                    id: \"cve\",\n                    title: \"CVE Database\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                    href: \"/cve\"\n                },\n                {\n                    id: \"dorking\",\n                    title: \"Google Dorking\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                    href: \"/dorking\"\n                }\n            ]\n        },\n        // Advanced Tools (Premium plans)\n        {\n            id: \"advanced\",\n            title: \"Advanced Tools\",\n            icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            plans: [\n                \"Hobby\",\n                \"Bughunter\",\n                \"Cybersecurity\"\n            ],\n            children: [\n                {\n                    id: \"playground\",\n                    title: \"API Playground\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                    href: \"/playground\"\n                },\n                {\n                    id: \"tools-advanced\",\n                    title: \"Advanced Tools\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n                    href: \"/tools\"\n                }\n            ]\n        },\n        // Community & Learning\n        {\n            id: \"community\",\n            title: \"Community\",\n            icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n            children: [\n                {\n                    id: \"leaderboard\",\n                    title: \"Leaderboard\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n                    href: \"/leaderboard\"\n                },\n                {\n                    id: \"community-hub\",\n                    title: \"Community Hub\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n                    href: \"/community\"\n                }\n            ]\n        },\n        // Admin Tools (Admin and Super Admin only)\n        ...(user === null || user === void 0 ? void 0 : user.role) === \"admin\" || (user === null || user === void 0 ? void 0 : user.role) === \"super_admin\" ? [\n            {\n                id: \"admin\",\n                title: \"Administration\",\n                icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n                roles: [\n                    \"admin\",\n                    \"super_admin\"\n                ],\n                children: [\n                    {\n                        id: \"user-management\",\n                        title: \"User Management\",\n                        icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n                        href: \"/dashboard?section=users\",\n                        roles: [\n                            \"admin\",\n                            \"super_admin\"\n                        ]\n                    },\n                    {\n                        id: \"analytics\",\n                        title: \"Analytics\",\n                        icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n                        href: \"/dashboard?section=analytics\",\n                        roles: [\n                            \"admin\",\n                            \"super_admin\"\n                        ]\n                    },\n                    {\n                        id: \"monitoring\",\n                        title: \"System Monitor\",\n                        icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n                        href: \"/dashboard?section=monitoring\",\n                        roles: [\n                            \"admin\",\n                            \"super_admin\"\n                        ]\n                    }\n                ]\n            }\n        ] : [],\n        // Super Admin Tools (Super Admin only)\n        ...(user === null || user === void 0 ? void 0 : user.role) === \"super_admin\" ? [\n            {\n                id: \"superadmin\",\n                title: \"Super Admin\",\n                icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n                roles: [\n                    \"super_admin\"\n                ],\n                children: [\n                    {\n                        id: \"website-settings\",\n                        title: \"Website Settings\",\n                        icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n                        href: \"/dashboard?section=website-settings\",\n                        roles: [\n                            \"super_admin\"\n                        ]\n                    },\n                    {\n                        id: \"payment-management\",\n                        title: \"Payment Management\",\n                        icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n                        href: \"/dashboard?section=payments\",\n                        roles: [\n                            \"super_admin\"\n                        ]\n                    },\n                    {\n                        id: \"bot-management\",\n                        title: \"Bot Management\",\n                        icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"],\n                        href: \"/dashboard?section=bots\",\n                        roles: [\n                            \"super_admin\"\n                        ]\n                    },\n                    {\n                        id: \"whatsapp-bot\",\n                        title: \"WhatsApp Bot\",\n                        icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n                        href: \"/dashboard?section=whatsapp\",\n                        roles: [\n                            \"super_admin\"\n                        ]\n                    },\n                    {\n                        id: \"telegram-bot\",\n                        title: \"Telegram Bot\",\n                        icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"],\n                        href: \"/dashboard?section=telegram\",\n                        roles: [\n                            \"super_admin\"\n                        ]\n                    },\n                    {\n                        id: \"system-config\",\n                        title: \"System Config\",\n                        icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"],\n                        href: \"/dashboard?section=system-config\",\n                        roles: [\n                            \"super_admin\"\n                        ]\n                    },\n                    {\n                        id: \"security-center\",\n                        title: \"Security Center\",\n                        icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"],\n                        href: \"/dashboard?section=security\",\n                        roles: [\n                            \"super_admin\"\n                        ]\n                    },\n                    {\n                        id: \"database-admin\",\n                        title: \"Database Admin\",\n                        icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                        href: \"/dashboard?section=database\",\n                        roles: [\n                            \"super_admin\"\n                        ]\n                    },\n                    {\n                        id: \"server-management\",\n                        title: \"Server Management\",\n                        icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"],\n                        href: \"/dashboard?section=server\",\n                        roles: [\n                            \"super_admin\"\n                        ]\n                    },\n                    {\n                        id: \"api-management\",\n                        title: \"API Management\",\n                        icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"],\n                        href: \"/dashboard?section=api\",\n                        roles: [\n                            \"super_admin\"\n                        ]\n                    }\n                ]\n            }\n        ] : [],\n        // Personal Section\n        {\n            id: \"personal\",\n            title: \"Personal\",\n            icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"],\n            children: [\n                {\n                    id: \"profile\",\n                    title: \"Profile\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"],\n                    href: \"/profile\"\n                },\n                {\n                    id: \"plan\",\n                    title: \"Subscription\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n                    href: \"/plan\"\n                },\n                {\n                    id: \"settings\",\n                    title: \"Settings\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n                    href: \"/settings\"\n                }\n            ]\n        }\n    ];\n    // Filter items based on user role and plan\n    const filterItems = (items)=>{\n        return items.filter((item)=>{\n            // Check role permissions\n            if (item.roles && !item.roles.includes((user === null || user === void 0 ? void 0 : user.role) || \"user\")) {\n                return false;\n            }\n            // Check plan permissions\n            if (item.plans && !item.plans.includes((user === null || user === void 0 ? void 0 : user.plan) || \"Free\")) {\n                return false;\n            }\n            // Check premium access\n            if (item.premium && (user === null || user === void 0 ? void 0 : user.plan) === \"Free\") {\n                return false;\n            }\n            // Filter children recursively\n            if (item.children) {\n                item.children = filterItems(item.children);\n            }\n            return true;\n        });\n    };\n    const filteredItems = filterItems(sidebarItems);\n    const toggleExpanded = (itemId)=>{\n        setExpandedItems((prev)=>prev.includes(itemId) ? prev.filter((id)=>id !== itemId) : [\n                ...prev,\n                itemId\n            ]);\n    };\n    const isActive = (href)=>{\n        if (false) {}\n        if (href === \"/dashboard\") {\n            return pathname === \"/dashboard\" && !window.location.search;\n        }\n        // Check for section-based URLs\n        if (href.includes(\"?section=\")) {\n            const currentUrl = \"\".concat(pathname).concat(window.location.search);\n            return currentUrl === href;\n        }\n        return pathname === href || pathname.startsWith(href + \"/\");\n    };\n    const renderSidebarItem = function(item) {\n        let level = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;\n        var _item_children;\n        const Icon = item.icon;\n        const hasChildren = item.children && item.children.length > 0;\n        const isExpanded = expandedItems.includes(item.id);\n        const active = item.href ? isActive(item.href) : false;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"\".concat(level > 0 ? \"ml-4\" : \"\"),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    onClick: ()=>{\n                        if (hasChildren) {\n                            toggleExpanded(item.id);\n                        } else if (item.href) {\n                            router.push(item.href);\n                        }\n                    },\n                    className: \"\\n            flex items-center justify-between p-3 rounded-lg cursor-pointer transition-all duration-200\\n            \".concat(active ? \"bg-cyber-primary/20 text-cyber-primary border-l-4 border-cyber-primary\" : \"text-gray-300 hover:bg-gray-800 hover:text-white\", \"\\n            \").concat(item.comingSoon ? \"opacity-50 cursor-not-allowed\" : \"\", \"\\n          \"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                    className: \"h-5 w-5 \".concat(active ? \"text-cyber-primary\" : \"\")\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                                    lineNumber: 386,\n                                    columnNumber: 13\n                                }, _this),\n                                !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: item.title\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                                            lineNumber: 389,\n                                            columnNumber: 17\n                                        }, _this),\n                                        item.badge && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-2 py-1 text-xs bg-cyber-primary/20 text-cyber-primary rounded-full\",\n                                            children: item.badge\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                                            lineNumber: 391,\n                                            columnNumber: 19\n                                        }, _this),\n                                        item.premium && (user === null || user === void 0 ? void 0 : user.plan) === \"Free\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-2 py-1 text-xs bg-yellow-500/20 text-yellow-400 rounded-full\",\n                                            children: \"PRO\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                                            lineNumber: 396,\n                                            columnNumber: 19\n                                        }, _this),\n                                        item.comingSoon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-2 py-1 text-xs bg-gray-500/20 text-gray-400 rounded-full\",\n                                            children: \"Soon\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                                            lineNumber: 401,\n                                            columnNumber: 19\n                                        }, _this)\n                                    ]\n                                }, void 0, true)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                            lineNumber: 385,\n                            columnNumber: 11\n                        }, _this),\n                        !isCollapsed && hasChildren && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                            className: \"w-4 h-4 transform transition-transform \".concat(isExpanded ? \"rotate-90\" : \"\")\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                            lineNumber: 410,\n                            columnNumber: 13\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                    lineNumber: 368,\n                    columnNumber: 9\n                }, _this),\n                !isCollapsed && hasChildren && isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-2 space-y-1\",\n                    children: (_item_children = item.children) === null || _item_children === void 0 ? void 0 : _item_children.map((child)=>renderSidebarItem(child, level + 1))\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                    lineNumber: 415,\n                    columnNumber: 11\n                }, _this)\n            ]\n        }, item.id, true, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n            lineNumber: 367,\n            columnNumber: 7\n        }, _this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\\n      bg-gray-900 border-r border-gray-800 transition-all duration-300 flex flex-col\\n      \".concat(isCollapsed ? \"w-16\" : \"w-64\", \"\\n    \"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-b border-gray-800\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-lg font-bold text-white\",\n                                    children: \"KodeXGuard\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                                    lineNumber: 433,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400\",\n                                    children: [\n                                        (user === null || user === void 0 ? void 0 : user.role) === \"super_admin\" ? \"\\uD83D\\uDC51 Super Admin\" : (user === null || user === void 0 ? void 0 : user.role) === \"admin\" ? \"\\uD83D\\uDEE1️ Admin\" : \"\\uD83D\\uDD12 User\",\n                                        \" • \",\n                                        user === null || user === void 0 ? void 0 : user.plan\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                                    lineNumber: 434,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                            lineNumber: 432,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onToggle,\n                            className: \"p-2 rounded-lg hover:bg-gray-800 text-gray-400 hover:text-white transition-colors\",\n                            children: isCollapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                className: \"w-5 h-5\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                                lineNumber: 445,\n                                columnNumber: 28\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                className: \"w-5 h-5\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                                lineNumber: 445,\n                                columnNumber: 59\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                            lineNumber: 441,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                    lineNumber: 430,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                lineNumber: 429,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto p-4 space-y-2\",\n                children: filteredItems.map((item)=>renderSidebarItem(item))\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                lineNumber: 451,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-t border-gray-800\",\n                children: !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-xs text-gray-500 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"\\xa9 2024 KodeXGuard\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                            lineNumber: 459,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"Cybersecurity Platform\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                            lineNumber: 460,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                    lineNumber: 458,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                lineNumber: 456,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n        lineNumber: 424,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardSidebar, \"IawfwaZbxNshDbEnUCH2X8OEMKg=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname\n    ];\n});\n_c = DashboardSidebar;\nvar _c;\n$RefreshReg$(_c, \"DashboardSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/DashboardSidebar.tsx\n"));

/***/ })

});