'use client'

import { useState, useEffect } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import {
  Home,
  Search,
  Shield,
  FileText,
  Database,
  Globe,
  Zap,
  Trophy,
  Users,
  Settings,
  CreditCard,
  Bot,
  Monitor,
  BarChart3,
  MessageSquare,
  Crown,
  Server,
  Activity,
  UserCheck,
  Cog,
  Code,
  Terminal,
  Send,
  ShieldCheck,
  Webhook,
  Star,
  ChevronRight,
  Menu,
  X,
  Target,
  UserCog
} from 'lucide-react'

interface User {
  id: string
  username: string
  email: string
  role: 'user' | 'admin' | 'super_admin' | 'moderator'
  plan: 'Free' | 'Student' | 'Hobby' | 'Bughunter' | 'Cybersecurity'
  level: number
  score: number
}

interface SidebarItem {
  id: string
  title: string
  icon: any
  href?: string
  badge?: string
  children?: SidebarItem[]
  roles?: string[]
  plans?: string[]
  premium?: boolean
  comingSoon?: boolean
}

interface DashboardSidebarProps {
  user: User | null
  isCollapsed: boolean
  onToggle: () => void
  isMobile?: boolean
}

export default function DashboardSidebar({ user, isCollapsed, onToggle, isMobile = false }: DashboardSidebarProps) {
  const router = useRouter()
  const pathname = usePathname()
  const [expandedItems, setExpandedItems] = useState<string[]>([])

  // Define sidebar items based on user role and plan
  const sidebarItems: SidebarItem[] = [
    // Main Dashboard
    {
      id: 'dashboard',
      title: 'Dashboard',
      icon: Home,
      href: '/dashboard'
    },

    // Core Tools (Available to all users)
    {
      id: 'tools',
      title: 'Security Tools',
      icon: Shield,
      children: [
        {
          id: 'osint',
          title: 'OSINT Lookup',
          icon: Search,
          href: '/osint',
          badge: 'Popular'
        },
        {
          id: 'scanner',
          title: 'Vulnerability Scanner',
          icon: Target,
          href: '/scanner',
          premium: true
        },
        {
          id: 'file-analyzer',
          title: 'File Analyzer',
          icon: FileText,
          href: '/file-analyzer',
          premium: true
        },
        {
          id: 'cve',
          title: 'CVE Database',
          icon: Database,
          href: '/cve'
        },
        {
          id: 'dorking',
          title: 'Google Dorking',
          icon: Globe,
          href: '/dorking'
        }
      ]
    },

    // Advanced Tools (Premium plans)
    {
      id: 'advanced',
      title: 'Advanced Tools',
      icon: Zap,
      plans: ['Hobby', 'Bughunter', 'Cybersecurity'],
      children: [
        {
          id: 'playground',
          title: 'API Playground',
          icon: Code,
          href: '/playground'
        },
        {
          id: 'tools-advanced',
          title: 'Advanced Tools',
          icon: Terminal,
          href: '/tools'
        }
      ]
    },

    // Community & Learning
    {
      id: 'community',
      title: 'Community',
      icon: Users,
      children: [
        {
          id: 'leaderboard',
          title: 'Leaderboard',
          icon: Trophy,
          href: '/leaderboard'
        },
        {
          id: 'community-hub',
          title: 'Community Hub',
          icon: MessageSquare,
          href: '/community'
        }
      ]
    },

    // Admin Tools (Admin and Super Admin only)
    ...(user?.role === 'admin' || user?.role === 'super_admin' ? [{
      id: 'admin',
      title: 'Administration',
      icon: UserCog,
      roles: ['admin', 'super_admin'],
      children: [
        {
          id: 'user-management',
          title: 'User Management',
          icon: Users,
          href: '/dashboard?section=users',
          roles: ['admin', 'super_admin']
        },
        {
          id: 'analytics',
          title: 'Analytics',
          icon: BarChart3,
          href: '/dashboard?section=analytics',
          roles: ['admin', 'super_admin']
        },
        {
          id: 'monitoring',
          title: 'System Monitor',
          icon: Monitor,
          href: '/dashboard?section=monitoring',
          roles: ['admin', 'super_admin']
        }
      ]
    }] : []),

    // Super Admin Tools (Super Admin only)
    ...(user?.role === 'super_admin' ? [{
      id: 'superadmin',
      title: 'Super Admin',
      icon: Crown,
      roles: ['super_admin'],
      children: [
        {
          id: 'website-settings',
          title: 'Website Settings',
          icon: Settings,
          href: '/dashboard?section=website-settings',
          roles: ['super_admin']
        },
        {
          id: 'payment-management',
          title: 'Payment Management',
          icon: CreditCard,
          href: '/dashboard?section=payments',
          roles: ['super_admin']
        },
        {
          id: 'bot-management',
          title: 'Bot Management',
          icon: Bot,
          href: '/dashboard?section=bots',
          roles: ['super_admin']
        },
        {
          id: 'whatsapp-bot',
          title: 'WhatsApp Bot',
          icon: MessageSquare,
          href: '/dashboard?section=whatsapp',
          roles: ['super_admin']
        },
        {
          id: 'telegram-bot',
          title: 'Telegram Bot',
          icon: Send,
          href: '/dashboard?section=telegram',
          roles: ['super_admin']
        },
        {
          id: 'system-config',
          title: 'System Config',
          icon: Cog,
          href: '/dashboard?section=system-config',
          roles: ['super_admin']
        },
        {
          id: 'security-center',
          title: 'Security Center',
          icon: ShieldCheck,
          href: '/dashboard?section=security',
          roles: ['super_admin']
        },
        {
          id: 'database-admin',
          title: 'Database Admin',
          icon: Database,
          href: '/dashboard?section=database',
          roles: ['super_admin']
        },
        {
          id: 'server-management',
          title: 'Server Management',
          icon: Server,
          href: '/dashboard?section=server',
          roles: ['super_admin']
        },
        {
          id: 'api-management',
          title: 'API Management',
          icon: Webhook,
          href: '/dashboard?section=api',
          roles: ['super_admin']
        }
      ]
    }] : []),

    // Personal Section
    {
      id: 'personal',
      title: 'Personal',
      icon: UserCheck,
      children: [
        {
          id: 'profile',
          title: 'Profile',
          icon: UserCheck,
          href: '/profile'
        },
        {
          id: 'plan',
          title: 'Subscription',
          icon: CreditCard,
          href: '/plan'
        },
        {
          id: 'settings',
          title: 'Settings',
          icon: Settings,
          href: '/settings'
        }
      ]
    }
  ]

  // Filter items based on user role and plan
  const filterItems = (items: SidebarItem[]): SidebarItem[] => {
    return items.filter(item => {
      // Check role permissions
      if (item.roles && !item.roles.includes(user?.role || 'user')) {
        return false
      }

      // Check plan permissions
      if (item.plans && !item.plans.includes(user?.plan || 'Free')) {
        return false
      }

      // Check premium access
      if (item.premium && user?.plan === 'Free') {
        return false
      }

      // Filter children recursively
      if (item.children) {
        item.children = filterItems(item.children)
      }

      return true
    })
  }

  const filteredItems = filterItems(sidebarItems)

  const toggleExpanded = (itemId: string) => {
    setExpandedItems(prev =>
      prev.includes(itemId)
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
    )
  }

  const isActive = (href: string) => {
    if (typeof window === 'undefined') return false

    if (href === '/dashboard') {
      return pathname === '/dashboard' && !window.location.search
    }

    // Check for section-based URLs
    if (href.includes('?section=')) {
      const currentUrl = `${pathname}${window.location.search}`
      return currentUrl === href
    }

    return pathname === href || pathname.startsWith(href + '/')
  }

  const renderSidebarItem = (item: SidebarItem, level: number = 0) => {
    const Icon = item.icon
    const hasChildren = item.children && item.children.length > 0
    const isExpanded = expandedItems.includes(item.id)
    const active = item.href ? isActive(item.href) : false

    return (
      <div key={item.id} className={`${level > 0 ? 'ml-4' : ''}`}>
        <div
          onClick={() => {
            if (hasChildren) {
              toggleExpanded(item.id)
            } else if (item.href) {
              router.push(item.href)
            }
          }}
          className={`
            flex items-center justify-between p-3 rounded-lg cursor-pointer transition-all duration-300 ease-in-out
            transform hover:scale-[1.02] active:scale-[0.98]
            ${active
              ? 'bg-cyber-primary/20 text-cyber-primary border-l-4 border-cyber-primary shadow-lg shadow-cyber-primary/20'
              : 'text-gray-300 hover:bg-gray-800 hover:text-white hover:shadow-md'
            }
            ${item.comingSoon ? 'opacity-50 cursor-not-allowed' : ''}
          `}
        >
          <div className="flex items-center space-x-3">
            <Icon className={`h-5 w-5 ${active ? 'text-cyber-primary' : ''}`} />
            {!isCollapsed && (
              <>
                <span className="font-medium">{item.title}</span>
                {item.badge && (
                  <span className="px-2 py-1 text-xs bg-cyber-primary/20 text-cyber-primary rounded-full">
                    {item.badge}
                  </span>
                )}
                {item.premium && user?.plan === 'Free' && (
                  <span className="px-2 py-1 text-xs bg-yellow-500/20 text-yellow-400 rounded-full">
                    PRO
                  </span>
                )}
                {item.comingSoon && (
                  <span className="px-2 py-1 text-xs bg-gray-500/20 text-gray-400 rounded-full">
                    Soon
                  </span>
                )}
              </>
            )}
          </div>

          {!isCollapsed && hasChildren && (
            <ChevronRight className={`w-4 h-4 transform transition-transform ${isExpanded ? 'rotate-90' : ''}`} />
          )}
        </div>

        {!isCollapsed && hasChildren && isExpanded && (
          <div className="mt-2 space-y-1">
            {item.children?.map(child => renderSidebarItem(child, level + 1))}
          </div>
        )}
      </div>
    )
  }

  return (
    <div className={`
      bg-gray-900 border-r border-gray-800 transition-all duration-300 flex flex-col
      ${isCollapsed ? 'w-16' : 'w-64'}
      ${isMobile ? 'fixed inset-y-0 left-0 z-50' : 'relative'}
      ${isMobile && isCollapsed ? '-translate-x-full' : 'translate-x-0'}
    `}>
      {/* Header */}
      <div className="p-4 border-b border-gray-800">
        <div className="flex items-center justify-between">
          {!isCollapsed && (
            <div>
              <h2 className="text-lg font-bold text-white">KodeXGuard</h2>
              <p className="text-xs text-gray-400">
                {user?.role === 'super_admin' ? '👑 Super Admin' :
                 user?.role === 'admin' ? '🛡️ Admin' :
                 '🔒 User'} • {user?.plan}
              </p>
            </div>
          )}
          <button
            onClick={onToggle}
            className="p-2 rounded-lg hover:bg-gray-800 text-gray-400 hover:text-white transition-colors"
          >
            {isCollapsed ? <Menu className="w-5 h-5" /> : <X className="w-5 h-5" />}
          </button>
        </div>
      </div>

      {/* Navigation */}
      <div className="flex-1 overflow-y-auto p-4 space-y-2">
        {filteredItems.map(item => renderSidebarItem(item))}
      </div>

      {/* Footer */}
      <div className="p-4 border-t border-gray-800">
        {!isCollapsed && (
          <div className="text-xs text-gray-500 text-center">
            <p>© 2024 KodeXGuard</p>
            <p>Cybersecurity Platform</p>
          </div>
        )}
      </div>
    </div>
  )
}
