'use client'

import { <PERSON>actNode, useEffect, useState } from 'react'
import { useTheme } from '@/contexts/ThemeContext'
import CyberHeader from './CyberHeader'
import CyberFooter from './CyberFooter'
import DashboardSidebar from './DashboardSidebar'

interface BaseLayoutProps {
  children: ReactNode
  variant?: 'public' | 'dashboard' | 'auth'
  showHeader?: boolean
  showFooter?: boolean
  showSidebar?: boolean
  className?: string
  user?: {
    id: string
    username: string
    email: string
    fullName: string
    avatar?: string
    role: string
    plan: string
    level?: number
    score?: number
    streak?: number
  }
}

export default function BaseLayout({
  children,
  variant = 'public',
  showHeader = true,
  showFooter = true,
  showSidebar = false,
  className = '',
  user
}: BaseLayoutProps) {
  const { theme } = useTheme()
  const [loading, setLoading] = useState(true)
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const [isMobile, setIsMobile] = useState(false)

  useEffect(() => {
    // Check if user is logged in for public pages
    if (variant === 'public') {
      const checkAuth = async () => {
        try {
          const token = localStorage.getItem('token')
          const userData = localStorage.getItem('user')
          
          if (token && userData && !user) {
            // User is logged in but not passed as prop
            const parsedUser = JSON.parse(userData)
            // You might want to set user state here or redirect
          }
        } catch (error) {
          console.error('Auth check error:', error)
          localStorage.removeItem('token')
          localStorage.removeItem('user')
        } finally {
          setLoading(false)
        }
      }
      checkAuth()
    } else {
      setLoading(false)
    }

    // Handle mobile detection
    const handleResize = () => {
      setIsMobile(window.innerWidth < 1024)
    }
    
    handleResize()
    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [variant, user])

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed)
  }

  if (loading && variant === 'public') {
    return (
      <div className="min-h-screen bg-cyber-dark flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-2 border-cyber-primary border-t-transparent mx-auto mb-4"></div>
          <div className="text-cyber-primary font-medium">Loading...</div>
        </div>
      </div>
    )
  }

  // Auth layout (login/register pages)
  if (variant === 'auth') {
    return (
      <div className={`min-h-screen transition-colors duration-300 ${
        theme === 'dark' 
          ? 'bg-cyber-dark text-white' 
          : 'bg-gray-50 text-gray-900'
      } ${className}`}>
        {/* Cyber Grid Background for dark mode */}
        {theme === 'dark' && (
          <div className="fixed inset-0 pointer-events-none opacity-5 z-0">
            <div
              className="absolute inset-0"
              style={{
                backgroundImage: `
                  linear-gradient(rgba(0, 255, 255, 0.1) 1px, transparent 1px),
                  linear-gradient(90deg, rgba(0, 255, 255, 0.1) 1px, transparent 1px)
                `,
                backgroundSize: '50px 50px'
              }}
            ></div>
          </div>
        )}
        
        <div className="relative z-10">
          {children}
        </div>
      </div>
    )
  }

  // Dashboard layout
  if (variant === 'dashboard') {
    return (
      <div className={`min-h-screen transition-colors duration-300 ${
        theme === 'dark' 
          ? 'bg-cyber-dark text-white' 
          : 'bg-gray-50 text-gray-900'
      } ${className}`}>
        {/* Mobile Overlay */}
        {isMobile && !sidebarCollapsed && (
          <div
            className="fixed inset-0 bg-black bg-opacity-50 z-40"
            onClick={toggleSidebar}
          />
        )}

        {/* Sidebar */}
        {showSidebar && user && (
          <DashboardSidebar
            user={user}
            isCollapsed={sidebarCollapsed}
            onToggle={toggleSidebar}
            isMobile={isMobile}
          />
        )}

        {/* Main Content Area */}
        <div className={`flex flex-col min-h-screen transition-all duration-300 ${
          showSidebar && !sidebarCollapsed ? 'lg:ml-64' : ''
        }`}>
          {/* Dashboard Header */}
          {showHeader && (
            <header className={`fixed top-0 right-0 z-50 transition-all duration-300 ${
              theme === 'dark' 
                ? 'bg-cyber-dark/95 backdrop-blur-md border-b border-cyber-border' 
                : 'bg-white/95 backdrop-blur-md border-b border-gray-200'
            } ${showSidebar && !sidebarCollapsed ? 'left-64' : 'left-0'}`}>
              <div className="flex items-center justify-between h-16 px-4">
                {/* Header content will be passed as children or props */}
                <div className="flex items-center space-x-4">
                  {(!showSidebar || isMobile) && (
                    <button
                      onClick={toggleSidebar}
                      className={`p-2 rounded-lg transition-colors ${
                        theme === 'dark'
                          ? 'text-gray-300 hover:text-cyber-primary hover:bg-cyber-primary/10'
                          : 'text-gray-600 hover:text-blue-600 hover:bg-blue-50'
                      } lg:hidden`}
                    >
                      <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                      </svg>
                    </button>
                  )}
                </div>
                
                {/* User info and actions */}
                {user && (
                  <div className="flex items-center space-x-4">
                    <div className="text-right hidden sm:block">
                      <p className={`font-medium text-sm ${
                        theme === 'dark' ? 'text-white' : 'text-gray-900'
                      }`}>
                        {user.username}
                      </p>
                      <p className={`text-xs ${
                        theme === 'dark' ? 'text-gray-400' : 'text-gray-500'
                      }`}>
                        {user.plan} Plan
                      </p>
                    </div>
                    <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                      theme === 'dark' 
                        ? 'bg-cyber-primary/20' 
                        : 'bg-blue-100'
                    }`}>
                      <span className={`font-bold ${
                        theme === 'dark' ? 'text-cyber-primary' : 'text-blue-600'
                      }`}>
                        {user.username?.charAt(0).toUpperCase()}
                      </span>
                    </div>
                  </div>
                )}
              </div>
            </header>
          )}

          {/* Content */}
          <main className={`flex-1 transition-all duration-300 ${
            showHeader ? 'pt-16' : ''
          }`}>
            <div className="p-6">
              {children}
            </div>
          </main>
        </div>
      </div>
    )
  }

  // Public layout (default)
  return (
    <div className={`min-h-screen transition-colors duration-300 ${
      theme === 'dark' 
        ? 'bg-cyber-dark text-white' 
        : 'bg-white text-gray-900'
    } ${className}`}>
      {/* Cyber Grid Background for dark mode */}
      {theme === 'dark' && (
        <div className="fixed inset-0 pointer-events-none opacity-5 z-0">
          <div
            className="absolute inset-0"
            style={{
              backgroundImage: `
                linear-gradient(rgba(0, 255, 255, 0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(0, 255, 255, 0.1) 1px, transparent 1px)
              `,
              backgroundSize: '50px 50px'
            }}
          ></div>

          {/* Animated particles for cyberpunk effect */}
          <div className="absolute inset-0">
            {Array.from({ length: 20 }).map((_, i) => (
              <div
                key={i}
                className="absolute w-1 h-1 bg-cyber-primary opacity-30 animate-cyber-float"
                style={{
                  left: `${Math.random() * 100}%`,
                  top: `${Math.random() * 100}%`,
                  animationDelay: `${Math.random() * 5}s`,
                  animationDuration: `${3 + Math.random() * 4}s`
                }}
              ></div>
            ))}
          </div>

          {/* Corner Accents */}
          <div className={`absolute top-0 left-0 w-32 h-32 border-l-2 border-t-2 opacity-30 ${
            theme === 'dark' ? 'border-cyber-primary' : 'border-blue-500'
          }`}></div>
          <div className={`absolute top-0 right-0 w-32 h-32 border-r-2 border-t-2 opacity-30 ${
            theme === 'dark' ? 'border-cyber-secondary' : 'border-pink-500'
          }`}></div>
          <div className={`absolute bottom-0 left-0 w-32 h-32 border-l-2 border-b-2 opacity-30 ${
            theme === 'dark' ? 'border-cyber-accent' : 'border-yellow-500'
          }`}></div>
          <div className={`absolute bottom-0 right-0 w-32 h-32 border-r-2 border-b-2 opacity-30 ${
            theme === 'dark' ? 'border-cyber-primary' : 'border-blue-500'
          }`}></div>
        </div>
      )}

      {/* Content */}
      <div className="relative z-10">
        {showHeader && <CyberHeader user={user} />}
        
        <main className={showHeader ? 'pt-16' : ''}>
          {children}
        </main>
        
        {showFooter && <CyberFooter />}
      </div>
    </div>
  )
}
