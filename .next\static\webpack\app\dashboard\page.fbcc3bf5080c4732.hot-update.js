"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./components/DashboardSidebar.tsx":
/*!*****************************************!*\
  !*** ./components/DashboardSidebar.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DashboardSidebar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Scan_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,MessageSquare,Monitor,Scan,Search,Send,Server,Settings,Shield,ShieldCheck,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/home.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Scan_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,MessageSquare,Monitor,Scan,Search,Send,Server,Settings,Shield,ShieldCheck,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Scan_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,MessageSquare,Monitor,Scan,Search,Send,Server,Settings,Shield,ShieldCheck,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Scan_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,MessageSquare,Monitor,Scan,Search,Send,Server,Settings,Shield,ShieldCheck,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/scan.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Scan_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,MessageSquare,Monitor,Scan,Search,Send,Server,Settings,Shield,ShieldCheck,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Scan_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,MessageSquare,Monitor,Scan,Search,Send,Server,Settings,Shield,ShieldCheck,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Scan_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,MessageSquare,Monitor,Scan,Search,Send,Server,Settings,Shield,ShieldCheck,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Scan_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,MessageSquare,Monitor,Scan,Search,Send,Server,Settings,Shield,ShieldCheck,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Scan_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,MessageSquare,Monitor,Scan,Search,Send,Server,Settings,Shield,ShieldCheck,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Scan_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,MessageSquare,Monitor,Scan,Search,Send,Server,Settings,Shield,ShieldCheck,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/terminal.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Scan_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,MessageSquare,Monitor,Scan,Search,Send,Server,Settings,Shield,ShieldCheck,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Scan_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,MessageSquare,Monitor,Scan,Search,Send,Server,Settings,Shield,ShieldCheck,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Scan_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,MessageSquare,Monitor,Scan,Search,Send,Server,Settings,Shield,ShieldCheck,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Scan_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,MessageSquare,Monitor,Scan,Search,Send,Server,Settings,Shield,ShieldCheck,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-cog.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Scan_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,MessageSquare,Monitor,Scan,Search,Send,Server,Settings,Shield,ShieldCheck,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Scan_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,MessageSquare,Monitor,Scan,Search,Send,Server,Settings,Shield,ShieldCheck,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/monitor.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Scan_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,MessageSquare,Monitor,Scan,Search,Send,Server,Settings,Shield,ShieldCheck,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Scan_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,MessageSquare,Monitor,Scan,Search,Send,Server,Settings,Shield,ShieldCheck,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Scan_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,MessageSquare,Monitor,Scan,Search,Send,Server,Settings,Shield,ShieldCheck,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Scan_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,MessageSquare,Monitor,Scan,Search,Send,Server,Settings,Shield,ShieldCheck,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Scan_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,MessageSquare,Monitor,Scan,Search,Send,Server,Settings,Shield,ShieldCheck,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Scan_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,MessageSquare,Monitor,Scan,Search,Send,Server,Settings,Shield,ShieldCheck,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/cog.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Scan_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,MessageSquare,Monitor,Scan,Search,Send,Server,Settings,Shield,ShieldCheck,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield-check.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Scan_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,MessageSquare,Monitor,Scan,Search,Send,Server,Settings,Shield,ShieldCheck,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Scan_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,MessageSquare,Monitor,Scan,Search,Send,Server,Settings,Shield,ShieldCheck,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/webhook.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Scan_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,MessageSquare,Monitor,Scan,Search,Send,Server,Settings,Shield,ShieldCheck,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-check.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction DashboardSidebar(param) {\n    let { user, isCollapsed, onToggle } = param;\n    var _this = this;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const [expandedItems, setExpandedItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Define sidebar items based on user role and plan\n    const sidebarItems = [\n        // Main Dashboard\n        {\n            id: \"dashboard\",\n            title: \"Dashboard\",\n            icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Scan_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            href: \"/dashboard\"\n        },\n        // Core Tools (Available to all users)\n        {\n            id: \"tools\",\n            title: \"Security Tools\",\n            icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Scan_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            children: [\n                {\n                    id: \"osint\",\n                    title: \"OSINT Lookup\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Scan_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                    href: \"/osint\",\n                    badge: \"Popular\"\n                },\n                {\n                    id: \"scanner\",\n                    title: \"Vulnerability Scanner\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Scan_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                    href: \"/scanner\",\n                    premium: true\n                },\n                {\n                    id: \"file-analyzer\",\n                    title: \"File Analyzer\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Scan_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                    href: \"/file-analyzer\",\n                    premium: true\n                },\n                {\n                    id: \"cve\",\n                    title: \"CVE Database\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Scan_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                    href: \"/cve\"\n                },\n                {\n                    id: \"dorking\",\n                    title: \"Google Dorking\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Scan_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                    href: \"/dorking\"\n                }\n            ]\n        },\n        // Advanced Tools (Premium plans)\n        {\n            id: \"advanced\",\n            title: \"Advanced Tools\",\n            icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Scan_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            plans: [\n                \"Hobby\",\n                \"Bughunter\",\n                \"Cybersecurity\"\n            ],\n            children: [\n                {\n                    id: \"playground\",\n                    title: \"API Playground\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Scan_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                    href: \"/playground\"\n                },\n                {\n                    id: \"tools-advanced\",\n                    title: \"Advanced Tools\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Scan_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n                    href: \"/tools\"\n                }\n            ]\n        },\n        // Community & Learning\n        {\n            id: \"community\",\n            title: \"Community\",\n            icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Scan_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n            children: [\n                {\n                    id: \"leaderboard\",\n                    title: \"Leaderboard\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Scan_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n                    href: \"/leaderboard\"\n                },\n                {\n                    id: \"community-hub\",\n                    title: \"Community Hub\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Scan_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n                    href: \"/community\"\n                }\n            ]\n        },\n        // Admin Tools (Admin and Super Admin only)\n        ...(user === null || user === void 0 ? void 0 : user.role) === \"admin\" || (user === null || user === void 0 ? void 0 : user.role) === \"super_admin\" ? [\n            {\n                id: \"admin\",\n                title: \"Administration\",\n                icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Scan_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n                roles: [\n                    \"admin\",\n                    \"super_admin\"\n                ],\n                children: [\n                    {\n                        id: \"user-management\",\n                        title: \"User Management\",\n                        icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Scan_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n                        href: \"/dashboard?section=users\",\n                        roles: [\n                            \"admin\",\n                            \"super_admin\"\n                        ]\n                    },\n                    {\n                        id: \"analytics\",\n                        title: \"Analytics\",\n                        icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Scan_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n                        href: \"/dashboard?section=analytics\",\n                        roles: [\n                            \"admin\",\n                            \"super_admin\"\n                        ]\n                    },\n                    {\n                        id: \"monitoring\",\n                        title: \"System Monitor\",\n                        icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Scan_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n                        href: \"/dashboard?section=monitoring\",\n                        roles: [\n                            \"admin\",\n                            \"super_admin\"\n                        ]\n                    }\n                ]\n            }\n        ] : [],\n        // Super Admin Tools (Super Admin only)\n        ...(user === null || user === void 0 ? void 0 : user.role) === \"super_admin\" ? [\n            {\n                id: \"superadmin\",\n                title: \"Super Admin\",\n                icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Scan_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n                roles: [\n                    \"super_admin\"\n                ],\n                children: [\n                    {\n                        id: \"website-settings\",\n                        title: \"Website Settings\",\n                        icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Scan_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n                        href: \"/dashboard?section=website-settings\",\n                        roles: [\n                            \"super_admin\"\n                        ]\n                    },\n                    {\n                        id: \"payment-management\",\n                        title: \"Payment Management\",\n                        icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Scan_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n                        href: \"/dashboard?section=payments\",\n                        roles: [\n                            \"super_admin\"\n                        ]\n                    },\n                    {\n                        id: \"bot-management\",\n                        title: \"Bot Management\",\n                        icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Scan_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"],\n                        href: \"/dashboard?section=bots\",\n                        roles: [\n                            \"super_admin\"\n                        ]\n                    },\n                    {\n                        id: \"whatsapp-bot\",\n                        title: \"WhatsApp Bot\",\n                        icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Scan_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n                        href: \"/dashboard?section=whatsapp\",\n                        roles: [\n                            \"super_admin\"\n                        ]\n                    },\n                    {\n                        id: \"telegram-bot\",\n                        title: \"Telegram Bot\",\n                        icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Scan_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"],\n                        href: \"/dashboard?section=telegram\",\n                        roles: [\n                            \"super_admin\"\n                        ]\n                    },\n                    {\n                        id: \"system-config\",\n                        title: \"System Config\",\n                        icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Scan_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"],\n                        href: \"/dashboard?section=system-config\",\n                        roles: [\n                            \"super_admin\"\n                        ]\n                    },\n                    {\n                        id: \"security-center\",\n                        title: \"Security Center\",\n                        icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Scan_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"],\n                        href: \"/dashboard?section=security\",\n                        roles: [\n                            \"super_admin\"\n                        ]\n                    },\n                    {\n                        id: \"database-admin\",\n                        title: \"Database Admin\",\n                        icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Scan_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                        href: \"/dashboard?section=database\",\n                        roles: [\n                            \"super_admin\"\n                        ]\n                    },\n                    {\n                        id: \"server-management\",\n                        title: \"Server Management\",\n                        icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Scan_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"],\n                        href: \"/dashboard?section=server\",\n                        roles: [\n                            \"super_admin\"\n                        ]\n                    },\n                    {\n                        id: \"api-management\",\n                        title: \"API Management\",\n                        icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Scan_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"],\n                        href: \"/dashboard?section=api\",\n                        roles: [\n                            \"super_admin\"\n                        ]\n                    }\n                ]\n            }\n        ] : [],\n        // Personal Section\n        {\n            id: \"personal\",\n            title: \"Personal\",\n            icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Scan_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"],\n            children: [\n                {\n                    id: \"profile\",\n                    title: \"Profile\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Scan_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"],\n                    href: \"/profile\"\n                },\n                {\n                    id: \"plan\",\n                    title: \"Subscription\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Scan_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n                    href: \"/plan\"\n                },\n                {\n                    id: \"settings\",\n                    title: \"Settings\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_MessageSquare_Monitor_Scan_Search_Send_Server_Settings_Shield_ShieldCheck_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n                    href: \"/settings\"\n                }\n            ]\n        }\n    ];\n    // Filter items based on user role and plan\n    const filterItems = (items)=>{\n        return items.filter((item)=>{\n            // Check role permissions\n            if (item.roles && !item.roles.includes((user === null || user === void 0 ? void 0 : user.role) || \"user\")) {\n                return false;\n            }\n            // Check plan permissions\n            if (item.plans && !item.plans.includes((user === null || user === void 0 ? void 0 : user.plan) || \"Free\")) {\n                return false;\n            }\n            // Check premium access\n            if (item.premium && (user === null || user === void 0 ? void 0 : user.plan) === \"Free\") {\n                return false;\n            }\n            // Filter children recursively\n            if (item.children) {\n                item.children = filterItems(item.children);\n            }\n            return true;\n        });\n    };\n    const filteredItems = filterItems(sidebarItems);\n    const toggleExpanded = (itemId)=>{\n        setExpandedItems((prev)=>prev.includes(itemId) ? prev.filter((id)=>id !== itemId) : [\n                ...prev,\n                itemId\n            ]);\n    };\n    const isActive = (href)=>{\n        if (false) {}\n        if (href === \"/dashboard\") {\n            return pathname === \"/dashboard\" && !window.location.search;\n        }\n        // Check for section-based URLs\n        if (href.includes(\"?section=\")) {\n            const currentUrl = \"\".concat(pathname).concat(window.location.search);\n            return currentUrl === href;\n        }\n        return pathname === href || pathname.startsWith(href + \"/\");\n    };\n    const renderSidebarItem = function(item) {\n        let level = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;\n        var _item_children;\n        const Icon = item.icon;\n        const hasChildren = item.children && item.children.length > 0;\n        const isExpanded = expandedItems.includes(item.id);\n        const active = item.href ? isActive(item.href) : false;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"\".concat(level > 0 ? \"ml-4\" : \"\"),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    onClick: ()=>{\n                        if (hasChildren) {\n                            toggleExpanded(item.id);\n                        } else if (item.href) {\n                            router.push(item.href);\n                        }\n                    },\n                    className: \"\\n            flex items-center justify-between p-3 rounded-lg cursor-pointer transition-all duration-200\\n            \".concat(active ? \"bg-cyber-primary/20 text-cyber-primary border-l-4 border-cyber-primary\" : \"text-gray-300 hover:bg-gray-800 hover:text-white\", \"\\n            \").concat(item.comingSoon ? \"opacity-50 cursor-not-allowed\" : \"\", \"\\n          \"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                    className: \"h-5 w-5 \".concat(active ? \"text-cyber-primary\" : \"\")\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                                    lineNumber: 716,\n                                    columnNumber: 13\n                                }, _this),\n                                !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: item.title\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                                            lineNumber: 719,\n                                            columnNumber: 17\n                                        }, _this),\n                                        item.badge && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-2 py-1 text-xs bg-cyber-primary/20 text-cyber-primary rounded-full\",\n                                            children: item.badge\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                                            lineNumber: 721,\n                                            columnNumber: 19\n                                        }, _this),\n                                        item.premium && (user === null || user === void 0 ? void 0 : user.plan) === \"Free\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-2 py-1 text-xs bg-yellow-500/20 text-yellow-400 rounded-full\",\n                                            children: \"PRO\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                                            lineNumber: 726,\n                                            columnNumber: 19\n                                        }, _this),\n                                        item.comingSoon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-2 py-1 text-xs bg-gray-500/20 text-gray-400 rounded-full\",\n                                            children: \"Soon\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                                            lineNumber: 731,\n                                            columnNumber: 19\n                                        }, _this)\n                                    ]\n                                }, void 0, true)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                            lineNumber: 715,\n                            columnNumber: 11\n                        }, _this),\n                        !isCollapsed && hasChildren && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"transform transition-transform \".concat(isExpanded ? \"rotate-90\" : \"\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-4 h-4\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M9 5l7 7-7 7\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                                    lineNumber: 742,\n                                    columnNumber: 17\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                                lineNumber: 741,\n                                columnNumber: 15\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                            lineNumber: 740,\n                            columnNumber: 13\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                    lineNumber: 698,\n                    columnNumber: 9\n                }, _this),\n                !isCollapsed && hasChildren && isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-2 space-y-1\",\n                    children: (_item_children = item.children) === null || _item_children === void 0 ? void 0 : _item_children.map((child)=>renderSidebarItem(child, level + 1))\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                    lineNumber: 749,\n                    columnNumber: 11\n                }, _this)\n            ]\n        }, item.id, true, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n            lineNumber: 697,\n            columnNumber: 7\n        }, _this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\\n      bg-gray-900 border-r border-gray-800 transition-all duration-300 flex flex-col\\n      \".concat(isCollapsed ? \"w-16\" : \"w-64\", \"\\n    \"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-b border-gray-800\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-lg font-bold text-white\",\n                                    children: \"KodeXGuard\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                                    lineNumber: 767,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400\",\n                                    children: [\n                                        (user === null || user === void 0 ? void 0 : user.role) === \"super_admin\" ? \"\\uD83D\\uDC51 Super Admin\" : (user === null || user === void 0 ? void 0 : user.role) === \"admin\" ? \"\\uD83D\\uDEE1️ Admin\" : \"\\uD83D\\uDD12 User\",\n                                        \" • \",\n                                        user === null || user === void 0 ? void 0 : user.plan\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                                    lineNumber: 768,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                            lineNumber: 766,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onToggle,\n                            className: \"p-2 rounded-lg hover:bg-gray-800 text-gray-400 hover:text-white transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-5 h-5\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M4 6h16M4 12h16M4 18h16\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                                    lineNumber: 780,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                                lineNumber: 779,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                            lineNumber: 775,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                    lineNumber: 764,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                lineNumber: 763,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto p-4 space-y-2\",\n                children: filteredItems.map((item)=>renderSidebarItem(item))\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                lineNumber: 787,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-t border-gray-800\",\n                children: !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-xs text-gray-500 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"\\xa9 2024 KodeXGuard\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                            lineNumber: 795,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"Cybersecurity Platform\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                            lineNumber: 796,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                    lineNumber: 794,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                lineNumber: 792,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n        lineNumber: 758,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardSidebar, \"IawfwaZbxNshDbEnUCH2X8OEMKg=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname\n    ];\n});\n_c = DashboardSidebar;\nvar _c;\n$RefreshReg$(_c, \"DashboardSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/DashboardSidebar.tsx\n"));

/***/ })

});