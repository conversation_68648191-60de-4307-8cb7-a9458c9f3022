"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./app/dashboard/page.tsx":
/*!********************************!*\
  !*** ./app/dashboard/page.tsx ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DashboardPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_DashboardSidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/DashboardSidebar */ \"(app-pages-browser)/./components/DashboardSidebar.tsx\");\n/* harmony import */ var _components_dashboard_UserDashboard__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/dashboard/UserDashboard */ \"(app-pages-browser)/./components/dashboard/UserDashboard.tsx\");\n/* harmony import */ var _components_dashboard_AdminSections__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/dashboard/AdminSections */ \"(app-pages-browser)/./components/dashboard/AdminSections.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction DashboardPage() {\n    var _user_username;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const section = searchParams.get(\"section\");\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [sidebarCollapsed, setSidebarCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadUserData();\n        // Check if mobile\n        const checkMobile = ()=>{\n            setIsMobile(window.innerWidth < 768);\n            if (window.innerWidth < 768) {\n                setSidebarCollapsed(true);\n            }\n        };\n        checkMobile();\n        window.addEventListener(\"resize\", checkMobile);\n        return ()=>window.removeEventListener(\"resize\", checkMobile);\n    }, []);\n    const loadUserData = async ()=>{\n        try {\n            setLoading(true);\n            console.log(\"\\uD83D\\uDD04 Loading user data...\");\n            const token = localStorage.getItem(\"token\");\n            if (!token) {\n                router.push(\"/login\");\n                return;\n            }\n            const response = await fetch(\"/api/auth/me\", {\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(token)\n                }\n            });\n            if (response.ok) {\n                const userData = await response.json();\n                setUser(userData);\n                console.log(\"✅ User data loaded:\", userData);\n            } else {\n                console.error(\"❌ Failed to load user data\");\n                router.push(\"/login\");\n            }\n        } catch (error) {\n            console.error(\"❌ Error loading user data:\", error);\n            router.push(\"/login\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const toggleSidebar = ()=>{\n        setSidebarCollapsed(!sidebarCollapsed);\n    };\n    // Close sidebar when clicking outside on mobile\n    const handleOverlayClick = ()=>{\n        if (isMobile && !sidebarCollapsed) {\n            setSidebarCollapsed(true);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-cyber-dark flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-2 border-cyber-primary border-t-transparent mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-300\",\n                        children: \"Loading dashboard...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 94,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 93,\n            columnNumber: 7\n        }, this);\n    }\n    const renderContent = ()=>{\n        // If no section specified, show user dashboard\n        if (!section) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_UserDashboard__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                user: user\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 105,\n                columnNumber: 14\n            }, this);\n        }\n        // Check if user has permission for admin sections\n        const adminSections = [\n            \"users\",\n            \"analytics\",\n            \"monitoring\"\n        ];\n        const superAdminSections = [\n            \"website-settings\",\n            \"payments\",\n            \"bots\",\n            \"whatsapp\",\n            \"telegram\",\n            \"system-config\",\n            \"security\",\n            \"database\",\n            \"server\",\n            \"api\"\n        ];\n        if (adminSections.includes(section)) {\n            if ((user === null || user === void 0 ? void 0 : user.role) !== \"admin\" && (user === null || user === void 0 ? void 0 : user.role) !== \"super_admin\") {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-white mb-4\",\n                            children: \"Access Denied\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400 mb-6\",\n                            children: \"You don't have permission to access this section.\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>router.push(\"/dashboard\"),\n                            className: \"btn-cyber-primary\",\n                            children: \"Back to Dashboard\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 118,\n                    columnNumber: 11\n                }, this);\n            }\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_AdminSections__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                section: section,\n                user: user\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 130,\n                columnNumber: 14\n            }, this);\n        }\n        if (superAdminSections.includes(section)) {\n            if ((user === null || user === void 0 ? void 0 : user.role) !== \"super_admin\") {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-white mb-4\",\n                            children: \"Super Admin Access Required\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400 mb-6\",\n                            children: \"This section requires super admin privileges.\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>router.push(\"/dashboard\"),\n                            className: \"btn-cyber-primary\",\n                            children: \"Back to Dashboard\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 11\n                }, this);\n            }\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_AdminSections__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                section: section,\n                user: user\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 148,\n                columnNumber: 14\n            }, this);\n        }\n        // Unknown section\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-2xl font-bold text-white mb-4\",\n                    children: \"Section Not Found\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 154,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-400 mb-6\",\n                    children: [\n                        'The requested section \"',\n                        section,\n                        '\" is not available.'\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 155,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>router.push(\"/dashboard\"),\n                    className: \"btn-cyber-primary\",\n                    children: \"Back to Dashboard\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 156,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 153,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-cyber-dark flex relative\",\n        children: [\n            isMobile && !sidebarCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 z-40\",\n                onClick: handleOverlayClick\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 170,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardSidebar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                user: user,\n                isCollapsed: sidebarCollapsed,\n                onToggle: toggleSidebar,\n                isMobile: isMobile\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 177,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col min-w-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"bg-gray-900 border-b border-gray-800 px-4 md:px-6 py-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: toggleSidebar,\n                                            className: \"p-2 rounded-lg hover:bg-gray-800 text-gray-400 hover:text-white transition-colors md:hidden\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-6 h-6\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M4 6h16M4 12h16M4 18h16\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 197,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-lg md:text-xl font-bold text-white\",\n                                                    children: section ? section.split(\"-\").map((word)=>word.charAt(0).toUpperCase() + word.slice(1)).join(\" \") : \"Dashboard\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 203,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-400 text-xs md:text-sm\",\n                                                    children: (user === null || user === void 0 ? void 0 : user.role) === \"super_admin\" ? \"\\uD83D\\uDC51 Super Admin Panel\" : (user === null || user === void 0 ? void 0 : user.role) === \"admin\" ? \"\\uD83D\\uDEE1️ Admin Panel\" : \"\\uD83D\\uDD12 User Dashboard\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 210,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 md:space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right hidden sm:block\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-white font-medium text-sm md:text-base\",\n                                                    children: user === null || user === void 0 ? void 0 : user.username\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 220,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-400 text-xs md:text-sm\",\n                                                    children: [\n                                                        user === null || user === void 0 ? void 0 : user.plan,\n                                                        \" Plan\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 221,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 md:w-10 md:h-10 bg-cyber-primary/20 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-cyber-primary font-bold text-sm md:text-base\",\n                                                children: user === null || user === void 0 ? void 0 : (_user_username = user.username) === null || _user_username === void 0 ? void 0 : _user_username.charAt(0).toUpperCase()\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 224,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 188,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 p-4 md:p-6 overflow-y-auto\",\n                        children: renderContent()\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 233,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 185,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 167,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardPage, \"7BqhCUcdNPPJYIxa26BOOET1LW0=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c = DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/page.tsx\n"));

/***/ })

});