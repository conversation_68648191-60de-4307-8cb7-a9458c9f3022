'use client'

import { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import DashboardLayout from '@/components/DashboardLayout'
import UserDashboard from '@/components/dashboard/UserDashboard'
import AdminSections from '@/components/dashboard/AdminSections'
import { useAuth } from '@/hooks/auth'

interface User {
  id: string
  username: string
  email: string
  role: 'user' | 'admin' | 'super_admin' | 'moderator'
  plan: 'Free' | 'Student' | 'Hobby' | 'Bughunter' | 'Cybersecurity'
  level: number
  score: number
  full_name?: string
}

export default function DashboardPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const section = searchParams.get('section')
  const { user, isLoading, isAuthenticated } = useAuth()

  // Redirect if not authenticated
  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/login')
    }
  }, [isLoading, isAuthenticated, router])

  if (isLoading) {
    return (
      <div className="min-h-screen bg-cyber-dark flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-2 border-cyber-primary border-t-transparent mx-auto mb-4"></div>
          <p className="text-gray-300">Loading dashboard...</p>
        </div>
      </div>
    )
  }

  if (!isAuthenticated || !user) {
    return (
      <div className="min-h-screen bg-cyber-dark flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-300">Redirecting to login...</p>
        </div>
      </div>
    )
  }

  const renderContent = () => {
    // If no section specified, show user dashboard
    if (!section) {
      return <UserDashboard user={user} />
    }

    // Check if user has permission for admin sections
    const adminSections = ['users', 'analytics', 'monitoring']
    const superAdminSections = [
      'website-settings', 'payments', 'bots', 'whatsapp', 'telegram', 
      'system-config', 'security', 'database', 'server', 'api'
    ]

    if (adminSections.includes(section)) {
      if (user?.role !== 'admin' && user?.role !== 'super_admin') {
        return (
          <div className="text-center py-12">
            <h2 className="text-2xl font-bold text-white mb-4">Access Denied</h2>
            <p className="text-gray-400 mb-6">You don't have permission to access this section.</p>
            <button 
              onClick={() => router.push('/dashboard')}
              className="btn-cyber-primary"
            >
              Back to Dashboard
            </button>
          </div>
        )
      }
      return <AdminSections section={section} user={user} />
    }

    if (superAdminSections.includes(section)) {
      if (user?.role !== 'super_admin') {
        return (
          <div className="text-center py-12">
            <h2 className="text-2xl font-bold text-white mb-4">Super Admin Access Required</h2>
            <p className="text-gray-400 mb-6">This section requires super admin privileges.</p>
            <button 
              onClick={() => router.push('/dashboard')}
              className="btn-cyber-primary"
            >
              Back to Dashboard
            </button>
          </div>
        )
      }
      return <AdminSections section={section} user={user} />
    }

    // Unknown section
    return (
      <div className="text-center py-12">
        <h2 className="text-2xl font-bold text-white mb-4">Section Not Found</h2>
        <p className="text-gray-400 mb-6">The requested section "{section}" is not available.</p>
        <button 
          onClick={() => router.push('/dashboard')}
          className="btn-cyber-primary"
        >
          Back to Dashboard
        </button>
      </div>
    )
  }

  return (
    <DashboardLayout>
      {renderContent()}
    </DashboardLayout>
  )
}
