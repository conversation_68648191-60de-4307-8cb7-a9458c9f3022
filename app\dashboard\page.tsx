'use client'

import { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import DashboardSidebar from '@/components/DashboardSidebar'
import UserDashboard from '@/components/dashboard/UserDashboard'
import AdminSections from '@/components/dashboard/AdminSections'

interface User {
  id: string
  username: string
  email: string
  role: 'user' | 'admin' | 'super_admin' | 'moderator'
  plan: 'Free' | 'Student' | 'Hobby' | 'Bughunter' | 'Cybersecurity'
  level: number
  score: number
  full_name?: string
}

export default function DashboardPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const section = searchParams.get('section')
  
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const [isMobile, setIsMobile] = useState(false)

  useEffect(() => {
    loadUserData()

    // Check if mobile
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768)
      if (window.innerWidth < 768) {
        setSidebarCollapsed(true)
      }
    }

    checkMobile()
    window.addEventListener('resize', checkMobile)

    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  const loadUserData = async () => {
    try {
      setLoading(true)
      console.log('🔄 Loading user data...')
      
      const token = localStorage.getItem('token')
      if (!token) {
        router.push('/login')
        return
      }

      const response = await fetch('/api/auth/me', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (response.ok) {
        const userData = await response.json()
        setUser(userData)
        console.log('✅ User data loaded:', userData)
      } else {
        console.error('❌ Failed to load user data')
        router.push('/login')
      }
    } catch (error) {
      console.error('❌ Error loading user data:', error)
      router.push('/login')
    } finally {
      setLoading(false)
    }
  }

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed)
  }

  // Close sidebar when clicking outside on mobile
  const handleOverlayClick = () => {
    if (isMobile && !sidebarCollapsed) {
      setSidebarCollapsed(true)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-cyber-dark flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-2 border-cyber-primary border-t-transparent mx-auto mb-4"></div>
          <p className="text-gray-300">Loading dashboard...</p>
        </div>
      </div>
    )
  }

  const renderContent = () => {
    // If no section specified, show user dashboard
    if (!section) {
      return <UserDashboard user={user} />
    }

    // Check if user has permission for admin sections
    const adminSections = ['users', 'analytics', 'monitoring']
    const superAdminSections = [
      'website-settings', 'payments', 'bots', 'whatsapp', 'telegram', 
      'system-config', 'security', 'database', 'server', 'api'
    ]

    if (adminSections.includes(section)) {
      if (user?.role !== 'admin' && user?.role !== 'super_admin') {
        return (
          <div className="text-center py-12">
            <h2 className="text-2xl font-bold text-white mb-4">Access Denied</h2>
            <p className="text-gray-400 mb-6">You don't have permission to access this section.</p>
            <button 
              onClick={() => router.push('/dashboard')}
              className="btn-cyber-primary"
            >
              Back to Dashboard
            </button>
          </div>
        )
      }
      return <AdminSections section={section} user={user} />
    }

    if (superAdminSections.includes(section)) {
      if (user?.role !== 'super_admin') {
        return (
          <div className="text-center py-12">
            <h2 className="text-2xl font-bold text-white mb-4">Super Admin Access Required</h2>
            <p className="text-gray-400 mb-6">This section requires super admin privileges.</p>
            <button 
              onClick={() => router.push('/dashboard')}
              className="btn-cyber-primary"
            >
              Back to Dashboard
            </button>
          </div>
        )
      }
      return <AdminSections section={section} user={user} />
    }

    // Unknown section
    return (
      <div className="text-center py-12">
        <h2 className="text-2xl font-bold text-white mb-4">Section Not Found</h2>
        <p className="text-gray-400 mb-6">The requested section "{section}" is not available.</p>
        <button 
          onClick={() => router.push('/dashboard')}
          className="btn-cyber-primary"
        >
          Back to Dashboard
        </button>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-cyber-dark flex relative">
      {/* Mobile Overlay */}
      {isMobile && !sidebarCollapsed && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40"
          onClick={handleOverlayClick}
        />
      )}

      {/* Sidebar */}
      <DashboardSidebar
        user={user}
        isCollapsed={sidebarCollapsed}
        onToggle={toggleSidebar}
        isMobile={isMobile}
      />

      {/* Main Content */}
      <div className="flex-1 flex flex-col min-w-0">
        {/* Header */}
        <header className="bg-gray-900 border-b border-gray-800 px-4 md:px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              {/* Mobile Menu Button */}
              {isMobile && (
                <button
                  onClick={toggleSidebar}
                  className="p-2 rounded-lg hover:bg-gray-800 text-gray-400 hover:text-white transition-colors md:hidden"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                  </svg>
                </button>
              )}

              <div>
                <h1 className="text-lg md:text-xl font-bold text-white">
                  {section ? (
                    section.split('-').map(word =>
                      word.charAt(0).toUpperCase() + word.slice(1)
                    ).join(' ')
                  ) : 'Dashboard'}
                </h1>
                <p className="text-gray-400 text-xs md:text-sm">
                  {user?.role === 'super_admin' ? '👑 Super Admin Panel' :
                   user?.role === 'admin' ? '🛡️ Admin Panel' :
                   '🔒 User Dashboard'}
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-2 md:space-x-4">
              <div className="text-right hidden sm:block">
                <p className="text-white font-medium text-sm md:text-base">{user?.username}</p>
                <p className="text-gray-400 text-xs md:text-sm">{user?.plan} Plan</p>
              </div>
              <div className="w-8 h-8 md:w-10 md:h-10 bg-cyber-primary/20 rounded-full flex items-center justify-center">
                <span className="text-cyber-primary font-bold text-sm md:text-base">
                  {user?.username?.charAt(0).toUpperCase()}
                </span>
              </div>
            </div>
          </div>
        </header>

        {/* Content */}
        <main className="flex-1 p-4 md:p-6 overflow-y-auto">
          {renderContent()}
        </main>
      </div>
    </div>
  )
}
