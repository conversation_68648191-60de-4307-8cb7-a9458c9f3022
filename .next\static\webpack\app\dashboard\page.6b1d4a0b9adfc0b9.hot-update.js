"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./components/DashboardSidebar.tsx":
/*!*****************************************!*\
  !*** ./components/DashboardSidebar.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DashboardSidebar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/home.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/terminal.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-cog.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/monitor.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/cog.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield-check.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/webhook.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-check.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,ChevronRight,Code,Cog,CreditCard,Crown,Database,FileText,Globe,Home,Menu,MessageSquare,Monitor,Search,Send,Server,Settings,Shield,ShieldCheck,Target,Terminal,Trophy,UserCheck,UserCog,Users,Webhook,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction DashboardSidebar(param) {\n    let { user, isCollapsed, onToggle } = param;\n    var _this = this;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const [expandedItems, setExpandedItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Define sidebar items based on user role and plan\n    const sidebarItems = [\n        // Main Dashboard\n        {\n            id: \"dashboard\",\n            title: \"Dashboard\",\n            icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            href: \"/dashboard\"\n        },\n        // Core Tools (Available to all users)\n        {\n            id: \"tools\",\n            title: \"Security Tools\",\n            icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            children: [\n                {\n                    id: \"osint\",\n                    title: \"OSINT Lookup\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                    href: \"/osint\",\n                    badge: \"Popular\"\n                },\n                {\n                    id: \"scanner\",\n                    title: \"Vulnerability Scanner\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                    href: \"/scanner\",\n                    premium: true\n                },\n                {\n                    id: \"file-analyzer\",\n                    title: \"File Analyzer\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                    href: \"/file-analyzer\",\n                    premium: true\n                },\n                {\n                    id: \"cve\",\n                    title: \"CVE Database\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                    href: \"/cve\"\n                },\n                {\n                    id: \"dorking\",\n                    title: \"Google Dorking\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                    href: \"/dorking\"\n                }\n            ]\n        },\n        // Advanced Tools (Premium plans)\n        {\n            id: \"advanced\",\n            title: \"Advanced Tools\",\n            icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            plans: [\n                \"Hobby\",\n                \"Bughunter\",\n                \"Cybersecurity\"\n            ],\n            children: [\n                {\n                    id: \"playground\",\n                    title: \"API Playground\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                    href: \"/playground\"\n                },\n                {\n                    id: \"tools-advanced\",\n                    title: \"Advanced Tools\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n                    href: \"/tools\"\n                }\n            ]\n        },\n        // Community & Learning\n        {\n            id: \"community\",\n            title: \"Community\",\n            icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n            children: [\n                {\n                    id: \"leaderboard\",\n                    title: \"Leaderboard\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n                    href: \"/leaderboard\"\n                },\n                {\n                    id: \"community-hub\",\n                    title: \"Community Hub\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n                    href: \"/community\"\n                }\n            ]\n        },\n        // Admin Tools (Admin and Super Admin only)\n        ...(user === null || user === void 0 ? void 0 : user.role) === \"admin\" || (user === null || user === void 0 ? void 0 : user.role) === \"super_admin\" ? [\n            {\n                id: \"admin\",\n                title: \"Administration\",\n                icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n                roles: [\n                    \"admin\",\n                    \"super_admin\"\n                ],\n                children: [\n                    {\n                        id: \"user-management\",\n                        title: \"User Management\",\n                        icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n                        href: \"/dashboard?section=users\",\n                        roles: [\n                            \"admin\",\n                            \"super_admin\"\n                        ]\n                    },\n                    {\n                        id: \"analytics\",\n                        title: \"Analytics\",\n                        icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n                        href: \"/dashboard?section=analytics\",\n                        roles: [\n                            \"admin\",\n                            \"super_admin\"\n                        ]\n                    },\n                    {\n                        id: \"monitoring\",\n                        title: \"System Monitor\",\n                        icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n                        href: \"/dashboard?section=monitoring\",\n                        roles: [\n                            \"admin\",\n                            \"super_admin\"\n                        ]\n                    }\n                ]\n            }\n        ] : [],\n        // Super Admin Tools (Super Admin only)\n        ...(user === null || user === void 0 ? void 0 : user.role) === \"super_admin\" ? [\n            {\n                id: \"superadmin\",\n                title: \"Super Admin\",\n                icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n                roles: [\n                    \"super_admin\"\n                ],\n                children: [\n                    {\n                        id: \"website-settings\",\n                        title: \"Website Settings\",\n                        icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n                        href: \"/dashboard?section=website-settings\",\n                        roles: [\n                            \"super_admin\"\n                        ]\n                    },\n                    {\n                        id: \"payment-management\",\n                        title: \"Payment Management\",\n                        icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n                        href: \"/dashboard?section=payments\",\n                        roles: [\n                            \"super_admin\"\n                        ]\n                    },\n                    {\n                        id: \"bot-management\",\n                        title: \"Bot Management\",\n                        icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"],\n                        href: \"/dashboard?section=bots\",\n                        roles: [\n                            \"super_admin\"\n                        ]\n                    },\n                    {\n                        id: \"whatsapp-bot\",\n                        title: \"WhatsApp Bot\",\n                        icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n                        href: \"/dashboard?section=whatsapp\",\n                        roles: [\n                            \"super_admin\"\n                        ]\n                    },\n                    {\n                        id: \"telegram-bot\",\n                        title: \"Telegram Bot\",\n                        icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"],\n                        href: \"/dashboard?section=telegram\",\n                        roles: [\n                            \"super_admin\"\n                        ]\n                    },\n                    {\n                        id: \"system-config\",\n                        title: \"System Config\",\n                        icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"],\n                        href: \"/dashboard?section=system-config\",\n                        roles: [\n                            \"super_admin\"\n                        ]\n                    },\n                    {\n                        id: \"security-center\",\n                        title: \"Security Center\",\n                        icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"],\n                        href: \"/dashboard?section=security\",\n                        roles: [\n                            \"super_admin\"\n                        ]\n                    },\n                    {\n                        id: \"database-admin\",\n                        title: \"Database Admin\",\n                        icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                        href: \"/dashboard?section=database\",\n                        roles: [\n                            \"super_admin\"\n                        ]\n                    },\n                    {\n                        id: \"server-management\",\n                        title: \"Server Management\",\n                        icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"],\n                        href: \"/dashboard?section=server\",\n                        roles: [\n                            \"super_admin\"\n                        ]\n                    },\n                    {\n                        id: \"api-management\",\n                        title: \"API Management\",\n                        icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"],\n                        href: \"/dashboard?section=api\",\n                        roles: [\n                            \"super_admin\"\n                        ]\n                    }\n                ]\n            }\n        ] : [],\n        // Personal Section\n        {\n            id: \"personal\",\n            title: \"Personal\",\n            icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"],\n            children: [\n                {\n                    id: \"profile\",\n                    title: \"Profile\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"],\n                    href: \"/profile\"\n                },\n                {\n                    id: \"plan\",\n                    title: \"Subscription\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n                    href: \"/plan\"\n                },\n                {\n                    id: \"settings\",\n                    title: \"Settings\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n                    href: \"/settings\"\n                }\n            ]\n        }\n    ];\n    // Filter items based on user role and plan\n    const filterItems = (items)=>{\n        return items.filter((item)=>{\n            // Check role permissions\n            if (item.roles && !item.roles.includes((user === null || user === void 0 ? void 0 : user.role) || \"user\")) {\n                return false;\n            }\n            // Check plan permissions\n            if (item.plans && !item.plans.includes((user === null || user === void 0 ? void 0 : user.plan) || \"Free\")) {\n                return false;\n            }\n            // Check premium access\n            if (item.premium && (user === null || user === void 0 ? void 0 : user.plan) === \"Free\") {\n                return false;\n            }\n            // Filter children recursively\n            if (item.children) {\n                item.children = filterItems(item.children);\n            }\n            return true;\n        });\n    };\n    const filteredItems = filterItems(sidebarItems);\n    const toggleExpanded = (itemId)=>{\n        setExpandedItems((prev)=>prev.includes(itemId) ? prev.filter((id)=>id !== itemId) : [\n                ...prev,\n                itemId\n            ]);\n    };\n    const isActive = (href)=>{\n        if (false) {}\n        if (href === \"/dashboard\") {\n            return pathname === \"/dashboard\" && !window.location.search;\n        }\n        // Check for section-based URLs\n        if (href.includes(\"?section=\")) {\n            const currentUrl = \"\".concat(pathname).concat(window.location.search);\n            return currentUrl === href;\n        }\n        return pathname === href || pathname.startsWith(href + \"/\");\n    };\n    const renderSidebarItem = function(item) {\n        let level = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;\n        var _item_children;\n        const Icon = item.icon;\n        const hasChildren = item.children && item.children.length > 0;\n        const isExpanded = expandedItems.includes(item.id);\n        const active = item.href ? isActive(item.href) : false;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"\".concat(level > 0 ? \"ml-4\" : \"\"),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    onClick: ()=>{\n                        if (hasChildren) {\n                            toggleExpanded(item.id);\n                        } else if (item.href) {\n                            router.push(item.href);\n                        }\n                    },\n                    className: \"\\n            flex items-center justify-between p-3 rounded-lg cursor-pointer transition-all duration-200\\n            \".concat(active ? \"bg-cyber-primary/20 text-cyber-primary border-l-4 border-cyber-primary\" : \"text-gray-300 hover:bg-gray-800 hover:text-white\", \"\\n            \").concat(item.comingSoon ? \"opacity-50 cursor-not-allowed\" : \"\", \"\\n          \"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                    className: \"h-5 w-5 \".concat(active ? \"text-cyber-primary\" : \"\")\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                                    lineNumber: 387,\n                                    columnNumber: 13\n                                }, _this),\n                                !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: item.title\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                                            lineNumber: 390,\n                                            columnNumber: 17\n                                        }, _this),\n                                        item.badge && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-2 py-1 text-xs bg-cyber-primary/20 text-cyber-primary rounded-full\",\n                                            children: item.badge\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                                            lineNumber: 392,\n                                            columnNumber: 19\n                                        }, _this),\n                                        item.premium && (user === null || user === void 0 ? void 0 : user.plan) === \"Free\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-2 py-1 text-xs bg-yellow-500/20 text-yellow-400 rounded-full\",\n                                            children: \"PRO\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                                            lineNumber: 397,\n                                            columnNumber: 19\n                                        }, _this),\n                                        item.comingSoon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-2 py-1 text-xs bg-gray-500/20 text-gray-400 rounded-full\",\n                                            children: \"Soon\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                                            lineNumber: 402,\n                                            columnNumber: 19\n                                        }, _this)\n                                    ]\n                                }, void 0, true)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                            lineNumber: 386,\n                            columnNumber: 11\n                        }, _this),\n                        !isCollapsed && hasChildren && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                            className: \"w-4 h-4 transform transition-transform \".concat(isExpanded ? \"rotate-90\" : \"\")\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                            lineNumber: 411,\n                            columnNumber: 13\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                    lineNumber: 369,\n                    columnNumber: 9\n                }, _this),\n                !isCollapsed && hasChildren && isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-2 space-y-1\",\n                    children: (_item_children = item.children) === null || _item_children === void 0 ? void 0 : _item_children.map((child)=>renderSidebarItem(child, level + 1))\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                    lineNumber: 416,\n                    columnNumber: 11\n                }, _this)\n            ]\n        }, item.id, true, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n            lineNumber: 368,\n            columnNumber: 7\n        }, _this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\\n      bg-gray-900 border-r border-gray-800 transition-all duration-300 flex flex-col\\n      \".concat(isCollapsed ? \"w-16\" : \"w-64\", \"\\n    \"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-b border-gray-800\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-lg font-bold text-white\",\n                                    children: \"KodeXGuard\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                                    lineNumber: 434,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400\",\n                                    children: [\n                                        (user === null || user === void 0 ? void 0 : user.role) === \"super_admin\" ? \"\\uD83D\\uDC51 Super Admin\" : (user === null || user === void 0 ? void 0 : user.role) === \"admin\" ? \"\\uD83D\\uDEE1️ Admin\" : \"\\uD83D\\uDD12 User\",\n                                        \" • \",\n                                        user === null || user === void 0 ? void 0 : user.plan\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                                    lineNumber: 435,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                            lineNumber: 433,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onToggle,\n                            className: \"p-2 rounded-lg hover:bg-gray-800 text-gray-400 hover:text-white transition-colors\",\n                            children: isCollapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                className: \"w-5 h-5\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                                lineNumber: 446,\n                                columnNumber: 28\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bot_ChevronRight_Code_Cog_CreditCard_Crown_Database_FileText_Globe_Home_Menu_MessageSquare_Monitor_Search_Send_Server_Settings_Shield_ShieldCheck_Target_Terminal_Trophy_UserCheck_UserCog_Users_Webhook_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                className: \"w-5 h-5\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                                lineNumber: 446,\n                                columnNumber: 59\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                            lineNumber: 442,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                    lineNumber: 431,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                lineNumber: 430,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto p-4 space-y-2\",\n                children: filteredItems.map((item)=>renderSidebarItem(item))\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                lineNumber: 452,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-t border-gray-800\",\n                children: !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-xs text-gray-500 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"\\xa9 2024 KodeXGuard\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                            lineNumber: 460,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"Cybersecurity Platform\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                            lineNumber: 461,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                    lineNumber: 459,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n                lineNumber: 457,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardSidebar.tsx\",\n        lineNumber: 425,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardSidebar, \"IawfwaZbxNshDbEnUCH2X8OEMKg=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname\n    ];\n});\n_c = DashboardSidebar;\nvar _c;\n$RefreshReg$(_c, \"DashboardSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/DashboardSidebar.tsx\n"));

/***/ })

});