import { NextRequest, NextResponse } from 'next/server'
import { WhatsAppBotService } from '@/lib/services/whatsapp-bot'
import { verifyToken } from '@/lib/auth'

// Global WhatsApp bot service instance
const whatsappBot = new WhatsAppBotService()

export async function GET(request: NextRequest) {
  try {
    const token = request.headers.get('authorization')?.replace('Bearer ', '')
    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const user = await verifyToken(token)
    if (!user || user.role !== 'super_admin') {
      return NextResponse.json({ error: 'Forbidden - Super Admin only' }, { status: 403 })
    }

    const { searchParams } = new URL(request.url)
    const sessionId = searchParams.get('sessionId')

    if (!sessionId) {
      return NextResponse.json({ error: 'Missing sessionId' }, { status: 400 })
    }

    // Get session and QR code
    const session = await whatsappBot.getSession(sessionId)
    
    if (!session) {
      return NextResponse.json({ error: 'Session not found' }, { status: 404 })
    }

    if (session.status === 'connected') {
      return NextResponse.json({
        success: true,
        data: {
          status: 'connected',
          message: 'WhatsApp bot is already connected',
          connectedAt: session.connectedAt
        }
      })
    }

    if (session.status === 'error') {
      return NextResponse.json({
        success: false,
        data: {
          status: 'error',
          message: session.error || 'Unknown error occurred',
          error: session.error
        }
      })
    }

    if (!session.qrCode) {
      return NextResponse.json({
        success: true,
        data: {
          status: 'connecting',
          message: 'QR code is being generated. Please wait...',
          qrCode: null
        }
      })
    }

    return NextResponse.json({
      success: true,
      data: {
        status: session.status,
        qrCode: session.qrCode,
        message: 'Scan this QR code with WhatsApp to connect the bot',
        lastActivity: session.lastActivity
      }
    })

  } catch (error) {
    console.error('Error getting WhatsApp QR code:', error)
    return NextResponse.json(
      { error: 'Failed to get QR code' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const token = request.headers.get('authorization')?.replace('Bearer ', '')
    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const user = await verifyToken(token)
    if (!user || user.role !== 'super_admin') {
      return NextResponse.json({ error: 'Forbidden - Super Admin only' }, { status: 403 })
    }

    const body = await request.json()
    const { sessionName } = body

    // Create new WhatsApp session
    const sessionId = await whatsappBot.createSession(sessionName || 'kodexguard-wa')

    return NextResponse.json({
      success: true,
      data: {
        sessionId,
        message: 'WhatsApp bot session created. QR code will be available shortly.',
        status: 'connecting'
      }
    })

  } catch (error) {
    console.error('Error creating WhatsApp session:', error)
    return NextResponse.json(
      { error: 'Failed to create WhatsApp session' },
      { status: 500 }
    )
  }
}
