import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'
import { NextRequest } from 'next/server'

export interface User {
  id: number
  username: string
  email: string
  full_name?: string
  role: 'user' | 'admin' | 'super_admin' | 'moderator'
  plan: 'Free' | 'Student' | 'Hobby' | 'Bughunter' | 'Cybersecurity' | 'Pro' | 'Expert' | 'Elite'
  level: number
  score: number
  streak_days: number
  email_verified: boolean
  status?: 'active' | 'inactive' | 'suspended' | 'banned'
  phone?: string
  telegram_id?: number
  whatsapp_number?: string
  avatar_url?: string
  timezone?: string
  language?: string
  last_active: Date
  created_at: Date
}

export interface AuthTokens {
  accessToken: string
  refreshToken: string
  expiresIn: number
}

export interface LoginCredentials {
  email: string
  password: string
}

export interface RegisterData {
  username: string
  email: string
  password: string
  fullName?: string
}

interface AuthResult {
  success: boolean
  user?: User
  tokens?: AuthTokens
  message?: string
}

const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key'
const JWT_REFRESH_SECRET = process.env.JWT_REFRESH_SECRET || 'your-super-secret-refresh-key'
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '1h'
const JWT_REFRESH_EXPIRES_IN = process.env.JWT_REFRESH_EXPIRES_IN || '7d'
const BCRYPT_ROUNDS = 12

// Mock users for testing
const mockUsers: (User & { password: string })[] = [
  {
    id: 1,
    username: 'admin',
    email: '<EMAIL>',
    full_name: 'Administrator',
    role: 'admin',
    plan: 'Elite',
    level: 100,
    score: 10000,
    streak_days: 365,
    email_verified: true,
    status: 'active',
    last_active: new Date(),
    created_at: new Date(),
    password: 'admin123'
  },
  {
    id: 2,
    username: 'testuser',
    email: '<EMAIL>',
    full_name: 'Test User',
    role: 'user',
    plan: 'Free',
    level: 1,
    score: 0,
    streak_days: 0,
    email_verified: true,
    status: 'active',
    last_active: new Date(),
    created_at: new Date(),
    password: 'test123'
  }
]

export class SimpleAuthService {
  // Hash password
  static async hashPassword(password: string): Promise<string> {
    return await bcrypt.hash(password, BCRYPT_ROUNDS)
  }

  // Verify password
  static async verifyPassword(password: string, hash: string): Promise<boolean> {
    return await bcrypt.compare(password, hash)
  }

  // Generate JWT tokens
  static generateTokens(user: User): AuthTokens {
    const payload = {
      id: user.id,
      username: user.username,
      email: user.email,
      role: user.role,
      plan: user.plan
    }

    const accessToken = jwt.sign(payload, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN })
    const refreshToken = jwt.sign(payload, JWT_REFRESH_SECRET, { expiresIn: JWT_REFRESH_EXPIRES_IN })

    return {
      accessToken,
      refreshToken,
      expiresIn: 7 * 24 * 60 * 60 // 7 days in seconds
    }
  }

  // Verify JWT token
  static verifyToken(token: string, isRefreshToken = false): any {
    try {
      const secret = isRefreshToken ? JWT_REFRESH_SECRET : JWT_SECRET
      console.log('🔍 Verifying token with secret length:', secret.length)
      console.log('🔍 Token length:', token.length)
      const result = jwt.verify(token, secret)
      console.log('✅ Token verification successful:', result)
      return result
    } catch (error) {
      console.log('❌ Token verification error:', error instanceof Error ? error.message : String(error))
      return null
    }
  }

  // Register new user
  static async register(data: RegisterData): Promise<AuthResult> {
    try {
      // Check if user already exists
      const existingUser = mockUsers.find(u => u.email === data.email || u.username === data.username)
      if (existingUser) {
        return { success: false, message: 'User already exists with this email or username' }
      }

      // Create new user
      const newUser: User & { password: string } = {
        id: mockUsers.length + 1,
        username: data.username,
        email: data.email,
        full_name: data.fullName || '',
        role: 'user',
        plan: 'Free',
        level: 1,
        score: 0,
        streak_days: 0,
        email_verified: false,
        status: 'active',
        last_active: new Date(),
        created_at: new Date(),
        password: data.password
      }

      // Add to mock users
      mockUsers.push(newUser)

      // Remove password from user object
      const { password, ...userWithoutPassword } = newUser

      // Generate tokens
      const tokens = this.generateTokens(userWithoutPassword)

      return { success: true, user: userWithoutPassword, tokens }
    } catch (error) {
      console.error('Registration error:', error)
      return { success: false, message: 'Registration failed' }
    }
  }

  // Login user
  static async login(email: string, password: string): Promise<AuthResult> {
    try {
      // Find user by email
      const user = mockUsers.find(u => u.email === email && (u.status === 'active' || !u.status))

      if (!user) {
        return { success: false, message: 'Invalid email or password' }
      }

      // Verify password (simple comparison for testing)
      if (password !== user.password) {
        return { success: false, message: 'Invalid email or password' }
      }

      // Remove password from user object
      const { password: userPassword, ...userWithoutPassword } = user

      // Generate tokens
      const tokens = this.generateTokens(userWithoutPassword)

      // Update last active
      user.last_active = new Date()

      return { success: true, user: userWithoutPassword, tokens }
    } catch (error) {
      console.error('Login error:', error)
      return { success: false, message: 'Login failed' }
    }
  }

  // Authenticate request
  static async authenticateRequest(request: NextRequest): Promise<{ success: boolean; user?: User; message?: string }> {
    try {
      // Get token from Authorization header or cookies
      let token = request.headers.get('authorization')?.replace('Bearer ', '')

      if (!token) {
        // Try to get from cookies
        const cookies = request.headers.get('cookie')
        console.log('🍪 Cookies in middleware:', cookies)
        if (cookies) {
          const tokenMatch = cookies.match(/accessToken=([^;]+)/)
          token = tokenMatch ? tokenMatch[1] : undefined
          console.log('🔑 Token from cookies:', token ? 'Found' : 'Not found')
        }
      }

      if (!token) {
        console.log('❌ No token found in headers or cookies')
        return { success: false, message: 'No token provided' }
      }

      // Verify token
      console.log('🔍 Verifying token in middleware...')
      const decoded = this.verifyToken(token)
      if (!decoded) {
        console.log('❌ Token verification failed in middleware')
        return { success: false, message: 'Invalid token' }
      }

      console.log('✅ Token decoded successfully:', decoded.id, decoded.username)

      // Get user from mock data
      const user = mockUsers.find(u => u.id === decoded.id)
      if (!user) {
        console.log('❌ User not found in mock data:', decoded.id)
        return { success: false, message: 'User not found' }
      }

      console.log('✅ User found in middleware:', user.username)
      const { password, ...userWithoutPassword } = user

      return { success: true, user: userWithoutPassword }
    } catch (error) {
      console.error('Authentication error:', error)
      return { success: false, message: 'Authentication failed' }
    }
  }

  // Logout user (for mock, just return success)
  static async logout(userId: number, _sessionToken: string): Promise<void> {
    // In a real implementation, this would remove the session from database
    console.log(`User ${userId} logged out`)
  }

  // Refresh token
  static async refreshToken(refreshToken: string): Promise<AuthResult> {
    try {
      // Verify refresh token
      const decoded = this.verifyToken(refreshToken, true)
      if (!decoded) {
        return { success: false, message: 'Invalid refresh token' }
      }

      // Get user data
      const user = mockUsers.find(u => u.id === decoded.id && (u.status === 'active' || !u.status))
      if (!user) {
        return { success: false, message: 'User not found or inactive' }
      }

      const { password, ...userWithoutPassword } = user

      // Generate new tokens
      const tokens = this.generateTokens(userWithoutPassword)

      // Update user last active
      user.last_active = new Date()

      return { success: true, user: userWithoutPassword, tokens }
    } catch (error) {
      console.error('Token refresh error:', error)
      return { success: false, message: 'Token refresh failed' }
    }
  }

  // Get user by ID
  static async getUserById(userId: number): Promise<User | null> {
    try {
      const user = mockUsers.find(u => u.id === userId)
      if (!user) return null

      const { password, ...userWithoutPassword } = user
      return userWithoutPassword
    } catch (error) {
      console.error('Get user error:', error)
      return null
    }
  }

  // Get all users (admin only)
  static getAllUsers(): User[] {
    return mockUsers.map(user => {
      const { password, ...userWithoutPassword } = user
      return userWithoutPassword
    })
  }
}
