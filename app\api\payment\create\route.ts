import { NextRequest, NextResponse } from 'next/server'
import { PaymentService, PaymentRequest } from '@/lib/services/payment'
import { verifyToken } from '@/lib/auth'
import { db } from '@/lib/database'

const paymentService = new PaymentService()

export async function POST(request: NextRequest) {
  try {
    const token = request.headers.get('authorization')?.replace('Bearer ', '')
    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const user = await verifyToken(token)
    if (!user) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 })
    }

    const body = await request.json()
    const { planId, gateway, duration, customerInfo } = body

    // Validate required fields
    if (!planId || !gateway || !duration) {
      return NextResponse.json({ 
        error: 'Missing required fields: planId, gateway, duration' 
      }, { status: 400 })
    }

    // Validate gateway
    const validGateways = ['tripay', 'midtrans', 'xendit', 'manual']
    if (!validGateways.includes(gateway)) {
      return NextResponse.json({ error: 'Invalid payment gateway' }, { status: 400 })
    }

    // Get plan details
    const plan = await paymentService.getPlan(planId)
    if (!plan) {
      return NextResponse.json({ error: 'Plan not found' }, { status: 404 })
    }

    // Check if user already has active subscription
    const [existingSubscription] = await db.query(`
      SELECT id, expires_at FROM subscriptions 
      WHERE user_id = ? AND status = 'active' AND expires_at > NOW()
    `, [user.id])

    if (existingSubscription && (existingSubscription as any[]).length > 0) {
      return NextResponse.json({ 
        error: 'You already have an active subscription',
        data: {
          currentSubscription: (existingSubscription as any[])[0],
          upgradeAvailable: true
        }
      }, { status: 400 })
    }

    // Calculate price based on duration
    let finalPrice = plan.price
    let durationMultiplier = 1

    switch (duration) {
      case 'daily':
        durationMultiplier = 1
        finalPrice = Math.round(plan.price / 30) // Daily price
        break
      case 'weekly':
        durationMultiplier = 7
        finalPrice = Math.round(plan.price / 4) // Weekly price
        break
      case 'monthly':
        durationMultiplier = 30
        finalPrice = plan.price // Monthly price (base)
        break
      case 'yearly':
        durationMultiplier = 365
        finalPrice = Math.round(plan.price * 10) // Yearly price (10 months)
        break
      default:
        return NextResponse.json({ error: 'Invalid duration' }, { status: 400 })
    }

    // Apply discounts for students
    if (plan.type === 'student' && user.role === 'user') {
      finalPrice = Math.round(finalPrice * 0.5) // 50% discount for students
    }

    // Prepare payment request
    const paymentRequest: PaymentRequest = {
      userId: user.id,
      planId: plan.id,
      amount: finalPrice,
      currency: plan.currency,
      duration: duration as any,
      gateway: gateway as any,
      customerInfo: {
        name: customerInfo?.name || user.username,
        email: customerInfo?.email || user.email,
        phone: customerInfo?.phone || user.phone || ''
      }
    }

    // Create payment
    const paymentResponse = await paymentService.createPayment(paymentRequest)

    // Log payment creation
    await db.query(`
      INSERT INTO payment_logs (payment_id, user_id, action, details, created_at)
      VALUES (?, ?, 'created', ?, NOW())
    `, [
      paymentResponse.paymentId,
      user.id,
      JSON.stringify({
        planId,
        amount: finalPrice,
        gateway,
        duration
      })
    ])

    return NextResponse.json({
      success: true,
      data: {
        payment: paymentResponse,
        plan: {
          id: plan.id,
          name: plan.name,
          type: plan.type,
          duration,
          originalPrice: plan.price,
          finalPrice,
          discount: plan.price - finalPrice,
          features: plan.features
        },
        instructions: {
          gateway,
          nextSteps: gateway === 'manual' 
            ? 'Please follow the manual payment instructions and send proof of payment'
            : 'Complete payment using the provided payment URL',
          expiresAt: paymentResponse.expiresAt
        }
      }
    })

  } catch (error) {
    console.error('Payment creation error:', error)
    return NextResponse.json(
      { 
        error: 'Failed to create payment',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const token = request.headers.get('authorization')?.replace('Bearer ', '')
    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const user = await verifyToken(token)
    if (!user) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 })
    }

    // Get user's payment history
    const [payments] = await db.query(`
      SELECT 
        p.id,
        p.plan_id,
        p.amount,
        p.currency,
        p.gateway,
        p.status,
        p.created_at,
        p.expires_at,
        pl.name as plan_name,
        pl.type as plan_type
      FROM payments p
      LEFT JOIN plans pl ON p.plan_id = pl.id
      WHERE p.user_id = ?
      ORDER BY p.created_at DESC
      LIMIT 20
    `, [user.id])

    // Get current subscription
    const [subscription] = await db.query(`
      SELECT 
        s.*,
        pl.name as plan_name,
        pl.type as plan_type,
        pl.features
      FROM subscriptions s
      LEFT JOIN plans pl ON s.plan_id = pl.id
      WHERE s.user_id = ? AND s.status = 'active'
      ORDER BY s.expires_at DESC
      LIMIT 1
    `, [user.id])

    return NextResponse.json({
      success: true,
      data: {
        payments: payments,
        currentSubscription: (subscription as any[]).length > 0 ? (subscription as any[])[0] : null,
        paymentMethods: [
          {
            gateway: 'tripay',
            name: 'Tripay',
            description: 'Bank Transfer, E-Wallet, Virtual Account',
            fees: 'Rp 2,500 + 0.7%',
            processingTime: 'Instant'
          },
          {
            gateway: 'midtrans',
            name: 'Midtrans',
            description: 'Credit Card, Bank Transfer, E-Wallet',
            fees: '2.9% + Rp 2,000',
            processingTime: 'Instant'
          },
          {
            gateway: 'xendit',
            name: 'Xendit',
            description: 'Bank Transfer, E-Wallet, Credit Card',
            fees: '2.9% + Rp 2,000',
            processingTime: 'Instant'
          },
          {
            gateway: 'manual',
            name: 'Manual Transfer',
            description: 'Direct bank transfer with manual verification',
            fees: 'Free',
            processingTime: '1-24 hours'
          }
        ]
      }
    })

  } catch (error) {
    console.error('Error fetching payment data:', error)
    return NextResponse.json(
      { error: 'Failed to fetch payment data' },
      { status: 500 }
    )
  }
}
