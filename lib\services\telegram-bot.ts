import { Telegraf, Context } from 'telegraf'
import { db } from '../database'
import { OSINTService } from './osint'
import { VulnerabilityScanner } from './scanner'

export interface TelegramBotConfig {
  token: string
  webhookUrl?: string
  webhookSecret?: string
}

export interface TelegramBotSession {
  id: string
  botId: string
  botUsername: string
  status: 'connected' | 'disconnected' | 'error'
  lastActivity: Date
  connectedAt?: Date
  error?: string
}

export class TelegramBotService {
  private bot: Telegraf | null = null
  private osintService: OSINTService
  private scannerService: VulnerabilityScanner
  private session: TelegramBotSession | null = null

  constructor() {
    this.osintService = new OSINTService()
    this.scannerService = new VulnerabilityScanner()
  }

  async connect(config: TelegramBotConfig): Promise<TelegramBotSession> {
    try {
      // Create Telegram bot
      this.bot = new Telegraf(config.token)

      // Get bot info
      const botInfo = await this.bot.telegram.getMe()

      // Create session
      const sessionId = `tg_${botInfo.id}_${Date.now()}`
      this.session = {
        id: sessionId,
        botId: botInfo.id.toString(),
        botUsername: botInfo.username,
        status: 'connected',
        lastActivity: new Date(),
        connectedAt: new Date()
      }

      // Set up webhook if provided
      if (config.webhookUrl) {
        await this.bot.telegram.setWebhook(config.webhookUrl, {
          secret_token: config.webhookSecret
        })
      }

      // Set up command handlers
      this.setupCommandHandlers()

      // Start bot
      await this.bot.launch()

      // Save session to database
      await this.saveSessionToDatabase()

      return this.session

    } catch (error) {
      console.error('Failed to connect Telegram bot:', error)
      
      if (this.session) {
        this.session.status = 'error'
        this.session.error = error instanceof Error ? error.message : 'Unknown error'
      } else {
        this.session = {
          id: `tg_error_${Date.now()}`,
          botId: 'unknown',
          botUsername: 'unknown',
          status: 'error',
          lastActivity: new Date(),
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      }

      throw error
    }
  }

  async disconnect(): Promise<boolean> {
    try {
      if (this.bot) {
        await this.bot.stop()
        this.bot = null
      }

      if (this.session) {
        this.session.status = 'disconnected'
        await this.updateSessionStatus('disconnected')
      }

      return true
    } catch (error) {
      console.error('Failed to disconnect Telegram bot:', error)
      return false
    }
  }

  getSession(): TelegramBotSession | null {
    return this.session
  }

  private setupCommandHandlers() {
    if (!this.bot) return

    // Help command
    this.bot.command('help', async (ctx) => {
      await this.handleHelpCommand(ctx)
    })

    // Start command
    this.bot.command('start', async (ctx) => {
      await this.handleStartCommand(ctx)
    })

    // Scan command
    this.bot.command('scan', async (ctx) => {
      await this.handleScanCommand(ctx)
    })

    // OSINT commands
    this.bot.command('osint', async (ctx) => {
      await this.handleOsintCommand(ctx)
    })

    this.bot.command('nik', async (ctx) => {
      await this.handleNikCommand(ctx)
    })

    this.bot.command('npwp', async (ctx) => {
      await this.handleNpwpCommand(ctx)
    })

    this.bot.command('imei', async (ctx) => {
      await this.handleImeiCommand(ctx)
    })

    // Status command
    this.bot.command('status', async (ctx) => {
      await this.handleStatusCommand(ctx)
    })

    // Plan command
    this.bot.command('plan', async (ctx) => {
      await this.handlePlanCommand(ctx)
    })

    // Update last activity on any message
    this.bot.on('message', async (ctx, next) => {
      if (this.session) {
        this.session.lastActivity = new Date()
        await this.updateLastActivity()
      }
      await next()
    })
  }

  private async handleHelpCommand(ctx: Context) {
    const helpText = `🤖 *KodeXGuard Bot Commands*\n\n` +
      `/help - Show this help message\n` +
      `/start - Start the bot\n` +
      `/scan <url> - Scan a URL for vulnerabilities\n` +
      `/osint <type> <value> - OSINT investigation\n` +
      `/nik <16-digit-nik> - Check NIK information\n` +
      `/npwp <15-digit-npwp> - Check NPWP information\n` +
      `/imei <15-digit-imei> - Check IMEI information\n` +
      `/status - Check your account status\n` +
      `/plan - Check your subscription plan\n\n` +
      `📱 *Need help?* Visit: https://kodexguard.com/docs`

    await ctx.replyWithMarkdown(helpText)
  }

  private async handleStartCommand(ctx: Context) {
    const userId = await this.getUserByTelegramId(ctx.from?.id)
    
    if (!userId) {
      await ctx.reply(
        '❌ You need to register on KodeXGuard platform first.\n\nVisit: https://kodexguard.com/register'
      )
      return
    }

    await ctx.replyWithMarkdown(
      `👋 Welcome to *KodeXGuard Bot*!\n\n` +
      `I can help you with cybersecurity tasks like vulnerability scanning, OSINT investigations, and more.\n\n` +
      `Type /help to see available commands.`
    )
  }

  private async handleScanCommand(ctx: Context) {
    const args = ctx.message?.text?.split(' ').slice(1) || []
    const userId = await this.getUserByTelegramId(ctx.from?.id)
    
    if (!userId) {
      await ctx.reply('❌ You need to register on KodeXGuard platform first.')
      return
    }

    if (args.length === 0) {
      await ctx.reply('❌ Usage: /scan <url>')
      return
    }

    const url = args[0]

    try {
      await ctx.reply('🔍 Starting vulnerability scan...')
      
      // Start scan (simplified)
      const scanId = await this.scannerService.startScan({
        id: `tg_${Date.now()}`,
        targetUrl: url,
        scanType: 'basic',
        options: {
          checkSQLi: true,
          checkXSS: true,
          checkLFI: false,
          checkRCE: false
        }
      }, userId)

      await ctx.reply(
        `✅ Scan started!\nScan ID: ${scanId}\n\nYou'll receive results when scan completes.`
      )
    } catch (error) {
      await ctx.reply(`❌ Scan failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  private async handleOsintCommand(ctx: Context) {
    await ctx.reply('🔍 OSINT investigation feature coming soon!')
  }

  private async handleNikCommand(ctx: Context) {
    const args = ctx.message?.text?.split(' ').slice(1) || []
    const userId = await this.getUserByTelegramId(ctx.from?.id)
    
    if (!userId) {
      await ctx.reply('❌ You need to register on KodeXGuard platform first.')
      return
    }

    if (args.length === 0) {
      await ctx.reply('❌ Usage: /nik <16-digit-nik>')
      return
    }

    const nik = args[0]

    try {
      await ctx.reply('🔍 Investigating NIK...')
      
      const results = await this.osintService.investigate({
        id: Date.now(),
        type: 'nik',
        value: nik,
        sources: ['all']
      })

      let resultText = '📋 *NIK Investigation Results*\n\n'
      results.forEach(result => {
        resultText += `*${result.source}*: ${result.found ? '✅ Found' : '❌ Not Found'}\n`
        if (result.data && typeof result.data === 'object') {
          resultText += `Confidence: ${Math.round(result.confidence * 100)}%\n`
        }
        resultText += '\n'
      })

      await ctx.replyWithMarkdown(resultText)
    } catch (error) {
      await ctx.reply(`❌ NIK investigation failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  private async handleNpwpCommand(ctx: Context) {
    const args = ctx.message?.text?.split(' ').slice(1) || []
    const userId = await this.getUserByTelegramId(ctx.from?.id)

    if (!userId) {
      await ctx.reply('❌ You need to register on KodeXGuard platform first.')
      return
    }

    if (args.length === 0) {
      await ctx.reply('❌ Usage: /npwp <15-digit-npwp>')
      return
    }

    const npwp = args[0]

    try {
      await ctx.reply('🔍 Investigating NPWP...')

      const results = await this.osintService.investigate({
        id: Date.now(),
        type: 'npwp',
        value: npwp,
        sources: ['all']
      })

      let resultText = '📋 *NPWP Investigation Results*\n\n'
      results.forEach(result => {
        resultText += `*${result.source}*: ${result.found ? '✅ Found' : '❌ Not Found'}\n`
        if (result.data && typeof result.data === 'object') {
          resultText += `Confidence: ${Math.round(result.confidence * 100)}%\n`

          // Add specific NPWP data if available
          if (result.data.npwp) {
            resultText += `NPWP: ${result.data.npwp}\n`
          }
          if (result.data.name) {
            resultText += `Name: ${result.data.name}\n`
          }
          if (result.data.address) {
            resultText += `Address: ${result.data.address}\n`
          }
          if (result.data.riskLevel) {
            resultText += `Risk Level: ${result.data.riskLevel}\n`
          }
        }
        resultText += '\n'
      })

      resultText += '⚠️ *Disclaimer*: This information is for educational purposes only.'

      await ctx.replyWithMarkdown(resultText)
    } catch (error) {
      await ctx.reply(`❌ NPWP investigation failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  private async handleImeiCommand(ctx: Context) {
    const args = ctx.message?.text?.split(' ').slice(1) || []
    const userId = await this.getUserByTelegramId(ctx.from?.id)

    if (!userId) {
      await ctx.reply('❌ You need to register on KodeXGuard platform first.')
      return
    }

    if (args.length === 0) {
      await ctx.reply('❌ Usage: /imei <15-digit-imei>')
      return
    }

    const imei = args[0]

    try {
      await ctx.reply('🔍 Investigating IMEI...')

      const results = await this.osintService.investigate({
        id: Date.now(),
        type: 'imei',
        value: imei,
        sources: ['all']
      })

      let resultText = '📋 *IMEI Investigation Results*\n\n'
      results.forEach(result => {
        resultText += `*${result.source}*: ${result.found ? '✅ Found' : '❌ Not Found'}\n`
        if (result.data && typeof result.data === 'object') {
          resultText += `Confidence: ${Math.round(result.confidence * 100)}%\n`

          // Add specific IMEI data if available
          if (result.data.imei) {
            resultText += `IMEI: ${result.data.imei}\n`
          }
          if (result.data.manufacturer) {
            resultText += `Manufacturer: ${result.data.manufacturer}\n`
          }
          if (result.data.model) {
            resultText += `Model: ${result.data.model}\n`
          }
          if (result.data.riskLevel) {
            resultText += `Risk Level: ${result.data.riskLevel}\n`
          }
          if (result.data.operators && result.data.operators.length > 0) {
            resultText += `Operators: ${result.data.operators.map((op: any) => op.operator).join(', ')}\n`
          }
        }
        resultText += '\n'
      })

      resultText += '⚠️ *Disclaimer*: This information is for educational purposes only.'

      await ctx.replyWithMarkdown(resultText)
    } catch (error) {
      await ctx.reply(`❌ IMEI investigation failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  private async handleStatusCommand(ctx: Context) {
    const userId = await this.getUserByTelegramId(ctx.from?.id)
    
    if (!userId) {
      await ctx.reply('❌ You need to register on KodeXGuard platform first.')
      return
    }

    try {
      const [userRows] = await db.query(
        'SELECT username, plan, score, level FROM users WHERE id = ?',
        [userId]
      )
      
      if (!userRows || (userRows as any[]).length === 0) {
        await ctx.reply('❌ User not found')
        return
      }
      
      const user = (userRows as any[])[0]
      
      const statusText = `👤 *Account Status*\n\n` +
        `Username: ${user.username}\n` +
        `Plan: ${user.plan}\n` +
        `Score: ${user.score}\n` +
        `Level: ${user.level}`
      
      await ctx.replyWithMarkdown(statusText)
    } catch (error) {
      await ctx.reply('❌ Failed to get status')
    }
  }

  private async handlePlanCommand(ctx: Context) {
    await ctx.reply('💼 Plan management feature coming soon!')
  }

  private async getUserByTelegramId(telegramId?: number): Promise<number | null> {
    if (!telegramId) return null
    
    try {
      const [rows] = await db.query(
        'SELECT id FROM users WHERE telegram_id = ?',
        [telegramId]
      )
      
      if (rows && (rows as any[]).length > 0) {
        return (rows as any[])[0].id
      }
      
      return null
    } catch (error) {
      console.error('Error getting user by Telegram ID:', error)
      return null
    }
  }

  private async saveSessionToDatabase(): Promise<void> {
    if (!this.session) return

    try {
      await db.query(
        `INSERT INTO bot_instances (id, type, name, status, created_at) 
         VALUES (?, 'telegram', ?, 'connected', NOW())
         ON DUPLICATE KEY UPDATE status = 'connected', updated_at = NOW()`,
        [this.session.id, this.session.botUsername]
      )
    } catch (error) {
      console.error('Error saving session to database:', error)
    }
  }

  private async updateSessionStatus(status: string): Promise<void> {
    if (!this.session) return

    try {
      await db.query(
        'UPDATE bot_instances SET status = ?, updated_at = NOW() WHERE id = ?',
        [status, this.session.id]
      )
    } catch (error) {
      console.error('Error updating session status:', error)
    }
  }

  private async updateLastActivity(): Promise<void> {
    if (!this.session) return

    try {
      await db.query(
        'UPDATE bot_instances SET last_activity = NOW() WHERE id = ?',
        [this.session.id]
      )
    } catch (error) {
      console.error('Error updating last activity:', error)
    }
  }
}
