import { NextRequest, NextResponse } from 'next/server'
import jwt from 'jsonwebtoken'

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-here-make-it-very-long-and-secure-for-production'

// Simple in-memory user storage (replace with database in production)
const users = new Map([
  ['1', {
    id: '1',
    username: 'admin',
    email: '<EMAIL>',
    fullName: '<PERSON><PERSON>Guard',
    role: 'admin',
    plan: 'Elite',
    bio: 'Cybersecurity expert and platform administrator. Passionate about protecting digital assets and educating the community about security best practices.',
    location: 'Jakarta, Indonesia',
    phone: '+62 812-3456-7890',
    website: 'https://kodexguard.com',
    github: 'kodexguard',
    twitter: 'kodexguard',
    linkedin: 'kodexguard',
    avatar: null,
    joinDate: '2024-01-01T00:00:00Z',
    lastActive: new Date().toISOString(),
    level: 28,
    score: 8950,
    streak: 12,
    achievements: [
      {
        id: '1',
        name: 'First Scan',
        description: 'Completed your first vulnerability scan',
        icon: '🎯',
        unlockedAt: '2024-01-02T10:00:00Z',
        rarity: 'common'
      },
      {
        id: '2',
        name: 'Bug Hunter',
        description: 'Found 100 vulnerabilities',
        icon: '🐛',
        unlockedAt: '2024-02-15T14:30:00Z',
        rarity: 'rare'
      },
      {
        id: '3',
        name: 'Elite Member',
        description: 'Upgraded to Elite plan',
        icon: '👑',
        unlockedAt: '2024-01-01T00:00:00Z',
        rarity: 'epic'
      },
      {
        id: '4',
        name: 'Community Leader',
        description: 'Helped 50+ community members',
        icon: '🌟',
        unlockedAt: '2024-03-10T16:45:00Z',
        rarity: 'legendary'
      },
      {
        id: '5',
        name: 'Streak Master',
        description: 'Maintained 30-day activity streak',
        icon: '🔥',
        unlockedAt: '2024-03-20T09:15:00Z',
        rarity: 'epic'
      }
    ],
    stats: {
      totalScans: 1247,
      vulnerabilitiesFound: 3892,
      reportsGenerated: 156,
      toolsUsed: 23,
      communityRank: 1,
      monthlyActivity: [45, 52, 38, 61, 47, 55, 42, 58, 49, 63, 51, 46]
    },
    preferences: {
      notifications: {
        email: true,
        push: true,
        sms: false,
        security: true,
        marketing: false
      },
      privacy: {
        profileVisibility: 'public',
        showEmail: false,
        showPhone: false,
        showLocation: true
      },
      security: {
        twoFactorEnabled: true,
        loginAlerts: true,
        sessionTimeout: 30
      }
    }
  }]
])

function getUserFromToken(request: NextRequest) {
  try {
    const cookies = request.headers.get('cookie')
    if (!cookies) return null

    const tokenMatch = cookies.match(/accessToken=([^;]+)/)
    const token = tokenMatch ? tokenMatch[1] : null

    if (!token) return null

    const decoded = jwt.verify(token, JWT_SECRET) as any
    return decoded
  } catch (error) {
    console.error('Token verification error:', error)
    return null
  }
}

export async function GET(request: NextRequest) {
  try {
    console.log('📋 Profile API: GET request received')
    
    const user = getUserFromToken(request)
    if (!user) {
      console.log('❌ Profile API: No valid token')
      return NextResponse.json({ success: false, error: 'Unauthorized' }, { status: 401 })
    }

    console.log('✅ Profile API: User authenticated:', user.username)

    const userProfile = users.get(user.id.toString())
    if (!userProfile) {
      console.log('❌ Profile API: User not found')
      return NextResponse.json({ success: false, error: 'User not found' }, { status: 404 })
    }

    console.log('✅ Profile API: Profile data retrieved successfully')

    return NextResponse.json({
      success: true,
      data: userProfile
    })

  } catch (error) {
    console.error('❌ Profile API Error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    console.log('📝 Profile API: PUT request received')
    
    const user = getUserFromToken(request)
    if (!user) {
      console.log('❌ Profile API: No valid token')
      return NextResponse.json({ success: false, error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    console.log('📝 Profile API: Update data:', body)

    const userProfile = users.get(user.id.toString())
    if (!userProfile) {
      console.log('❌ Profile API: User not found')
      return NextResponse.json({ success: false, error: 'User not found' }, { status: 404 })
    }

    // Update allowed fields
    const allowedFields = ['fullName', 'bio', 'location', 'phone', 'website', 'github', 'twitter', 'linkedin']
    const updatedProfile = { ...userProfile }

    allowedFields.forEach(field => {
      if (body[field] !== undefined) {
        updatedProfile[field] = body[field]
      }
    })

    // Update last active
    updatedProfile.lastActive = new Date().toISOString()

    // Save updated profile
    users.set(user.id.toString(), updatedProfile)

    console.log('✅ Profile API: Profile updated successfully')

    return NextResponse.json({
      success: true,
      data: updatedProfile,
      message: 'Profile updated successfully'
    })

  } catch (error) {
    console.error('❌ Profile API Error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
