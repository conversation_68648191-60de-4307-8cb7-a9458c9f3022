import { NextRequest, NextResponse } from 'next/server'
import { SimpleAuthService } from '@/lib/auth-simple'

export async function GET(request: NextRequest) {
  try {
    // Get authenticated user and verify admin access
    const authResult = await SimpleAuthService.authenticateRequest(request)

    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Check if user is admin
    if (authResult.user.role !== 'admin') {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      )
    }

    // Get all users from SimpleAuthService
    const allUsers = SimpleAuthService.getAllUsers()

    // Calculate statistics based on real user data
    const totalUsers = allUsers.length
    const activeUsers = allUsers.filter(user => {
      const lastActive = new Date(user.last_active)
      const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000)
      return lastActive > oneDayAgo
    }).length

    const newUsersToday = allUsers.filter(user => {
      const createdAt = new Date(user.created_at)
      const today = new Date()
      return createdAt.toDateString() === today.toDateString()
    }).length

    // Generate realistic statistics based on user data
    const totalScans = allUsers.reduce((sum, user) => sum + Math.floor(user.score / 10), 0) + Math.floor(Math.random() * 500)
    const scansToday = Math.floor(totalScans * 0.05) + Math.floor(Math.random() * 50)
    const totalVulnerabilities = Math.floor(totalScans * 0.3) + Math.floor(Math.random() * 200)
    const criticalVulnerabilities = Math.floor(totalVulnerabilities * 0.15)

    // Calculate users by plan
    const usersByPlan = allUsers.reduce((acc, user) => {
      acc[user.plan] = (acc[user.plan] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    // Generate revenue statistics
    const planPrices = {
      'Free': 0,
      'Student': 5,
      'Hobby': 15,
      'Bughunter': 50,
      'Cybersecurity': 100,
      'Pro': 200,
      'Expert': 500,
      'Elite': 1000
    }

    const monthlyRevenue = allUsers.reduce((sum, user) => {
      return sum + (planPrices[user.plan as keyof typeof planPrices] || 0)
    }, 0)

    // Generate recent activities based on user data
    const recentActivities = [
      {
        id: '1',
        type: 'user_registration',
        description: `New user ${allUsers[allUsers.length - 1]?.username || 'admin'} registered`,
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        severity: 'low',
        userId: allUsers[allUsers.length - 1]?.id,
        username: allUsers[allUsers.length - 1]?.username
      },
      {
        id: '2',
        type: 'scan_completed',
        description: 'Vulnerability scan completed on target.example.com',
        timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
        severity: 'medium',
        userId: 1,
        username: 'admin'
      },
      {
        id: '3',
        type: 'threat_detected',
        description: 'Critical threat detected in uploaded file',
        timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
        severity: 'critical',
        userId: 1,
        username: 'admin'
      },
      {
        id: '4',
        type: 'system_alert',
        description: 'High server load detected',
        timestamp: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString(),
        severity: 'high'
      },
      {
        id: '5',
        type: 'scan_completed',
        description: 'OSINT investigation completed',
        timestamp: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString(),
        severity: 'low',
        userId: 1,
        username: 'admin'
      }
    ]

    // Generate chart data (last 30 days)
    const userGrowthChart = Array.from({ length: 30 }, (_, i) => {
      const date = new Date()
      date.setDate(date.getDate() - (29 - i))
      return {
        date: date.toISOString().split('T')[0],
        users: Math.floor(Math.random() * 10) + (i * 0.5),
        registrations: Math.floor(Math.random() * 5)
      }
    })

    const activityChart = Array.from({ length: 30 }, (_, i) => {
      const date = new Date()
      date.setDate(date.getDate() - (29 - i))
      return {
        date: date.toISOString().split('T')[0],
        scans: Math.floor(Math.random() * 50) + 10,
        osint: Math.floor(Math.random() * 30) + 5,
        files: Math.floor(Math.random() * 20) + 2
      }
    })

    // Bot status
    const botStatus = {
      whatsapp: {
        status: 'online',
        uptime: 99.5,
        lastActivity: new Date(Date.now() - 5 * 60 * 1000).toISOString()
      },
      telegram: {
        status: 'online',
        uptime: 98.8,
        lastActivity: new Date(Date.now() - 10 * 60 * 1000).toISOString()
      }
    }

    return NextResponse.json({
      success: true,
      data: {
        overview: {
          totalUsers,
          activeUsers,
          newUsersToday,
          totalScans,
          scansToday,
          totalVulnerabilities,
          criticalVulnerabilities,
          systemUptime: '99.8%',
          serverLoad: Math.floor(Math.random() * 30) + 20
        },
        users: {
          total: totalUsers,
          active: activeUsers,
          inactive: totalUsers - activeUsers,
          byPlan: usersByPlan,
          growth: userGrowthChart,
          list: allUsers.map(user => ({
            id: user.id,
            username: user.username,
            email: user.email,
            plan: user.plan,
            level: user.level,
            score: user.score,
            joinedAt: user.created_at,
            lastActive: user.last_active,
            status: new Date(user.last_active) > new Date(Date.now() - 24 * 60 * 60 * 1000) ? 'active' : 'inactive',
            emailVerified: user.email_verified
          }))
        },
        scans: {
          total: totalScans,
          today: scansToday,
          vulnerabilities: totalVulnerabilities,
          critical: criticalVulnerabilities,
          activity: activityChart
        },
        files: {
          total: Math.floor(totalScans * 0.3),
          threatsDetected: Math.floor(totalScans * 0.05),
          threatRate: 15
        },
        osint: {
          total: Math.floor(totalScans * 0.8),
          cveSearches: Math.floor(totalScans * 0.2),
          dorkingQueries: Math.floor(totalScans * 0.1)
        },
        system: {
          uptime: '99.8%',
          serverLoad: Math.floor(Math.random() * 30) + 20,
          memoryUsage: Math.floor(Math.random() * 40) + 40,
          diskUsage: Math.floor(Math.random() * 30) + 15
        },
        revenue: {
          total: monthlyRevenue * 12,
          thisMonth: monthlyRevenue,
          lastMonth: monthlyRevenue * 0.9,
          growth: 10,
          byPlan: Object.entries(usersByPlan).reduce((acc, [plan, count]) => {
            acc[plan] = count * (planPrices[plan as keyof typeof planPrices] || 0)
            return acc
          }, {} as Record<string, number>),
          projectedAnnual: monthlyRevenue * 12 * 1.2
        },
        bots: botStatus,
        activities: recentActivities,
        charts: {
          userGrowth: userGrowthChart,
          activity: activityChart
        }
      }
    })

  } catch (error) {
    console.error('Admin stats error:', error)

    return NextResponse.json(
      {
        success: false,
        message: 'Internal server error',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
