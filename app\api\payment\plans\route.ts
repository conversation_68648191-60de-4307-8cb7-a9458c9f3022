import { NextRequest, NextResponse } from 'next/server'
import { PaymentService } from '@/lib/services/payment'
import { verifyToken } from '@/lib/auth'

const paymentService = new PaymentService()

export async function GET(request: NextRequest) {
  try {
    // Get available plans
    const plans = await paymentService.getAvailablePlans()

    return NextResponse.json({
      success: true,
      data: {
        plans,
        currency: 'IDR',
        supportedGateways: ['tripay', 'midtrans', 'xendit', 'manual'],
        features: {
          free: {
            highlight: 'Perfect for getting started',
            limitations: ['Limited daily usage', 'Basic support', 'No API access']
          },
          student: {
            highlight: 'Special pricing for students',
            benefits: ['50% discount', 'API access', 'Priority support']
          },
          hobby: {
            highlight: 'For hobbyist developers',
            benefits: ['Increased limits', 'API access', 'Email support']
          },
          bughunter: {
            highlight: 'For bug bounty hunters',
            benefits: ['High limits', 'Advanced tools', 'Priority support']
          },
          cybersecurity: {
            highlight: 'Enterprise-grade features',
            benefits: ['Unlimited usage', 'Team features', 'White-label options']
          }
        }
      }
    })

  } catch (error) {
    console.error('Error fetching plans:', error)
    return NextResponse.json(
      { error: 'Failed to fetch plans' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const token = request.headers.get('authorization')?.replace('Bearer ', '')
    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const user = await verifyToken(token)
    if (!user || user.role !== 'super_admin') {
      return NextResponse.json({ error: 'Forbidden - Super Admin only' }, { status: 403 })
    }

    const body = await request.json()
    const { 
      name, 
      type, 
      price, 
      currency, 
      duration, 
      features, 
      description,
      isPopular = false 
    } = body

    // Validate required fields
    if (!name || !type || !price || !currency || !duration || !features || !description) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 })
    }

    // Validate plan type
    const validTypes = ['free', 'student', 'hobby', 'bughunter', 'cybersecurity']
    if (!validTypes.includes(type)) {
      return NextResponse.json({ error: 'Invalid plan type' }, { status: 400 })
    }

    // Validate duration
    const validDurations = ['daily', 'weekly', 'monthly', 'yearly']
    if (!validDurations.includes(duration)) {
      return NextResponse.json({ error: 'Invalid duration' }, { status: 400 })
    }

    // Create plan ID
    const planId = `${type}_${duration}_${Date.now()}`

    // Insert into database
    const { db } = await import('@/lib/database')
    await db.query(`
      INSERT INTO plans (
        id, name, type, price, currency, duration, 
        features, description, is_popular, is_active, created_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, true, NOW())
    `, [
      planId, name, type, price, currency, duration,
      JSON.stringify(features), description, isPopular
    ])

    return NextResponse.json({
      success: true,
      data: {
        planId,
        message: 'Plan created successfully'
      }
    })

  } catch (error) {
    console.error('Error creating plan:', error)
    return NextResponse.json(
      { error: 'Failed to create plan' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const token = request.headers.get('authorization')?.replace('Bearer ', '')
    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const user = await verifyToken(token)
    if (!user || user.role !== 'super_admin') {
      return NextResponse.json({ error: 'Forbidden - Super Admin only' }, { status: 403 })
    }

    const body = await request.json()
    const { planId, updates } = body

    if (!planId || !updates) {
      return NextResponse.json({ error: 'Missing planId or updates' }, { status: 400 })
    }

    // Build update query
    const updateFields = []
    const updateValues = []

    if (updates.name) {
      updateFields.push('name = ?')
      updateValues.push(updates.name)
    }
    if (updates.price !== undefined) {
      updateFields.push('price = ?')
      updateValues.push(updates.price)
    }
    if (updates.features) {
      updateFields.push('features = ?')
      updateValues.push(JSON.stringify(updates.features))
    }
    if (updates.description) {
      updateFields.push('description = ?')
      updateValues.push(updates.description)
    }
    if (updates.isPopular !== undefined) {
      updateFields.push('is_popular = ?')
      updateValues.push(updates.isPopular)
    }
    if (updates.isActive !== undefined) {
      updateFields.push('is_active = ?')
      updateValues.push(updates.isActive)
    }

    if (updateFields.length === 0) {
      return NextResponse.json({ error: 'No valid updates provided' }, { status: 400 })
    }

    updateFields.push('updated_at = NOW()')
    updateValues.push(planId)

    const { db } = await import('@/lib/database')
    await db.query(`
      UPDATE plans SET ${updateFields.join(', ')} WHERE id = ?
    `, updateValues)

    return NextResponse.json({
      success: true,
      message: 'Plan updated successfully'
    })

  } catch (error) {
    console.error('Error updating plan:', error)
    return NextResponse.json(
      { error: 'Failed to update plan' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const token = request.headers.get('authorization')?.replace('Bearer ', '')
    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const user = await verifyToken(token)
    if (!user || user.role !== 'super_admin') {
      return NextResponse.json({ error: 'Forbidden - Super Admin only' }, { status: 403 })
    }

    const { searchParams } = new URL(request.url)
    const planId = searchParams.get('planId')

    if (!planId) {
      return NextResponse.json({ error: 'Missing planId' }, { status: 400 })
    }

    // Soft delete - set is_active to false
    const { db } = await import('@/lib/database')
    await db.query(`
      UPDATE plans SET is_active = false, updated_at = NOW() WHERE id = ?
    `, [planId])

    return NextResponse.json({
      success: true,
      message: 'Plan deactivated successfully'
    })

  } catch (error) {
    console.error('Error deleting plan:', error)
    return NextResponse.json(
      { error: 'Failed to delete plan' },
      { status: 500 }
    )
  }
}
