'use client'

import { useState, useEffect } from 'react'
import DashboardLayout from '@/components/DashboardLayout'
import { 
  User, 
  Mail, 
  Phone,
  MapPin,
  Calendar,
  Shield,
  Crown,
  Star,
  Award,
  Target,
  Zap,
  Activity,
  TrendingUp,
  Edit,
  Save,
  Camera,
  Lock,
  Key,
  Bell,
  Globe,
  Eye,
  EyeOff,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Flame,
  Trophy,
  Bug,
  Search,
  FileText,
  Database
} from 'lucide-react'

interface UserProfile {
  id: string
  username: string
  email: string
  fullName: string
  avatar?: string
  bio: string
  location: string
  website: string
  joinedAt: string
  lastActive: string
  plan: 'Free' | 'Pro' | 'Expert' | 'Elite'
  level: number
  score: number
  rank: number
  streak: number
  badges: Badge[]
  stats: UserStats
  preferences: UserPreferences
}

interface Badge {
  id: string
  name: string
  description: string
  icon: string
  color: string
  earnedAt: string
  rarity: 'common' | 'rare' | 'epic' | 'legendary'
}

interface UserStats {
  totalScans: number
  vulnerabilitiesFound: number
  osintQueries: number
  filesAnalyzed: number
  cveReported: number
  toolsUsed: number
  daysActive: number
  pointsThisWeek: number
  pointsThisMonth: number
}

interface UserPreferences {
  emailNotifications: boolean
  pushNotifications: boolean
  publicProfile: boolean
  showStats: boolean
  theme: 'dark' | 'light' | 'auto'
  language: string
}

export default function ProfilePage() {
  const [profile, setProfile] = useState<UserProfile | null>(null)
  const [loading, setLoading] = useState(true)
  const [editing, setEditing] = useState(false)
  const [activeTab, setActiveTab] = useState('overview')

  useEffect(() => {
    loadProfile()
  }, [])

  const loadProfile = async () => {
    try {
      console.log('🔄 Loading profile data...')
      setLoading(true)

      const response = await fetch('/api/profile')
      const data = await response.json()

      if (data.success) {
        console.log('✅ Profile data loaded:', data.data)

        // Transform API data to match component interface
        const transformedProfile: UserProfile = {
          id: data.data.id,
          username: data.data.username,
          email: data.data.email,
          fullName: data.data.fullName,
          bio: data.data.bio || '',
          location: data.data.location || '',
          website: data.data.website || '',
          joinedAt: new Date(data.data.joinDate).toLocaleDateString(),
          lastActive: new Date(data.data.lastActive).toLocaleDateString(),
          plan: data.data.plan,
          level: data.data.level,
          score: data.data.score,
          rank: data.data.stats.communityRank,
          streak: data.data.streak,
          badges: data.data.achievements.map((achievement: any) => ({
            id: achievement.id,
            name: achievement.name,
            description: achievement.description,
            icon: achievement.icon,
            color: achievement.rarity === 'legendary' ? 'yellow' :
                   achievement.rarity === 'epic' ? 'purple' :
                   achievement.rarity === 'rare' ? 'blue' : 'gray',
            earnedAt: new Date(achievement.unlockedAt).toLocaleDateString(),
            rarity: achievement.rarity
          })),
          stats: {
            totalScans: data.data.stats.totalScans,
            vulnerabilitiesFound: data.data.stats.vulnerabilitiesFound,
            osintQueries: data.data.stats.totalScans * 3,
            filesAnalyzed: data.data.stats.reportsGenerated,
            cveReported: Math.floor(data.data.stats.vulnerabilitiesFound / 100),
            toolsUsed: data.data.stats.toolsUsed,
            daysActive: Math.floor((new Date().getTime() - new Date(data.data.joinDate).getTime()) / (1000 * 60 * 60 * 24)),
            pointsThisWeek: Math.floor(data.data.score * 0.1),
            pointsThisMonth: Math.floor(data.data.score * 0.3)
          },
          preferences: {
            emailNotifications: true,
            pushNotifications: false,
            publicProfile: true,
            showStats: true,
            theme: 'dark',
            language: 'en'
          }
        }

        setProfile(transformedProfile)
      } else {
        console.error('❌ Failed to load profile:', data.error)
      }
    } catch (error) {
      console.error('❌ Error loading profile:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSaveProfile = async (updatedData: Partial<UserProfile>) => {
    try {
      console.log('💾 Saving profile data...')

      const response = await fetch('/api/profile', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updatedData),
      })

      const data = await response.json()

      if (data.success) {
        console.log('✅ Profile saved successfully')
        await loadProfile() // Reload profile data
        setEditing(false)
      } else {
        console.error('❌ Failed to save profile:', data.error)
      }
    } catch (error) {
      console.error('❌ Error saving profile:', error)
    }
  }

  const getBadgeIcon = (icon: string) => {
    switch (icon) {
      case 'bug': return Bug
      case 'search': return Search
      case 'shield': return Shield
      case 'trophy': return Trophy
      default: return Award
    }
  }

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'legendary': return 'text-yellow-400 bg-yellow-400/20 border-yellow-400'
      case 'epic': return 'text-purple-400 bg-purple-400/20 border-purple-400'
      case 'rare': return 'text-blue-400 bg-blue-400/20 border-blue-400'
      case 'common': return 'text-gray-400 bg-gray-400/20 border-gray-400'
      default: return 'text-gray-400 bg-gray-400/20 border-gray-400'
    }
  }

  const getPlanColor = (plan: string) => {
    switch (plan) {
      case 'Elite': return 'text-yellow-400 bg-yellow-400/20'
      case 'Expert': return 'text-purple-400 bg-purple-400/20'
      case 'Pro': return 'text-blue-400 bg-blue-400/20'
      case 'Free': return 'text-gray-400 bg-gray-400/20'
      default: return 'text-gray-400 bg-gray-400/20'
    }
  }

  if (loading) {
    return (
      <DashboardLayout>
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-2 border-cyber-primary border-t-transparent mx-auto mb-4"></div>
            <div className="text-cyber-primary font-medium">Loading profile...</div>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  if (!profile) {
    return (
      <DashboardLayout>
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <XCircle className="h-12 w-12 text-red-400 mx-auto mb-4" />
            <div className="text-red-400 font-medium">Failed to load profile</div>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-8">
        {/* Header */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
          <div>
            <h1 className="text-4xl font-bold mb-2">
              <span className="text-cyber-glow">User</span>{' '}
              <span className="text-cyber-pink">Profile</span>
            </h1>
            <p className="text-gray-300 text-lg">
              Manage your account and preferences
            </p>
          </div>
          
          <div className="mt-6 lg:mt-0 flex items-center space-x-4">
            {editing ? (
              <>
                <button 
                  onClick={() => setEditing(false)}
                  className="btn-cyber-secondary"
                >
                  Cancel
                </button>
                <button 
                  onClick={handleSaveProfile}
                  className="btn-cyber-primary"
                >
                  <Save className="h-4 w-4 mr-2" />
                  Save Changes
                </button>
              </>
            ) : (
              <button 
                onClick={() => setEditing(true)}
                className="btn-cyber-primary"
              >
                <Edit className="h-4 w-4 mr-2" />
                Edit Profile
              </button>
            )}
          </div>
        </div>

        {/* Profile Header */}
        <div className="card-cyber">
          <div className="flex flex-col lg:flex-row lg:items-center lg:space-x-8">
            <div className="relative mb-6 lg:mb-0">
              <div className="w-32 h-32 rounded-full bg-gradient-to-r from-cyber-primary to-cyber-secondary flex items-center justify-center text-4xl font-bold text-black">
                {profile.username.charAt(0)}
              </div>
              {editing && (
                <button className="absolute bottom-0 right-0 p-2 rounded-full bg-cyber-primary text-black hover:bg-cyber-primary/80 transition-colors">
                  <Camera className="h-4 w-4" />
                </button>
              )}
              {profile.streak > 0 && (
                <div className="absolute -top-2 -right-2 bg-cyber-secondary text-black text-sm font-bold px-3 py-1 rounded-full flex items-center space-x-1">
                  <Flame className="h-4 w-4" />
                  <span>{profile.streak}</span>
                </div>
              )}
            </div>
            
            <div className="flex-1">
              <div className="flex items-center space-x-4 mb-4">
                <h2 className="text-3xl font-bold text-white">{profile.username}</h2>
                <div className={`px-3 py-1 rounded-full text-sm font-bold ${getPlanColor(profile.plan)}`}>
                  {profile.plan}
                </div>
              </div>
              
              <p className="text-gray-300 text-lg mb-4">{profile.fullName}</p>
              
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-cyber-primary">{profile.level}</div>
                  <div className="text-sm text-gray-400">Level</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-cyber-secondary">{profile.score.toLocaleString()}</div>
                  <div className="text-sm text-gray-400">Score</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-cyber-accent">#{profile.rank}</div>
                  <div className="text-sm text-gray-400">Rank</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-400">{profile.stats.daysActive}</div>
                  <div className="text-sm text-gray-400">Days Active</div>
                </div>
              </div>
              
              <p className="text-gray-400">{profile.bio}</p>
            </div>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="flex space-x-1 bg-cyber-dark/50 p-1 rounded-lg">
          {[
            { id: 'overview', label: 'Overview', icon: User },
            { id: 'stats', label: 'Statistics', icon: TrendingUp },
            { id: 'badges', label: 'Badges', icon: Award },
            { id: 'settings', label: 'Settings', icon: Lock }
          ].map((tab) => {
            const Icon = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
                  activeTab === tab.id
                    ? 'bg-cyber-primary/20 text-cyber-primary border border-cyber-primary/30'
                    : 'text-gray-400 hover:text-white hover:bg-cyber-primary/10'
                }`}
              >
                <Icon className="h-4 w-4" />
                <span>{tab.label}</span>
              </button>
            )
          })}
        </div>
      </div>
    </DashboardLayout>
  )
}
