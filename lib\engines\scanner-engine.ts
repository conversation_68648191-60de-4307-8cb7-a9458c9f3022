import { spawn, exec } from 'child_process'
import { promisify } from 'util'
import { db } from '../database'
import axios from 'axios'
import crypto from 'crypto'
import fs from 'fs'
import path from 'path'

const execAsync = promisify(exec)

export interface ScanTarget {
  id: string
  target: string // IP, domain, or CIDR range
  scanType: 'nmap' | 'masscan' | 'zmap' | 'custom' | 'shodan' | 'comprehensive'
  ports: string // Port range (e.g., "1-1000", "80,443,8080")
  options: ScanOptions
  // Enhanced options
  priority: 'low' | 'normal' | 'high' | 'critical'
  stealth: boolean
  evasion: boolean
  geolocation: boolean
  vulnerabilityCheck: boolean
  serviceEnumeration: boolean
}

export interface ScanOptions {
  aggressive: boolean
  serviceDetection: boolean
  osDetection: boolean
  scriptScan: boolean
  timing: 1 | 2 | 3 | 4 | 5 // T1-T5
  maxRetries: number
  timeout: number
  userAgent?: string
  sourcePort?: number
  decoy?: string[]
  // Advanced options
  fragmentPackets: boolean
  randomizeHosts: boolean
  spoofMac?: string
  dataLength?: number
  ttl?: number
  badsum: boolean
  scanDelay?: number
  maxParallelism?: number
  minRate?: number
  maxRate?: number
}

export interface PortResult {
  port: number
  protocol: 'tcp' | 'udp'
  state: 'open' | 'closed' | 'filtered' | 'unfiltered' | 'open|filtered' | 'closed|filtered'
  service?: string
  version?: string
  banner?: string
  cpe?: string[]
  scripts?: Record<string, any>
  // Enhanced port information
  confidence: number
  reason: string
  reasonTtl?: number
  vulnerabilities?: VulnerabilityInfo[]
  ssl?: SSLInfo
  http?: HTTPInfo
  riskLevel: 'low' | 'medium' | 'high' | 'critical'
}

export interface VulnerabilityInfo {
  id: string
  type: 'CVE' | 'exploit' | 'misconfiguration'
  severity: 'low' | 'medium' | 'high' | 'critical'
  description: string
  references: string[]
  exploitAvailable: boolean
}

export interface SSLInfo {
  version: string
  cipher: string
  certificate: {
    subject: string
    issuer: string
    validFrom: string
    validTo: string
    fingerprint: string
  }
  vulnerabilities: string[]
}

export interface HTTPInfo {
  statusCode: number
  server: string
  headers: Record<string, string>
  title?: string
  technologies: string[]
  forms: FormInfo[]
  cookies: CookieInfo[]
}

export interface FormInfo {
  action: string
  method: string
  inputs: string[]
  hasFileUpload: boolean
}

export interface CookieInfo {
  name: string
  secure: boolean
  httpOnly: boolean
  sameSite?: string
}

export interface HostResult {
  ip: string
  hostname?: string
  status: 'up' | 'down' | 'unknown'
  os?: {
    name: string
    accuracy: number
    cpe: string[]
    fingerprint: string
  }
  ports: PortResult[]
  latency: number
  // Enhanced host information
  geolocation?: {
    country: string
    city: string
    coordinates: [number, number]
    isp: string
    organization: string
  }
  reputation?: {
    malicious: boolean
    score: number
    sources: string[]
  }
  whois?: {
    registrar: string
    registrationDate: string
    expirationDate: string
    nameservers: string[]
  }
  traceroute?: string[]
  uptime?: number
  lastSeen?: Date
}

export interface ScanSession {
  id: string
  target: ScanTarget
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled' | 'paused'
  startTime: Date
  endTime?: Date
  progress: number
  hostsFound: number
  hostsScanned: number
  portsFound: number
  results: HostResult[]
  errors: string[]
  command?: string
  // Enhanced session tracking
  performance: {
    avgScanTime: number
    hostsPerSecond: number
    portsPerSecond: number
    successRate: number
  }
  statistics: {
    totalPorts: number
    openPorts: number
    closedPorts: number
    filteredPorts: number
    vulnerabilitiesFound: number
    highRiskServices: number
  }
  resources: {
    memoryUsage: number
    cpuUsage: number
    networkUsage: number
  }
}

export class ScannerEngine {
  private sessions: Map<string, ScanSession> = new Map()
  private runningProcesses: Map<string, any> = new Map()
  private scanQueue: ScanTarget[] = []
  private maxConcurrentScans: number = 3
  private activeScanCount: number = 0

  // Tool availability
  private toolsAvailable = {
    nmap: false,
    masscan: false,
    zmap: false,
    ncat: false,
    dig: false
  }

  constructor() {
    this.initializeEngine()
  }

  private async initializeEngine() {
    console.log('🔍 Advanced Scanner Engine initialized')
    await this.createTables()
    await this.checkDependencies()
    await this.loadScanQueue()
    this.startPerformanceMonitoring()
  }

  private startPerformanceMonitoring() {
    setInterval(() => {
      this.updatePerformanceMetrics()
    }, 30000) // Update every 30 seconds
  }

  private updatePerformanceMetrics() {
    const memUsage = process.memoryUsage()
    const cpuUsage = process.cpuUsage()

    this.sessions.forEach(session => {
      if (session.status === 'running') {
        session.resources = {
          memoryUsage: memUsage.heapUsed / 1024 / 1024, // MB
          cpuUsage: (cpuUsage.user + cpuUsage.system) / 1000000, // seconds
          networkUsage: session.results.reduce((total, host) =>
            total + host.ports.length, 0)
        }
      }
    })
  }

  private async createTables() {
    try {
      // Enhanced scan_sessions table
      await db.query(`
        CREATE TABLE IF NOT EXISTS scan_sessions (
          id VARCHAR(36) PRIMARY KEY,
          user_id INT,
          target VARCHAR(500) NOT NULL,
          scan_type ENUM('nmap', 'masscan', 'zmap', 'custom', 'shodan', 'comprehensive') NOT NULL,
          config JSON,
          status ENUM('pending', 'running', 'completed', 'failed', 'cancelled', 'paused') DEFAULT 'pending',
          progress INT DEFAULT 0,
          hosts_found INT DEFAULT 0,
          hosts_scanned INT DEFAULT 0,
          ports_found INT DEFAULT 0,
          vulnerabilities_found INT DEFAULT 0,
          performance_metrics JSON,
          statistics JSON,
          resource_usage JSON,
          command_executed TEXT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          completed_at TIMESTAMP NULL,
          INDEX idx_user_id (user_id),
          INDEX idx_status (status),
          INDEX idx_scan_type (scan_type),
          INDEX idx_created_at (created_at)
        )
      `)

      // Enhanced scan_results table
      await db.query(`
        CREATE TABLE IF NOT EXISTS scan_results (
          id VARCHAR(36) PRIMARY KEY,
          session_id VARCHAR(36),
          ip VARCHAR(45) NOT NULL,
          hostname VARCHAR(255),
          status ENUM('up', 'down', 'unknown') DEFAULT 'unknown',
          os_info JSON,
          ports JSON,
          geolocation JSON,
          reputation JSON,
          whois_info JSON,
          traceroute JSON,
          latency INT DEFAULT 0,
          uptime INT DEFAULT 0,
          last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          risk_score INT DEFAULT 0,
          vulnerabilities JSON,
          scanned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (session_id) REFERENCES scan_sessions(id) ON DELETE CASCADE,
          INDEX idx_session_id (session_id),
          INDEX idx_ip (ip),
          INDEX idx_status (status),
          INDEX idx_risk_score (risk_score)
        )
      `)

      // Port scan results table
      await db.query(`
        CREATE TABLE IF NOT EXISTS port_scan_results (
          id VARCHAR(36) PRIMARY KEY,
          scan_result_id VARCHAR(36),
          port INT NOT NULL,
          protocol ENUM('tcp', 'udp') NOT NULL,
          state ENUM('open', 'closed', 'filtered', 'unfiltered', 'open|filtered', 'closed|filtered') NOT NULL,
          service VARCHAR(100),
          version VARCHAR(255),
          banner TEXT,
          confidence INT DEFAULT 0,
          reason VARCHAR(100),
          reason_ttl INT,
          ssl_info JSON,
          http_info JSON,
          vulnerabilities JSON,
          risk_level ENUM('low', 'medium', 'high', 'critical') DEFAULT 'low',
          scripts_output JSON,
          scanned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (scan_result_id) REFERENCES scan_results(id) ON DELETE CASCADE,
          INDEX idx_scan_result_id (scan_result_id),
          INDEX idx_port (port),
          INDEX idx_state (state),
          INDEX idx_risk_level (risk_level)
        )
      `)

      console.log('✅ Enhanced Scanner Engine tables created')
    } catch (error) {
      console.error('❌ Error creating scanner tables:', error)
    }
  }

  private async checkDependencies() {
    console.log('🔧 Checking scanner tool dependencies...')

    // Check nmap
    try {
      await execAsync('nmap --version')
      this.toolsAvailable.nmap = true
      console.log('✅ Nmap available')
    } catch {
      console.log('❌ Nmap not available')
    }

    // Check masscan
    try {
      await execAsync('masscan --version')
      this.toolsAvailable.masscan = true
      console.log('✅ Masscan available')
    } catch {
      console.log('❌ Masscan not available')
    }

    // Check zmap
    try {
      await execAsync('zmap --version')
      this.toolsAvailable.zmap = true
      console.log('✅ Zmap available')
    } catch {
      console.log('❌ Zmap not available')
    }

    // Check ncat
    try {
      await execAsync('ncat --version')
      this.toolsAvailable.ncat = true
      console.log('✅ Ncat available')
    } catch {
      console.log('❌ Ncat not available')
    }

    // Check dig
    try {
      await execAsync('dig -v')
      this.toolsAvailable.dig = true
      console.log('✅ Dig available')
    } catch {
      console.log('❌ Dig not available')
    }

    const availableTools = Object.values(this.toolsAvailable).filter(Boolean).length
    console.log(`📊 ${availableTools}/5 scanning tools available`)
  }

  private async loadScanQueue() {
    try {
      const pendingScans = await db.query(`
        SELECT * FROM scan_sessions
        WHERE status IN ('pending', 'paused')
        ORDER BY created_at ASC
      `)

      for (const scan of pendingScans) {
        const target: ScanTarget = {
          id: scan.id,
          target: scan.target,
          scanType: scan.scan_type,
          ports: JSON.parse(scan.config).ports || '1-1000',
          options: JSON.parse(scan.config).options || {},
          priority: JSON.parse(scan.config).priority || 'normal',
          stealth: JSON.parse(scan.config).stealth || false,
          evasion: JSON.parse(scan.config).evasion || false,
          geolocation: JSON.parse(scan.config).geolocation || false,
          vulnerabilityCheck: JSON.parse(scan.config).vulnerabilityCheck || false,
          serviceEnumeration: JSON.parse(scan.config).serviceEnumeration || false
        }

        this.scanQueue.push(target)
      }

      console.log(`📋 Loaded ${this.scanQueue.length} pending scans`)
    } catch (error) {
      console.error('❌ Error loading scan queue:', error)
    }
  }

  // Main scan method with enhanced capabilities
  async startScan(target: ScanTarget, userId?: number): Promise<string> {
    const sessionId = this.generateId()

    const session: ScanSession = {
      id: sessionId,
      target,
      status: 'pending',
      startTime: new Date(),
      progress: 0,
      hostsFound: 0,
      hostsScanned: 0,
      portsFound: 0,
      results: [],
      errors: [],
      performance: {
        avgScanTime: 0,
        hostsPerSecond: 0,
        portsPerSecond: 0,
        successRate: 0
      },
      statistics: {
        totalPorts: 0,
        openPorts: 0,
        closedPorts: 0,
        filteredPorts: 0,
        vulnerabilitiesFound: 0,
        highRiskServices: 0
      },
      resources: {
        memoryUsage: 0,
        cpuUsage: 0,
        networkUsage: 0
      }
    }

    this.sessions.set(sessionId, session)

    // Save to database
    await db.query(`
      INSERT INTO scan_sessions (id, user_id, target, scan_type, config, status, created_at)
      VALUES (?, ?, ?, ?, ?, 'pending', NOW())
    `, [
      sessionId,
      userId || null,
      target.target,
      target.scanType,
      JSON.stringify(target)
    ])

    // Start scanning based on type
    this.executeScan(sessionId)

    return sessionId
  }

  private async executeScan(sessionId: string) {
    const session = this.sessions.get(sessionId)
    if (!session) return

    try {
      session.status = 'running'
      await this.updateSessionStatus(sessionId, 'running')

      console.log(`🔍 Starting ${session.target.scanType} scan for ${session.target.target}`)

      let results: HostResult[] = []

      switch (session.target.scanType) {
        case 'nmap':
          results = await this.executeNmapScan(session)
          break
        case 'masscan':
          results = await this.executeMasscanScan(session)
          break
        case 'zmap':
          results = await this.executeZmapScan(session)
          break
        case 'shodan':
          results = await this.executeShodanScan(session)
          break
        case 'comprehensive':
          results = await this.executeComprehensiveScan(session)
          break
        default:
          results = await this.executeCustomScan(session)
      }

      session.results = results
      session.status = 'completed'
      session.endTime = new Date()
      session.progress = 100

      // Update statistics
      this.updateSessionStatistics(session)

      // Save results to database
      await this.saveResults(sessionId, results)
      await this.updateSessionStatus(sessionId, 'completed')

      console.log(`✅ Scan ${sessionId} completed: ${results.length} hosts scanned`)

    } catch (error: any) {
      session.status = 'failed'
      session.errors.push(`Scan failed: ${error.message}`)
      await this.updateSessionStatus(sessionId, 'failed')
      console.error(`❌ Scan ${sessionId} failed:`, error)
    }
  }

  // Nmap scan implementation with advanced options
  private async executeNmapScan(session: ScanSession): Promise<HostResult[]> {
    if (!this.toolsAvailable.nmap) {
      throw new Error('Nmap is not available')
    }

    const target = session.target
    const options = target.options

    // Build nmap command
    let command = 'nmap'

    // Basic options
    command += ` -p ${target.ports}`
    command += ` -T${options.timing || 4}`

    // Advanced options
    if (options.serviceDetection) command += ' -sV'
    if (options.osDetection) command += ' -O'
    if (options.scriptScan) command += ' -sC'
    if (options.aggressive) command += ' -A'

    // Stealth and evasion
    if (target.stealth) command += ' -sS'
    if (target.evasion) {
      if (options.fragmentPackets) command += ' -f'
      if (options.decoy && options.decoy.length > 0) {
        command += ` -D ${options.decoy.join(',')}`
      }
      if (options.spoofMac) command += ` --spoof-mac ${options.spoofMac}`
    }

    // Performance tuning
    if (options.maxParallelism) command += ` --max-parallelism ${options.maxParallelism}`
    if (options.minRate) command += ` --min-rate ${options.minRate}`
    if (options.maxRate) command += ` --max-rate ${options.maxRate}`
    if (options.scanDelay) command += ` --scan-delay ${options.scanDelay}ms`

    // Output format
    command += ' -oX -' // XML output to stdout
    command += ` ${target.target}`

    session.command = command
    console.log(`🔍 Executing: ${command}`)

    try {
      const { stdout } = await execAsync(command, {
        timeout: options.timeout || 300000, // 5 minutes default
        maxBuffer: 1024 * 1024 * 10 // 10MB buffer
      })

      // Parse nmap XML output
      const results = await this.parseNmapOutput(stdout, session)

      // Enhance results with additional information
      if (target.geolocation || target.vulnerabilityCheck) {
        await this.enhanceResults(results, target)
      }

      return results

    } catch (error: any) {
      throw new Error(`Nmap scan failed: ${error.message}`)
    }
  }

  // Masscan implementation for high-speed scanning
  private async executeMasscanScan(session: ScanSession): Promise<HostResult[]> {
    if (!this.toolsAvailable.masscan) {
      throw new Error('Masscan is not available')
    }

    const target = session.target
    const options = target.options

    // Build masscan command
    let command = 'masscan'
    command += ` -p ${target.ports}`
    command += ` --rate ${options.maxRate || 1000}`
    command += ' -oJ -' // JSON output to stdout
    command += ` ${target.target}`

    session.command = command
    console.log(`🚀 Executing: ${command}`)

    try {
      const { stdout } = await execAsync(command, {
        timeout: options.timeout || 300000,
        maxBuffer: 1024 * 1024 * 10
      })

      // Parse masscan JSON output
      const results = await this.parseMasscanOutput(stdout, session)

      // Enhance with service detection if requested
      if (target.serviceEnumeration) {
        await this.enhanceWithServiceDetection(results, target)
      }

      return results

    } catch (error: any) {
      throw new Error(`Masscan scan failed: ${error.message}`)
    }
  }

  // Comprehensive scan combining multiple tools
  private async executeComprehensiveScan(session: ScanSession): Promise<HostResult[]> {
    const results: HostResult[] = []

    try {
      // Phase 1: Fast port discovery with masscan
      if (this.toolsAvailable.masscan) {
        console.log('🚀 Phase 1: Fast port discovery with Masscan')
        const masscanResults = await this.executeMasscanScan(session)
        results.push(...masscanResults)
      }

      // Phase 2: Detailed service detection with nmap
      if (this.toolsAvailable.nmap && results.length > 0) {
        console.log('🔍 Phase 2: Service detection with Nmap')
        const openPorts = this.extractOpenPorts(results)
        if (openPorts.length > 0) {
          const nmapResults = await this.executeTargetedNmapScan(openPorts, session)
          this.mergeResults(results, nmapResults)
        }
      }

      // Phase 3: Vulnerability assessment
      if (session.target.vulnerabilityCheck) {
        console.log('🛡️ Phase 3: Vulnerability assessment')
        await this.performVulnerabilityAssessment(results)
      }

      // Phase 4: Geolocation and reputation check
      if (session.target.geolocation) {
        console.log('🌍 Phase 4: Geolocation and reputation analysis')
        await this.enhanceResults(results, session.target)
      }

      return results

    } catch (error: any) {
      throw new Error(`Comprehensive scan failed: ${error.message}`)
    }
  }
          ports_found INT DEFAULT 0,
          results JSON,
          errors JSON,
          command TEXT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          completed_at TIMESTAMP NULL
        )
      `)

      await db.query(`
        CREATE TABLE IF NOT EXISTS scan_results (
          id VARCHAR(36) PRIMARY KEY,
          session_id VARCHAR(36),
          ip VARCHAR(45) NOT NULL,
          hostname VARCHAR(255),
          status ENUM('up', 'down', 'unknown') DEFAULT 'unknown',
          os_name VARCHAR(255),
          os_accuracy INT,
          ports JSON,
          latency DECIMAL(8,3),
          geolocation JSON,
          last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (session_id) REFERENCES scan_sessions(id) ON DELETE CASCADE,
          INDEX idx_ip (ip),
          INDEX idx_session (session_id)
        )
      `)

      console.log('✅ Scanner Engine tables created')
    } catch (error) {
      console.error('❌ Error creating scanner tables:', error)
    }
  }

  private async checkDependencies() {
    const tools = ['nmap', 'masscan']
    
    for (const tool of tools) {
      try {
        await execAsync(`${tool} --version`)
        console.log(`✅ ${tool} is available`)
      } catch (error) {
        console.log(`⚠️ ${tool} is not installed or not in PATH`)
      }
    }
  }

  async startScan(target: ScanTarget, userId?: number): Promise<string> {
    const sessionId = this.generateId()
    
    const session: ScanSession = {
      id: sessionId,
      target,
      status: 'pending',
      startTime: new Date(),
      progress: 0,
      hostsFound: 0,
      hostsScanned: 0,
      portsFound: 0,
      results: [],
      errors: []
    }

    this.sessions.set(sessionId, session)

    // Save to database
    await db.query(`
      INSERT INTO scan_sessions (id, user_id, target, scan_type, config, status, created_at)
      VALUES (?, ?, ?, ?, ?, 'pending', NOW())
    `, [sessionId, userId || null, target.target, target.scanType, JSON.stringify(target)])

    // Start scanning
    this.executeScan(sessionId)

    return sessionId
  }

  private async executeScan(sessionId: string) {
    const session = this.sessions.get(sessionId)
    if (!session) return

    try {
      session.status = 'running'
      await this.updateSessionStatus(sessionId, 'running')

      console.log(`🔍 Starting ${session.target.scanType} scan for ${session.target.target}`)

      let results: HostResult[] = []

      switch (session.target.scanType) {
        case 'nmap':
          results = await this.executeNmapScan(session)
          break
        case 'masscan':
          results = await this.executeMasscanScan(session)
          break
        case 'zmap':
          results = await this.executeZmapScan(session)
          break
        default:
          throw new Error(`Unsupported scan type: ${session.target.scanType}`)
      }

      session.results = results
      session.hostsFound = results.length
      session.hostsScanned = results.filter(h => h.status === 'up').length
      session.portsFound = results.reduce((total, host) => total + host.ports.length, 0)
      session.status = 'completed'
      session.endTime = new Date()
      session.progress = 100

      // Save results to database
      await this.saveResults(sessionId, results)
      await this.updateSessionStatus(sessionId, 'completed')

      console.log(`🎉 Scan ${sessionId} completed: ${session.hostsFound} hosts, ${session.portsFound} ports`)

    } catch (error) {
      session.status = 'failed'
      session.errors.push(`Scan failed: ${error}`)
      await this.updateSessionStatus(sessionId, 'failed')
      console.error(`❌ Scan ${sessionId} failed:`, error)
    }
  }

  private async executeNmapScan(session: ScanSession): Promise<HostResult[]> {
    const { target, ports, options } = session.target
    
    // Build nmap command
    let command = ['nmap']
    
    // Add timing
    command.push(`-T${options.timing}`)
    
    // Add scan options
    if (options.serviceDetection) command.push('-sV')
    if (options.osDetection) command.push('-O')
    if (options.scriptScan) command.push('-sC')
    if (options.aggressive) command.push('-A')
    
    // Add port specification
    if (ports && ports !== 'all') {
      command.push('-p', ports)
    }
    
    // Add output format
    command.push('-oX', '-') // XML output to stdout
    
    // Add target
    command.push(target)
    
    session.command = command.join(' ')

    return new Promise((resolve, reject) => {
      const process = spawn('nmap', command.slice(1))
      let xmlOutput = ''
      let errorOutput = ''

      this.runningProcesses.set(session.id, process)

      process.stdout.on('data', (data) => {
        xmlOutput += data.toString()
      })

      process.stderr.on('data', (data) => {
        errorOutput += data.toString()
        console.log(`Nmap stderr: ${data}`)
      })

      process.on('close', (code) => {
        this.runningProcesses.delete(session.id)
        
        if (code === 0) {
          try {
            const results = this.parseNmapXML(xmlOutput)
            resolve(results)
          } catch (error) {
            reject(new Error(`Failed to parse nmap output: ${error}`))
          }
        } else {
          reject(new Error(`Nmap exited with code ${code}: ${errorOutput}`))
        }
      })

      process.on('error', (error) => {
        this.runningProcesses.delete(session.id)
        reject(new Error(`Failed to start nmap: ${error}`))
      })
    })
  }

  private async executeMasscanScan(session: ScanSession): Promise<HostResult[]> {
    const { target, ports, options } = session.target
    
    // Build masscan command
    let command = ['masscan']
    
    // Add rate limiting
    command.push('--rate', '1000')
    
    // Add port specification
    if (ports && ports !== 'all') {
      command.push('-p', ports)
    } else {
      command.push('-p', '1-65535')
    }
    
    // Add output format
    command.push('-oJ', '-') // JSON output to stdout
    
    // Add target
    command.push(target)
    
    session.command = command.join(' ')

    return new Promise((resolve, reject) => {
      const process = spawn('masscan', command.slice(1))
      let jsonOutput = ''
      let errorOutput = ''

      this.runningProcesses.set(session.id, process)

      process.stdout.on('data', (data) => {
        jsonOutput += data.toString()
      })

      process.stderr.on('data', (data) => {
        errorOutput += data.toString()
      })

      process.on('close', (code) => {
        this.runningProcesses.delete(session.id)
        
        if (code === 0) {
          try {
            const results = this.parseMasscanJSON(jsonOutput)
            resolve(results)
          } catch (error) {
            reject(new Error(`Failed to parse masscan output: ${error}`))
          }
        } else {
          reject(new Error(`Masscan exited with code ${code}: ${errorOutput}`))
        }
      })

      process.on('error', (error) => {
        this.runningProcesses.delete(session.id)
        reject(new Error(`Failed to start masscan: ${error}`))
      })
    })
  }

  private async executeZmapScan(session: ScanSession): Promise<HostResult[]> {
    // ZMap implementation for single port scanning
    const { target, ports } = session.target
    const port = ports.split(',')[0] || '80' // Use first port for zmap
    
    let command = ['zmap', '-p', port, target]
    session.command = command.join(' ')

    return new Promise((resolve, reject) => {
      const process = spawn('zmap', command.slice(1))
      let output = ''
      let errorOutput = ''

      this.runningProcesses.set(session.id, process)

      process.stdout.on('data', (data) => {
        output += data.toString()
      })

      process.stderr.on('data', (data) => {
        errorOutput += data.toString()
      })

      process.on('close', (code) => {
        this.runningProcesses.delete(session.id)
        
        if (code === 0) {
          const results = this.parseZmapOutput(output, parseInt(port))
          resolve(results)
        } else {
          reject(new Error(`ZMap exited with code ${code}: ${errorOutput}`))
        }
      })

      process.on('error', (error) => {
        this.runningProcesses.delete(session.id)
        reject(new Error(`Failed to start zmap: ${error}`))
      })
    })
  }

  private parseNmapXML(xmlOutput: string): HostResult[] {
    // Basic XML parsing for nmap output
    // In production, use a proper XML parser like xml2js
    const results: HostResult[] = []
    
    // This is a simplified parser - implement proper XML parsing
    const hostRegex = /<host[^>]*>[\s\S]*?<\/host>/g
    const hosts = xmlOutput.match(hostRegex) || []
    
    for (const hostXml of hosts) {
      const ipMatch = hostXml.match(/<address addr="([^"]+)" addrtype="ipv4"/)
      const hostnameMatch = hostXml.match(/<hostname name="([^"]+)"/)
      const stateMatch = hostXml.match(/<status state="([^"]+)"/)
      
      if (ipMatch) {
        const host: HostResult = {
          ip: ipMatch[1],
          hostname: hostnameMatch?.[1],
          status: stateMatch?.[1] === 'up' ? 'up' : 'down',
          ports: [],
          latency: 0,
          lastSeen: new Date()
        }
        
        // Parse ports (simplified)
        const portRegex = /<port protocol="([^"]+)" portid="([^"]+)"[^>]*>[\s\S]*?<\/port>/g
        let portMatch
        while ((portMatch = portRegex.exec(hostXml)) !== null) {
          const stateMatch = portMatch[0].match(/<state state="([^"]+)"/)
          const serviceMatch = portMatch[0].match(/<service name="([^"]+)"/)
          
          host.ports.push({
            port: parseInt(portMatch[2]),
            protocol: portMatch[1] as 'tcp' | 'udp',
            state: stateMatch?.[1] as any || 'unknown',
            service: serviceMatch?.[1]
          })
        }
        
        results.push(host)
      }
    }
    
    return results
  }

  private parseMasscanJSON(jsonOutput: string): HostResult[] {
    const results: HostResult[] = []
    const lines = jsonOutput.trim().split('\n')
    
    for (const line of lines) {
      if (line.trim() && line !== '}') {
        try {
          const data = JSON.parse(line.replace(/,$/, ''))
          
          if (data.ip && data.ports) {
            const host: HostResult = {
              ip: data.ip,
              status: 'up',
              ports: data.ports.map((p: any) => ({
                port: p.port,
                protocol: p.proto,
                state: 'open'
              })),
              latency: 0,
              lastSeen: new Date()
            }
            
            results.push(host)
          }
        } catch (error) {
          // Skip invalid JSON lines
        }
      }
    }
    
    return results
  }

  private parseZmapOutput(output: string, port: number): HostResult[] {
    const results: HostResult[] = []
    const ips = output.trim().split('\n').filter(ip => ip.trim())
    
    for (const ip of ips) {
      if (ip.match(/^\d+\.\d+\.\d+\.\d+$/)) {
        results.push({
          ip: ip.trim(),
          status: 'up',
          ports: [{
            port,
            protocol: 'tcp',
            state: 'open'
          }],
          latency: 0,
          lastSeen: new Date()
        })
      }
    }
    
    return results
  }

  private async saveResults(sessionId: string, results: HostResult[]) {
    for (const host of results) {
      try {
        await db.query(`
          INSERT INTO scan_results (id, session_id, ip, hostname, status, ports, latency, last_seen)
          VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
        `, [
          this.generateId(),
          sessionId,
          host.ip,
          host.hostname || null,
          host.status,
          JSON.stringify(host.ports),
          host.latency
        ])
      } catch (error) {
        console.error('Error saving scan result:', error)
      }
    }
  }

  private async updateSessionStatus(sessionId: string, status: string) {
    try {
      const session = this.sessions.get(sessionId)
      await db.query(`
        UPDATE scan_sessions 
        SET status = ?, progress = ?, hosts_found = ?, hosts_scanned = ?, ports_found = ?, 
            updated_at = NOW(), completed_at = ${status === 'completed' ? 'NOW()' : 'NULL'}
        WHERE id = ?
      `, [
        status, 
        session?.progress || 0,
        session?.hostsFound || 0,
        session?.hostsScanned || 0,
        session?.portsFound || 0,
        sessionId
      ])
    } catch (error) {
      console.error('Error updating session status:', error)
    }
  }

  async getSession(sessionId: string): Promise<ScanSession | null> {
    return this.sessions.get(sessionId) || null
  }

  async cancelScan(sessionId: string): Promise<boolean> {
    const process = this.runningProcesses.get(sessionId)
    if (process) {
      process.kill('SIGTERM')
      this.runningProcesses.delete(sessionId)
      
      const session = this.sessions.get(sessionId)
      if (session) {
        session.status = 'cancelled'
        await this.updateSessionStatus(sessionId, 'cancelled')
      }
      
      return true
    }
    return false
  }

  // Enhanced geolocation lookup
  async enrichWithGeolocation(ip: string): Promise<any> {
    try {
      const response = await axios.get(`http://ip-api.com/json/${ip}`)
      return {
        country: response.data.country,
        city: response.data.city,
        latitude: response.data.lat,
        longitude: response.data.lon,
        isp: response.data.isp
      }
    } catch (error) {
      return null
    }
  }

  private generateId(): string {
    return Math.random().toString(36).substring(2) + Date.now().toString(36)
  }
}

// Export singleton instance
export const scannerEngine = new ScannerEngine()
