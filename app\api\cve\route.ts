import { NextRequest, NextResponse } from 'next/server'
import jwt from 'jsonwebtoken'
import { CVEService } from '@/lib/services/cve'

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-here-make-it-very-long-and-secure-for-production'

function getUserFromToken(request: NextRequest) {
  try {
    const cookies = request.headers.get('cookie')
    if (!cookies) return null

    const tokenMatch = cookies.match(/accessToken=([^;]+)/)
    const token = tokenMatch ? tokenMatch[1] : null

    if (!token) return null

    const decoded = jwt.verify(token, JWT_SECRET) as any
    return decoded
  } catch (error) {
    console.error('Token verification error:', error)
    return null
  }
}

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 CVE API: GET request received')

    const user = getUserFromToken(request)
    if (!user) {
      console.log('❌ CVE API: No valid token')
      return NextResponse.json({ success: false, error: 'Unauthorized' }, { status: 401 })
    }

    console.log('✅ CVE API: User authenticated:', user.username)

    const { searchParams } = new URL(request.url)
    const search = searchParams.get('search') || ''
    const severity = searchParams.get('severity') || ''
    const limit = parseInt(searchParams.get('limit') || '10')
    const offset = parseInt(searchParams.get('offset') || '0')

    // Initialize CVE service
    const cveService = new CVEService()

    // Perform search
    const searchResult = await cveService.search({
      query: search,
      severity: severity as any,
      page: Math.floor(offset / limit) + 1,
      limit
    })

    console.log('✅ CVE API: Data retrieved successfully')

    return NextResponse.json({
      success: true,
      data: {
        cves: searchResult.data,
        pagination: {
          total: searchResult.total,
          limit,
          offset,
          hasMore: offset + limit < searchResult.total
        }
      }
    })

  } catch (error) {
    console.error('❌ CVE API Error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    console.log('🔍 CVE API: POST request received')

    const user = getUserFromToken(request)
    if (!user) {
      console.log('❌ CVE API: No valid token')
      return NextResponse.json({ success: false, error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { cveId } = body

    console.log('🔍 CVE API: Looking up CVE:', cveId)

    // Initialize CVE service
    const cveService = new CVEService()
    const cve = await cveService.getById(cveId)

    if (!cve) {
      console.log('❌ CVE API: CVE not found')
      return NextResponse.json({ success: false, error: 'CVE not found' }, { status: 404 })
    }

    console.log('✅ CVE API: CVE found successfully')

    return NextResponse.json({
      success: true,
      data: cve
    })

  } catch (error) {
    console.error('❌ CVE API Error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
