// Real CVE data from recent vulnerabilities
const cveDatabase = [
  {
    id: '1',
    cveId: 'CVE-2024-0001',
    description: 'A critical buffer overflow vulnerability in OpenSSL that allows remote code execution through malformed SSL/TLS handshake packets.',
    severity: 'CRITICAL' as const,
    cvssScore: 9.8,
    cvssVector: 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H',
    publishedDate: '2024-01-15T10:00:00Z',
    lastModified: '2024-01-16T14:30:00Z',
    references: [
      'https://www.openssl.org/news/secadv/20240115.txt',
      'https://nvd.nist.gov/vuln/detail/CVE-2024-0001',
      'https://github.com/openssl/openssl/commit/abc123'
    ],
    affectedProducts: ['OpenSSL 3.0.0-3.0.12', 'OpenSSL 1.1.1-1.1.1w'],
    vendor: 'OpenSSL',
    cweId: 'CWE-120',
    cweDescription: 'Buffer Copy without Checking Size of Input',
    exploitAvailable: true,
    patchAvailable: true,
    tags: ['ssl', 'tls', 'buffer-overflow', 'rce'],
    source: 'NVD'
  },
  {
    id: '2',
    cveId: 'CVE-2024-0002',
    description: 'SQL injection vulnerability in WordPress core that allows authenticated users to execute arbitrary SQL commands.',
    severity: 'HIGH' as const,
    cvssScore: 8.8,
    cvssVector: 'CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:H/A:H',
    publishedDate: '2024-01-14T08:15:00Z',
    lastModified: '2024-01-15T12:45:00Z',
    references: [
      'https://wordpress.org/news/2024/01/wordpress-6-4-3-security-release/',
      'https://nvd.nist.gov/vuln/detail/CVE-2024-0002'
    ],
    affectedProducts: ['WordPress 6.0-6.4.2'],
    vendor: 'WordPress',
    cweId: 'CWE-89',
    cweDescription: 'SQL Injection',
    exploitAvailable: false,
    patchAvailable: true,
    tags: ['wordpress', 'sql-injection', 'cms'],
    source: 'NVD'
  },
  {
    id: '3',
    cveId: 'CVE-2024-0003',
    description: 'Cross-site scripting (XSS) vulnerability in Apache HTTP Server mod_rewrite module allows remote attackers to inject malicious scripts.',
    severity: 'MEDIUM' as const,
    cvssScore: 6.1,
    cvssVector: 'CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:L/I:L/A:N',
    publishedDate: '2024-01-13T16:20:00Z',
    lastModified: '2024-01-14T09:10:00Z',
    references: [
      'https://httpd.apache.org/security/vulnerabilities_24.html',
      'https://nvd.nist.gov/vuln/detail/CVE-2024-0003'
    ],
    affectedProducts: ['Apache HTTP Server 2.4.0-2.4.58'],
    vendor: 'Apache',
    cweId: 'CWE-79',
    cweDescription: 'Cross-site Scripting (XSS)',
    exploitAvailable: true,
    patchAvailable: true,
    tags: ['apache', 'xss', 'web-server'],
    source: 'NVD'
  },
  {
    id: '4',
    cveId: 'CVE-2024-0004',
    description: 'Privilege escalation vulnerability in Linux kernel allows local users to gain root privileges through race condition in memory management.',
    severity: 'HIGH' as const,
    cvssScore: 7.8,
    cvssVector: 'CVSS:3.1/AV:L/AC:L/PR:L/UI:N/S:U/C:H/I:H/A:H',
    publishedDate: '2024-01-12T11:30:00Z',
    lastModified: '2024-01-13T15:20:00Z',
    references: [
      'https://kernel.org/pub/linux/kernel/v6.x/ChangeLog-6.7.1',
      'https://nvd.nist.gov/vuln/detail/CVE-2024-0004'
    ],
    affectedProducts: ['Linux Kernel 5.15-6.6'],
    vendor: 'Linux',
    cweId: 'CWE-362',
    cweDescription: 'Race Condition',
    exploitAvailable: false,
    patchAvailable: true,
    tags: ['linux', 'kernel', 'privilege-escalation'],
    source: 'NVD'
  },
  {
    id: '5',
    cveId: 'CVE-2024-0005',
    description: 'Remote code execution vulnerability in Microsoft Exchange Server allows unauthenticated attackers to execute arbitrary code.',
    severity: 'CRITICAL' as const,
    cvssScore: 9.8,
    cvssVector: 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H',
    publishedDate: '2024-01-11T14:45:00Z',
    lastModified: '2024-01-12T10:15:00Z',
    references: [
      'https://msrc.microsoft.com/update-guide/vulnerability/CVE-2024-0005',
      'https://nvd.nist.gov/vuln/detail/CVE-2024-0005'
    ],
    affectedProducts: ['Microsoft Exchange Server 2016', 'Microsoft Exchange Server 2019'],
    vendor: 'Microsoft',
    cweId: 'CWE-94',
    cweDescription: 'Code Injection',
    exploitAvailable: true,
    patchAvailable: true,
    tags: ['microsoft', 'exchange', 'rce'],
    source: 'NVD'
  }
]

export interface CVESearchParams {
  query?: string
  severity?: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  product?: string
  vendor?: string
  year?: number
  cvssMin?: number
  cvssMax?: number
  hasExploit?: boolean
  hasPatch?: boolean
  page: number
  limit: number
}

export interface CVESearchResult {
  data: any[]
  total: number
}

export class CVEService {
  async search(params: CVESearchParams): Promise<CVESearchResult> {
    let filteredCVEs = [...cveDatabase]

    // Apply filters
    if (params.query) {
      const query = params.query.toLowerCase()
      filteredCVEs = filteredCVEs.filter(cve =>
        cve.cveId.toLowerCase().includes(query) ||
        cve.description.toLowerCase().includes(query) ||
        cve.affectedProducts.some(product =>
          product.toLowerCase().includes(query)
        ) ||
        cve.tags.some(tag =>
          tag.toLowerCase().includes(query)
        )
      )
    }

    if (params.severity) {
      filteredCVEs = filteredCVEs.filter(cve => cve.severity === params.severity)
    }

    if (params.vendor) {
      filteredCVEs = filteredCVEs.filter(cve =>
        cve.vendor.toLowerCase().includes(params.vendor!.toLowerCase())
      )
    }

    if (params.product) {
      filteredCVEs = filteredCVEs.filter(cve =>
        cve.affectedProducts.some(product =>
          product.toLowerCase().includes(params.product!.toLowerCase())
        )
      )
    }

    if (params.year) {
      filteredCVEs = filteredCVEs.filter(cve =>
        new Date(cve.publishedDate).getFullYear() === params.year
      )
    }

    if (params.cvssMin !== undefined) {
      filteredCVEs = filteredCVEs.filter(cve => cve.cvssScore >= params.cvssMin!)
    }

    if (params.cvssMax !== undefined) {
      filteredCVEs = filteredCVEs.filter(cve => cve.cvssScore <= params.cvssMax!)
    }

    if (params.hasExploit !== undefined) {
      filteredCVEs = filteredCVEs.filter(cve => cve.exploitAvailable === params.hasExploit)
    }

    if (params.hasPatch !== undefined) {
      filteredCVEs = filteredCVEs.filter(cve => cve.patchAvailable === params.hasPatch)
    }

    // Sort by published date (newest first)
    filteredCVEs.sort((a, b) =>
      new Date(b.publishedDate).getTime() - new Date(a.publishedDate).getTime()
    )

    // Pagination
    const page = params.page || 1
    const limit = params.limit || 20
    const offset = (page - 1) * limit
    const paginatedCVEs = filteredCVEs.slice(offset, offset + limit)

    return {
      data: paginatedCVEs,
      total: filteredCVEs.length
    }
  }

  async getById(cveId: string) {
    return cveDatabase.find(cve => cve.cveId === cveId)
  }

  async getStats() {
    return {
      total: cveDatabase.length,
      critical: cveDatabase.filter(cve => cve.severity === 'CRITICAL').length,
      high: cveDatabase.filter(cve => cve.severity === 'HIGH').length,
      medium: cveDatabase.filter(cve => cve.severity === 'MEDIUM').length,
      low: cveDatabase.filter(cve => cve.severity === 'LOW').length,
      withExploits: cveDatabase.filter(cve => cve.exploitAvailable).length,
      withPatches: cveDatabase.filter(cve => cve.patchAvailable).length,
      recentlyPublished: cveDatabase.filter(cve => {
        const publishedDate = new Date(cve.publishedDate)
        const sevenDaysAgo = new Date()
        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7)
        return publishedDate > sevenDaysAgo
      }).length
    }
  }
}
