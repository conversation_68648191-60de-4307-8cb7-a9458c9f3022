'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/hooks/auth'
import { 
  Shield, 
  Search, 
  FileText, 
  Database, 
  Globe, 
  Zap, 
  Trophy, 
  Users, 
  TrendingUp, 
  Activity, 
  Star, 
  Award, 
  Target, 
  Flame, 
  Crown,
  ArrowRight,
  Plus,
  Eye,
  Calendar,
  Clock,
  CheckCircle,
  AlertTriangle,
  Info,
  Sparkles,
  Rocket,
  Lightning,
  Gamepad2
} from 'lucide-react'

interface User {
  id: string
  username: string
  email: string
  role: 'user' | 'admin' | 'super_admin' | 'moderator'
  plan: 'Free' | 'Student' | 'Hobby' | 'Bughunter' | 'Cybersecurity'
  level: number
  score: number
  full_name?: string
}

interface DashboardStats {
  level: number
  score: number
  rank: number
  streak: number
  scansCompleted: number
  vulnerabilitiesFound: number
  osintQueries: number
  filesAnalyzed: number
}

interface QuickAction {
  title: string
  description: string
  icon: any
  href: string
  badge?: string
  premium?: boolean
  color: string
  stats: string
}

interface UserDashboardProps {
  user: User | null
}

export default function UserDashboard({ user }: UserDashboardProps) {
  const router = useRouter()
  const { getToken } = useAuth()
  const [stats, setStats] = useState<DashboardStats>({
    level: 1,
    score: 0,
    rank: 999,
    streak: 0,
    scansCompleted: 0,
    vulnerabilitiesFound: 0,
    osintQueries: 0,
    filesAnalyzed: 0
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadDashboardStats()
  }, [])

  const loadDashboardStats = async () => {
    try {
      setLoading(true)
      console.log('🔄 Loading dashboard stats...')

      const token = getToken()
      if (!token) {
        console.error('❌ No token available')
        router.push('/login')
        return
      }

      const response = await fetch('/api/dashboard/stats', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (response.ok) {
        const data = await response.json()
        setStats(data)
        console.log('✅ Dashboard stats loaded:', data)
      } else {
        console.error('❌ Failed to load dashboard stats')
      }
    } catch (error) {
      console.error('❌ Error loading dashboard stats:', error)
    } finally {
      setLoading(false)
    }
  }

  // Base actions for all users
  const baseActions: QuickAction[] = [
    {
      title: 'OSINT Lookup',
      description: 'Investigate emails, phone numbers, and personal information',
      icon: Search,
      href: '/osint',
      badge: 'Popular',
      color: 'cyber-primary',
      stats: '456 queries'
    },
    {
      title: 'Vulnerability Scan',
      description: 'Scan websites and applications for security vulnerabilities',
      icon: Shield,
      href: '/scanner',
      premium: true,
      color: 'cyber-secondary',
      stats: '142 scans'
    },
    {
      title: 'File Analysis',
      description: 'Analyze files for malware, webshells, and suspicious content',
      icon: FileText,
      href: '/file-analyzer',
      premium: true,
      color: 'cyber-accent',
      stats: '89 files'
    },
    {
      title: 'CVE Database',
      description: 'Search and explore the latest CVE vulnerabilities',
      icon: Database,
      href: '/cve',
      color: 'green-400',
      stats: '50K+ CVEs'
    },
    {
      title: 'Google Dorking',
      description: 'Use advanced search queries to find sensitive information',
      icon: Globe,
      href: '/dorking',
      color: 'blue-400',
      stats: 'Advanced'
    },
    {
      title: 'API Playground',
      description: 'Test and explore our API endpoints',
      icon: Zap,
      href: '/playground',
      color: 'purple-400',
      stats: '2.8K calls'
    }
  ]

  // Admin actions for admin and super admin
  const adminActions: QuickAction[] = [
    {
      title: 'User Management',
      description: 'Manage users, roles, and permissions',
      icon: Users,
      href: '/dashboard?section=users',
      badge: 'Admin',
      color: 'yellow-400',
      stats: 'Management'
    },
    {
      title: 'System Analytics',
      description: 'View platform analytics and reports',
      icon: TrendingUp,
      href: '/dashboard?section=analytics',
      badge: 'Admin',
      color: 'green-400',
      stats: 'Reports'
    }
  ]

  // Super admin actions for super admin only
  const superAdminActions: QuickAction[] = [
    {
      title: 'Website Settings',
      description: 'Configure website settings and parameters',
      icon: Crown,
      href: '/dashboard?section=website-settings',
      badge: 'Super Admin',
      color: 'red-400',
      stats: 'Full Control'
    },
    {
      title: 'Payment System',
      description: 'Manage payments and subscriptions',
      icon: TrendingUp,
      href: '/dashboard?section=payments',
      badge: 'Super Admin',
      color: 'green-400',
      stats: 'Revenue'
    }
  ]

  // Combine actions based on user role
  const quickActions: QuickAction[] = [
    ...baseActions,
    ...(user?.role === 'admin' || user?.role === 'super_admin' ? adminActions : []),
    ...(user?.role === 'super_admin' ? superAdminActions : [])
  ]

  const achievements = [
    { id: 1, title: 'First Scan', description: 'Completed your first vulnerability scan', icon: Shield, earned: true },
    { id: 2, title: 'OSINT Expert', description: 'Performed 100 OSINT queries', icon: Search, earned: true },
    { id: 3, title: 'Vulnerability Hunter', description: 'Found 10 vulnerabilities', icon: Target, earned: false },
    { id: 4, title: 'Streak Master', description: 'Maintained a 7-day streak', icon: Flame, earned: false }
  ]

  const recentActivity = [
    { id: 1, type: 'scan', description: 'Completed vulnerability scan on example.com', time: '2 hours ago', severity: 'medium' },
    { id: 2, type: 'osint', description: 'OSINT <NAME_EMAIL>', time: '4 hours ago', severity: 'low' },
    { id: 3, type: 'file', description: 'Analyzed suspicious.exe file', time: '1 day ago', severity: 'high' },
    { id: 4, type: 'cve', description: 'Searched CVE-2024-0001', time: '2 days ago', severity: 'info' }
  ]

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-2 border-cyber-primary border-t-transparent mx-auto mb-4"></div>
          <p className="text-gray-300">Loading dashboard data...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Welcome Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
        <div>
          <h1 className="text-2xl md:text-3xl lg:text-4xl font-bold mb-2">
            <span className="text-cyber-glow">🛡️ Welcome back,</span>{' '}
            <span className="text-cyber-pink">{user?.username || 'User'}</span>
          </h1>
          <p className="text-gray-300 text-base md:text-lg">
            Your personal cybersecurity command center
          </p>
          <div className="flex flex-wrap items-center gap-3 md:gap-6 mt-3">
            <div className="flex items-center space-x-2">
              <Star className="h-5 w-5 text-cyber-primary" />
              <span className="text-cyber-primary font-medium">Level {stats.level}</span>
            </div>
            <div className="flex items-center space-x-2">
              <Trophy className="h-5 w-5 text-yellow-400" />
              <span className="text-yellow-400 font-medium">{stats.score.toLocaleString()} points</span>
            </div>
            <div className="flex items-center space-x-2">
              <Award className="h-5 w-5 text-purple-400" />
              <span className="text-purple-400 font-medium">Rank #{stats.rank}</span>
            </div>
            <div className="flex items-center space-x-2">
              <Flame className="h-4 w-4 text-cyber-secondary" />
              <span className="text-cyber-secondary font-medium">{stats.streak} day streak</span>
            </div>
            {/* Admin/Super Admin Role Badge */}
            {(user?.role === 'admin' || user?.role === 'super_admin') && (
              <>
                <span>•</span>
                <span className={`px-2 py-1 rounded text-xs font-medium ${
                  user?.role === 'super_admin' ? 'bg-red-500/20 text-red-400' : 'bg-yellow-500/20 text-yellow-400'
                }`}>
                  {user?.role === 'super_admin' ? '👑 SUPER ADMIN' : '🛡️ ADMIN'}
                </span>
              </>
            )}
          </div>
        </div>

        <div className="mt-6 lg:mt-0">
          <div className="flex items-center space-x-4">
            <div className="text-right">
              <div className="text-sm text-gray-400">Current Plan</div>
              <div className="text-lg font-semibold text-cyber-primary">
                {user?.plan || 'Free'}
              </div>
            </div>
            <button 
              onClick={() => router.push('/plan')}
              className="btn-cyber-primary"
            >
              <Rocket className="h-4 w-4 mr-2" />
              Upgrade Plan
            </button>
          </div>
        </div>
      </div>

      {/* Quick Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="card-cyber">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Scans Completed</p>
              <p className="text-2xl font-bold text-white">{stats.scansCompleted}</p>
            </div>
            <Shield className="h-8 w-8 text-cyber-primary" />
          </div>
        </div>

        <div className="card-cyber">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Vulnerabilities Found</p>
              <p className="text-2xl font-bold text-white">{stats.vulnerabilitiesFound}</p>
            </div>
            <Target className="h-8 w-8 text-red-400" />
          </div>
        </div>

        <div className="card-cyber">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">OSINT Queries</p>
              <p className="text-2xl font-bold text-white">{stats.osintQueries}</p>
            </div>
            <Search className="h-8 w-8 text-blue-400" />
          </div>
        </div>

        <div className="card-cyber">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Files Analyzed</p>
              <p className="text-2xl font-bold text-white">{stats.filesAnalyzed}</p>
            </div>
            <FileText className="h-8 w-8 text-green-400" />
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div>
        <h2 className="text-xl md:text-2xl font-bold text-white mb-4 md:mb-6">Quick Actions</h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
          {quickActions.map((action, index) => {
            const Icon = action.icon
            return (
              <div
                key={index}
                onClick={() => router.push(action.href)}
                className="card-cyber hover:border-cyber-primary transition-all duration-300 cursor-pointer group"
              >
                <div className="flex items-start justify-between mb-4">
                  <div className={`p-3 rounded-lg bg-${action.color}/20`}>
                    <Icon className={`h-6 w-6 text-${action.color}`} />
                  </div>
                  <div className="flex items-center space-x-2">
                    {action.badge && (
                      <span className="px-2 py-1 text-xs bg-cyber-primary/20 text-cyber-primary rounded-full">
                        {action.badge}
                      </span>
                    )}
                    {action.premium && user?.plan === 'Free' && (
                      <span className="px-2 py-1 text-xs bg-yellow-500/20 text-yellow-400 rounded-full">
                        PRO
                      </span>
                    )}
                    <ArrowRight className="h-5 w-5 text-gray-400 group-hover:text-cyber-primary transition-colors" />
                  </div>
                </div>
                
                <h3 className="text-lg font-semibold text-white mb-2">{action.title}</h3>
                <p className="text-gray-400 text-sm mb-3">{action.description}</p>
                
                <div className="flex items-center justify-between">
                  <span className="text-xs text-gray-500">{action.stats}</span>
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                </div>
              </div>
            )
          })}
        </div>
      </div>
    </div>
  )
}
