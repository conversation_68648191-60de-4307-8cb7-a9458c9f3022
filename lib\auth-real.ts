import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'
import { db } from './database'
import { NextRequest } from 'next/server'

export interface User {
  id: number
  username: string
  email: string
  full_name?: string
  role: 'user' | 'admin' | 'super_admin' | 'moderator'
  plan: 'Free' | 'Student' | 'Hobby' | 'Bughunter' | 'Cybersecurity' | 'Pro' | 'Expert' | 'Elite'
  level: number
  score: number
  streak_days: number
  email_verified: boolean
  status?: 'active' | 'inactive' | 'suspended' | 'banned'
  phone?: string
  telegram_id?: number
  whatsapp_number?: string
  avatar_url?: string
  timezone?: string
  language?: string
  last_active: Date
  created_at: Date
}

export interface AuthTokens {
  accessToken: string
  refreshToken: string
  expiresIn: number
}

export interface LoginCredentials {
  email: string
  password: string
}

export interface RegisterData {
  username: string
  email: string
  password: string
  fullName?: string
}

const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key'
const JWT_REFRESH_SECRET = process.env.JWT_REFRESH_SECRET || 'your-super-secret-refresh-key'
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '1h'
const JWT_REFRESH_EXPIRES_IN = process.env.JWT_REFRESH_EXPIRES_IN || '7d'
const BCRYPT_ROUNDS = 12

export class RealAuthService {
  // Hash password
  static async hashPassword(password: string): Promise<string> {
    return await bcrypt.hash(password, BCRYPT_ROUNDS)
  }

  // Verify password
  static async verifyPassword(password: string, hash: string): Promise<boolean> {
    return await bcrypt.compare(password, hash)
  }

  // Generate JWT tokens
  static generateTokens(user: User): AuthTokens {
    const payload = {
      id: user.id,
      username: user.username,
      email: user.email,
      role: user.role,
      plan: user.plan
    }

    const accessToken = jwt.sign(payload, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN })
    const refreshToken = jwt.sign(payload, JWT_REFRESH_SECRET, { expiresIn: JWT_REFRESH_EXPIRES_IN })

    return {
      accessToken,
      refreshToken,
      expiresIn: 7 * 24 * 60 * 60 // 7 days in seconds
    }
  }

  // Verify JWT token
  static verifyToken(token: string, isRefreshToken = false): any {
    try {
      const secret = isRefreshToken ? JWT_REFRESH_SECRET : JWT_SECRET
      return jwt.verify(token, secret)
    } catch (error) {
      return null
    }
  }

  // Register new user
  static async register(data: RegisterData): Promise<{ success: boolean; user?: User; tokens?: AuthTokens; message?: string }> {
    try {
      // Check if user already exists
      const existingUsers = await db.query(
        'SELECT id FROM users WHERE email = ? OR username = ?',
        [data.email, data.username]
      )

      if (existingUsers.length > 0) {
        return { success: false, message: 'User already exists with this email or username' }
      }

      // Hash password
      const passwordHash = await this.hashPassword(data.password)

      // Insert user
      const result = await db.query(
        `INSERT INTO users (username, email, password_hash, full_name, role, plan, level, score, streak_days, email_verified, created_at, updated_at, last_active) 
         VALUES (?, ?, ?, ?, 'user', 'Free', 1, 0, 0, false, NOW(), NOW(), NOW())`,
        [data.username, data.email, passwordHash, data.fullName || '']
      )

      const userId = (result as any).insertId

      // Create user preferences
      await db.query(
        `INSERT INTO user_preferences (user_id, created_at, updated_at) 
         VALUES (?, NOW(), NOW())`,
        [userId]
      )

      // Get created user
      const users = await db.query('SELECT * FROM users WHERE id = ?', [userId])
      const user = users[0] as User

      // Generate tokens
      const tokens = this.generateTokens(user)

      // Save session
      await this.saveSession(userId, tokens.accessToken, tokens.refreshToken)

      return { success: true, user, tokens }
    } catch (error) {
      console.error('Registration error:', error)
      return { success: false, message: 'Registration failed' }
    }
  }

  // Login user (overloaded method)
  static async login(email: string, password: string): Promise<{ success: boolean; user?: User; tokens?: AuthTokens; message?: string }>
  static async login(credentials: LoginCredentials): Promise<{ success: boolean; user?: User; tokens?: AuthTokens; message?: string }>
  static async login(emailOrCredentials: string | LoginCredentials, password?: string): Promise<{ success: boolean; user?: User; tokens?: AuthTokens; message?: string }> {
    try {
      // Handle both parameter formats
      let email: string
      let pwd: string

      if (typeof emailOrCredentials === 'string') {
        email = emailOrCredentials
        pwd = password!
      } else {
        email = emailOrCredentials.email
        pwd = emailOrCredentials.password
      }

      // Get user by email (optimized query)
      const users = await db.query(
        'SELECT id, username, email, full_name, role, plan, level, score, streak_days, email_verified, status, last_active, created_at, password_hash FROM users WHERE email = ? AND (status IS NULL OR status = "active") LIMIT 1',
        [email]
      ) as any[]

      if (!users || users.length === 0) {
        return { success: false, message: 'Invalid email or password' }
      }

      const user = users[0] as User & { password_hash: string }

      // Verify password (optimized)
      const isValidPassword = await this.verifyPassword(pwd, user.password_hash)
      if (!isValidPassword) {
        return { success: false, message: 'Invalid email or password' }
      }

      // Remove password from user object before any async operations
      const { password_hash, ...userWithoutPassword } = user

      // Generate tokens (synchronous operation)
      const tokens = this.generateTokens(userWithoutPassword)

      // Async operations in parallel (non-blocking)
      const updatePromises = [
        // Update last active (non-blocking)
        db.query('UPDATE users SET last_active = NOW() WHERE id = ?', [user.id]).catch(err =>
          console.warn('Failed to update last_active:', err)
        ),
        // Save session (non-blocking)
        this.saveSession(user.id, tokens.accessToken, tokens.refreshToken).catch(err =>
          console.warn('Failed to save session:', err)
        )
      ]

      // Don't wait for these operations to complete
      Promise.all(updatePromises).catch(() => {
        // Ignore errors, login should still succeed
      })

      return { success: true, user: userWithoutPassword, tokens }
    } catch (error) {
      console.error('Login error:', error)
      return { success: false, message: 'Login failed' }
    }
  }

  // Authenticate request
  static async authenticateRequest(request: NextRequest): Promise<{ success: boolean; user?: User; message?: string }> {
    try {
      // Get token from Authorization header or cookies
      let token = request.headers.get('authorization')?.replace('Bearer ', '')
      
      if (!token) {
        // Try to get from cookies
        const cookies = request.headers.get('cookie')
        if (cookies) {
          const tokenMatch = cookies.match(/accessToken=([^;]+)/)
          token = tokenMatch ? tokenMatch[1] : null
        }
      }

      if (!token) {
        return { success: false, message: 'No token provided' }
      }

      // Verify token
      const decoded = this.verifyToken(token)
      if (!decoded) {
        return { success: false, message: 'Invalid token' }
      }

      // Get user from database
      const users = await db.query('SELECT * FROM users WHERE id = ?', [decoded.id])
      if (users.length === 0) {
        return { success: false, message: 'User not found' }
      }

      const user = users[0] as User

      // Check if session is valid
      const sessions = await db.query(
        'SELECT id FROM user_sessions WHERE user_id = ? AND session_token = ? AND expires_at > NOW()',
        [user.id, token]
      )

      if (sessions.length === 0) {
        return { success: false, message: 'Session expired' }
      }

      return { success: true, user }
    } catch (error) {
      console.error('Authentication error:', error)
      return { success: false, message: 'Authentication failed' }
    }
  }

  // Save user session
  static async saveSession(userId: number | string, sessionToken: string, refreshToken: string, ipAddress?: string, userAgent?: string): Promise<void> {
    try {
      // Generate a UUID for the session
      const sessionId = crypto.randomUUID()

      // Convert userId to string if it's a number
      const userIdStr = userId.toString()

      await db.query(
        `INSERT INTO user_sessions (id, user_id, session_token, refresh_token, ip_address, user_agent, expires_at)
         VALUES (?, ?, ?, ?, ?, ?, DATE_ADD(NOW(), INTERVAL 7 DAY))`,
        [sessionId, userIdStr, sessionToken, refreshToken, ipAddress || null, userAgent || null]
      )
    } catch (error) {
      console.error('Failed to save session:', error)
      // Don't throw error to allow login to continue
    }
  }

  // Logout user
  static async logout(userId: number, sessionToken: string): Promise<void> {
    await db.query(
      'DELETE FROM user_sessions WHERE user_id = ? AND session_token = ?',
      [userId, sessionToken]
    )
  }

  // Refresh token
  static async refreshToken(refreshToken: string): Promise<AuthResult> {
    try {
      // Verify refresh token
      const decoded = this.verifyToken(refreshToken, true)
      if (!decoded) {
        return { success: false, message: 'Invalid refresh token' }
      }

      // Check if session exists and is valid
      const [sessionRows] = await db.query(
        'SELECT * FROM user_sessions WHERE refresh_token = ? AND expires_at > NOW()',
        [refreshToken]
      )

      if (!sessionRows || (sessionRows as any[]).length === 0) {
        return { success: false, message: 'Session expired or not found' }
      }

      const session = (sessionRows as any[])[0]

      // Get user data
      const [userRows] = await db.query(
        'SELECT * FROM users WHERE id = ? AND is_active = true',
        [decoded.id]
      )

      if (!userRows || (userRows as any[]).length === 0) {
        return { success: false, message: 'User not found or inactive' }
      }

      const user = (userRows as any[])[0]

      // Generate new tokens
      const tokens = this.generateTokens(user)

      // Update session with new tokens
      await db.query(
        `UPDATE user_sessions
         SET session_token = ?, refresh_token = ?, expires_at = DATE_ADD(NOW(), INTERVAL 7 DAY), updated_at = NOW()
         WHERE id = ?`,
        [tokens.accessToken, tokens.refreshToken, session.id]
      )

      // Update user last active
      await db.query('UPDATE users SET last_active = NOW() WHERE id = ?', [user.id])

      return { success: true, user, tokens }
    } catch (error) {
      console.error('Token refresh error:', error)
      return { success: false, message: 'Token refresh failed' }
    }
  }

  // Get user by ID
  static async getUserById(userId: number): Promise<User | null> {
    try {
      const users = await db.query('SELECT * FROM users WHERE id = ?', [userId])
      return users.length > 0 ? users[0] as User : null
    } catch (error) {
      console.error('Get user error:', error)
      return null
    }
  }

  // Update user last active
  static async updateLastActive(userId: number): Promise<void> {
    await db.query('UPDATE users SET last_active = NOW() WHERE id = ?', [userId])
  }

  // Change password
  static async changePassword(userId: number, currentPassword: string, newPassword: string): Promise<{ success: boolean; message?: string }> {
    try {
      // Get user
      const users = await db.query('SELECT password_hash FROM users WHERE id = ?', [userId])
      if (users.length === 0) {
        return { success: false, message: 'User not found' }
      }

      const user = users[0] as { password_hash: string }

      // Verify current password
      const isValidPassword = await this.verifyPassword(currentPassword, user.password_hash)
      if (!isValidPassword) {
        return { success: false, message: 'Current password is incorrect' }
      }

      // Hash new password
      const newPasswordHash = await this.hashPassword(newPassword)

      // Update password
      await db.query(
        'UPDATE users SET password_hash = ?, updated_at = NOW() WHERE id = ?',
        [newPasswordHash, userId]
      )

      // Revoke all sessions
      await db.query('DELETE FROM user_sessions WHERE user_id = ?', [userId])

      return { success: true, message: 'Password changed successfully' }
    } catch (error) {
      console.error('Change password error:', error)
      return { success: false, message: 'Failed to change password' }
    }
  }

  // Clean expired sessions
  static async cleanExpiredSessions(): Promise<void> {
    await db.query('DELETE FROM user_sessions WHERE expires_at < NOW()')
  }
}
