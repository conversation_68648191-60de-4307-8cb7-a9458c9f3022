'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/hooks/auth'
import Link from 'next/link'
import PublicLayout from '@/components/PublicLayout'
import { 
  Shield, 
  Search, 
  FileText, 
  Database,
  Globe,
  Cpu,
  Zap, 
  Users, 
  Trophy,
  Star,
  ArrowRight,
  CheckCircle,
  Play,
  Target,
  Eye,
  Code,
  Activity,
  Terminal,
  Crown,
  Lock,
  Wifi
} from 'lucide-react'

interface PlatformStats {
  totalUsers: number
  activeUsers: number
  totalScans: number
  vulnerabilitiesFound: number
  threatsBlocked: number
  dataProcessed: string
  uptime: string
  successRate: number
}

interface RecentActivity {
  id: string
  type: string
  description: string
  timestamp: Date
  severity?: string
  user?: string
}

export default function HomePage() {
  const { isAuthenticated, isLoading } = useAuth()
  const router = useRouter()

  // Redirect to dashboard if authenticated
  useEffect(() => {
    if (!isLoading && isAuthenticated) {
      router.push('/dashboard')
    }
  }, [isAuthenticated, isLoading, router])

  const [stats, setStats] = useState<PlatformStats>({
    totalUsers: 0,
    activeUsers: 0,
    totalScans: 0,
    vulnerabilitiesFound: 0,
    threatsBlocked: 0,
    dataProcessed: '0 B',
    uptime: '0 days',
    successRate: 100
  })

  const [recentActivities, setRecentActivities] = useState<RecentActivity[]>([])
  const [isVisible, setIsVisible] = useState(false)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    setIsVisible(true)
    loadPlatformStats()
  }, [])

  const loadPlatformStats = async () => {
    try {
      const response = await fetch('/api/stats/platform')
      const data = await response.json()

      if (data.success) {
        setStats(data.data.platform)
        setRecentActivities(data.data.recentActivities)
      }
    } catch (error) {
      console.error('Failed to load platform stats:', error)
      // Use fallback stats if API fails
      setStats({
        totalUsers: 1250,
        activeUsers: 89,
        totalScans: 15420,
        vulnerabilitiesFound: 2847,
        threatsBlocked: 156,
        dataProcessed: '2.4 GB',
        uptime: '2 years 3 months',
        successRate: 98
      })
    } finally {
      setLoading(false)
    }
  }

  const features = [
    {
      icon: Search,
      title: 'OSINT Investigator',
      description: 'Advanced intelligence gathering with NIK, NPWP, phone numbers, IMEI, and sensitive data investigation',
      color: 'text-cyber-primary',
      gradient: 'from-cyan-500 to-blue-500',
      href: '/osint'
    },
    {
      icon: Shield,
      title: 'Vulnerability Scanner',
      description: 'Automated security scanning for SQLi, XSS, LFI, RCE, and other critical vulnerabilities with CVSS scoring',
      color: 'text-cyber-secondary',
      gradient: 'from-pink-500 to-purple-500',
      href: '/scanner'
    },
    {
      icon: FileText,
      title: 'File Analyzer',
      description: 'Deep file analysis for webshells, malware, ransomware, and hidden threats detection',
      color: 'text-cyber-accent',
      gradient: 'from-yellow-500 to-orange-500',
      href: '/file-analyzer'
    },
    {
      icon: Database,
      title: 'CVE Intelligence',
      description: 'Comprehensive CVE database with daily updates and latest vulnerability intelligence',
      color: 'text-red-400',
      gradient: 'from-red-500 to-pink-500',
      href: '/cve'
    },
    {
      icon: Globe,
      title: 'Google Dorking',
      description: 'Extensive dork collection for sensitive information discovery and security research',
      color: 'text-green-400',
      gradient: 'from-green-500 to-emerald-500',
      href: '/dorking'
    },
    {
      icon: Cpu,
      title: 'Developer Tools',
      description: 'Essential security tools including hash generators, encoders, and payload builders',
      color: 'text-purple-400',
      gradient: 'from-purple-500 to-indigo-500',
      href: '/tools'
    }
  ]

  const plans = [
    {
      name: 'Free',
      price: '$0',
      period: '/month',
      features: ['10 scans/day', 'Basic OSINT', 'CVE lookup', 'Community support'],
      color: 'border-gray-600',
      popular: false
    },
    {
      name: 'Student',
      price: '$9',
      period: '/month',
      features: ['100 scans/day', 'Full OSINT', 'File analyzer', 'Bot access', 'Priority support'],
      color: 'border-cyber-primary',
      popular: true
    },
    {
      name: 'Cybersecurity',
      price: '$49',
      period: '/month',
      features: ['Unlimited scans', 'Advanced features', 'API access', 'Custom integrations', '24/7 support'],
      color: 'border-cyber-secondary',
      popular: false
    }
  ]

  // Show loading state
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-cyber-dark">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-cyber-primary"></div>
      </div>
    )
  }

  // If authenticated, don't render (will redirect)
  if (isAuthenticated) {
    return null
  }

  return (
    <PublicLayout>
      {/* Hero Section */}
      <section className="relative overflow-hidden min-h-screen flex items-center">
        {/* Cyber Background Effects */}
        <div className="absolute inset-0">
          <div className="absolute inset-0 bg-gradient-to-br from-cyber-bg-primary via-cyber-bg-secondary to-cyber-bg-tertiary"></div>
          
          {/* Animated Cyber Lines */}
          <div className="absolute inset-0">
            {Array.from({ length: 10 }, (_, i) => (
              <div
                key={i}
                className="absolute h-px bg-gradient-to-r from-transparent via-cyber-primary to-transparent opacity-30 animate-cyber-scan"
                style={{
                  top: `${10 + i * 10}%`,
                  animationDelay: `${i * 0.5}s`,
                  animationDuration: `${3 + i * 0.2}s`
                }}
              ></div>
            ))}
          </div>
          
          {/* Floating Particles */}
          <div className="absolute inset-0">
            {Array.from({ length: 50 }, (_, i) => (
              <div
                key={i}
                className="absolute w-1 h-1 bg-cyber-primary rounded-full animate-matrix-rain opacity-40"
                style={{
                  left: `${Math.random() * 100}%`,
                  animationDelay: `${Math.random() * 5}s`,
                  animationDuration: `${5 + Math.random() * 3}s`
                }}
              ></div>
            ))}
          </div>
        </div>
        
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
          <div className="text-center">
            {/* Logo with Cyber Effect */}
            <div className="flex justify-center mb-8">
              <div className="relative group">
                <Shield className="h-24 w-24 text-cyber-primary animate-cyber-glow" />
                <div className="absolute inset-0 bg-cyber-primary opacity-30 blur-xl animate-cyber-pulse"></div>
                <div className="absolute -inset-4 border border-cyber-primary opacity-20 rounded-full animate-ping"></div>
              </div>
            </div>
            
            {/* Main Heading with Glitch Effect */}
            <h1 className={`text-5xl md:text-7xl lg:text-8xl font-bold mb-6 transition-all duration-1000 ${
              isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
            }`}>
              <span className="text-cyber-glow animate-glitch">
                KodeX
              </span>
              <span className="text-cyber-pink">
                Guard
              </span>
            </h1>
            
            <div className={`transition-all duration-1000 delay-300 ${
              isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
            }`}>
              <p className="text-xl md:text-3xl text-cyber-secondary mb-4 font-bold uppercase tracking-wider">
                Cybersecurity & Bug Hunting Platform
              </p>
              
              <p className="text-lg text-gray-400 mb-8 max-w-3xl mx-auto leading-relaxed">
                Advanced OSINT Investigation • Vulnerability Scanner • File Analyzer • CVE Intelligence • 
                Google Dorking • Developer Tools in one integrated cyberpunk platform
              </p>
            </div>
            
            {/* CTA Buttons */}
            <div className={`flex flex-col sm:flex-row gap-6 justify-center mb-16 transition-all duration-1000 delay-500 ${
              isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
            }`}>
              <Link href="/register" className="btn-cyber-primary text-lg px-8 py-4">
                <Zap className="h-5 w-5 mr-2" />
                Start Hacking
              </Link>
              <Link href="/demo" className="btn-cyber-secondary text-lg px-8 py-4">
                <Play className="h-5 w-5 mr-2" />
                Watch Demo
              </Link>
            </div>
            
            {/* Cyber Stats */}
            <div className={`grid grid-cols-2 md:grid-cols-4 gap-8 max-w-5xl mx-auto transition-all duration-1000 delay-700 ${
              isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
            }`}>
              <div className="text-center group">
                <div className="text-4xl md:text-5xl font-bold text-cyber-primary mb-2 animate-cyber-glow group-hover:animate-cyber-pulse">
                  {stats.totalScans.toLocaleString()}+
                </div>
                <div className="text-gray-400 uppercase tracking-wider font-medium">Security Scans</div>
              </div>
              <div className="text-center group">
                <div className="text-4xl md:text-5xl font-bold text-cyber-secondary mb-2 animate-cyber-glow-pink group-hover:animate-cyber-pulse">
                  {stats.vulnerabilitiesFound.toLocaleString()}+
                </div>
                <div className="text-gray-400 uppercase tracking-wider font-medium">Vulnerabilities Found</div>
              </div>
              <div className="text-center group">
                <div className="text-4xl md:text-5xl font-bold text-cyber-accent mb-2 animate-cyber-glow group-hover:animate-cyber-pulse">
                  {stats.totalUsers.toLocaleString()}+
                </div>
                <div className="text-gray-400 uppercase tracking-wider font-medium">Cyber Warriors</div>
              </div>
              <div className="text-center group">
                <div className="text-4xl md:text-5xl font-bold text-green-400 mb-2 animate-cyber-glow group-hover:animate-cyber-pulse">
                  {stats.threatsBlocked.toLocaleString()}+
                </div>
                <div className="text-gray-400 uppercase tracking-wider font-medium">Threats Blocked</div>
              </div>
            </div>
          </div>
        </div>
        
        {/* Scroll Indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
          <div className="w-6 h-10 border-2 border-cyber-primary rounded-full flex justify-center">
            <div className="w-1 h-3 bg-cyber-primary rounded-full mt-2 animate-pulse"></div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 relative">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              <span className="text-cyber-glow">Cyber</span> Arsenal
            </h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto">
              Advanced cybersecurity tools and intelligence gathering capabilities 
              designed for modern security professionals and ethical hackers
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => {
              const Icon = feature.icon
              return (
                <Link
                  key={feature.title}
                  href={feature.href}
                  className="group card-cyber hover:border-cyber-primary transition-all duration-300"
                >
                  <div className="relative">
                    <div className={`inline-flex p-4 rounded-lg bg-gradient-to-r ${feature.gradient} mb-6 group-hover:animate-cyber-pulse`}>
                      <Icon className="h-8 w-8 text-white" />
                    </div>
                    
                    <h3 className={`text-xl font-bold mb-4 ${feature.color} group-hover:text-white transition-colors duration-300`}>
                      {feature.title}
                    </h3>
                    
                    <p className="text-gray-400 group-hover:text-gray-300 transition-colors duration-300 leading-relaxed">
                      {feature.description}
                    </p>
                    
                    <div className="mt-6 flex items-center text-cyber-primary group-hover:text-white transition-colors duration-300">
                      <span className="text-sm font-medium">Explore Tool</span>
                      <ArrowRight className="h-4 w-4 ml-2 group-hover:translate-x-1 transition-transform duration-300" />
                    </div>
                  </div>
                </Link>
              )
            })}
          </div>
        </div>
      </section>

      {/* Real-Time Activity Feed */}
      <section className="py-20 bg-cyber-dark/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-cyber-glow mb-6">
              Live Security Feed
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Real-time cybersecurity activities from our global community
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Recent Activities */}
            <div className="bg-cyber-card border border-cyber-border rounded-xl p-6">
              <h3 className="text-2xl font-bold text-cyber-primary mb-6 flex items-center">
                <Activity className="h-6 w-6 mr-3" />
                Recent Activities
              </h3>

              <div className="space-y-4 max-h-96 overflow-y-auto">
                {loading ? (
                  // Loading skeleton
                  Array.from({ length: 5 }).map((_, i) => (
                    <div key={i} className="animate-pulse">
                      <div className="flex items-start space-x-3">
                        <div className="w-8 h-8 bg-gray-700 rounded-full"></div>
                        <div className="flex-1">
                          <div className="h-4 bg-gray-700 rounded w-3/4 mb-2"></div>
                          <div className="h-3 bg-gray-800 rounded w-1/2"></div>
                        </div>
                      </div>
                    </div>
                  ))
                ) : recentActivities.length > 0 ? (
                  recentActivities.map((activity) => (
                    <div key={activity.id} className="flex items-start space-x-3 p-3 rounded-lg hover:bg-cyber-primary/5 transition-colors">
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                        activity.type === 'scan' ? 'bg-blue-500/20 text-blue-400' :
                        activity.type === 'osint' ? 'bg-purple-500/20 text-purple-400' :
                        activity.type === 'file_analysis' ? 'bg-orange-500/20 text-orange-400' :
                        'bg-green-500/20 text-green-400'
                      }`}>
                        {activity.type === 'scan' && <Shield className="h-4 w-4" />}
                        {activity.type === 'osint' && <Search className="h-4 w-4" />}
                        {activity.type === 'file_analysis' && <FileText className="h-4 w-4" />}
                        {activity.type === 'user_registration' && <Users className="h-4 w-4" />}
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm text-gray-300 leading-relaxed">
                          {activity.description}
                        </p>
                        <div className="flex items-center justify-between mt-1">
                          <p className="text-xs text-gray-500">
                            {new Date(activity.timestamp).toLocaleTimeString()}
                          </p>
                          {activity.severity && (
                            <span className={`text-xs px-2 py-1 rounded-full ${
                              activity.severity === 'critical' ? 'bg-red-500/20 text-red-400' :
                              activity.severity === 'high' ? 'bg-orange-500/20 text-orange-400' :
                              activity.severity === 'medium' ? 'bg-yellow-500/20 text-yellow-400' :
                              'bg-green-500/20 text-green-400'
                            }`}>
                              {activity.severity}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <Activity className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>No recent activities</p>
                  </div>
                )}
              </div>
            </div>

            {/* Platform Statistics */}
            <div className="bg-cyber-card border border-cyber-border rounded-xl p-6">
              <h3 className="text-2xl font-bold text-cyber-primary mb-6 flex items-center">
                <Target className="h-6 w-6 mr-3" />
                Platform Health
              </h3>

              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <span className="text-gray-300">System Uptime</span>
                  <span className="text-cyber-primary font-bold">{stats.uptime}</span>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-gray-300">Success Rate</span>
                  <span className="text-green-400 font-bold">{stats.successRate}%</span>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-gray-300">Active Users</span>
                  <span className="text-cyber-secondary font-bold">{stats.activeUsers}</span>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-gray-300">Data Processed</span>
                  <span className="text-cyber-accent font-bold">{stats.dataProcessed}</span>
                </div>

                {/* Success Rate Progress Bar */}
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-400">System Performance</span>
                    <span className="text-cyber-primary">{stats.successRate}%</span>
                  </div>
                  <div className="w-full bg-gray-700 rounded-full h-2">
                    <div
                      className="bg-gradient-to-r from-cyber-primary to-cyber-secondary h-2 rounded-full transition-all duration-1000"
                      style={{ width: `${stats.successRate}%` }}
                    ></div>
                  </div>
                </div>

                {/* Real-time indicator */}
                <div className="flex items-center justify-center space-x-2 pt-4 border-t border-cyber-border">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  <span className="text-sm text-gray-400">Live data • Updated now</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </PublicLayout>
  )
}
