import { NextRequest, NextResponse } from 'next/server'
import { SimpleAuthService } from '@/lib/auth-simple'

export async function POST(request: NextRequest) {
  try {
    console.log('🔐 Logout attempt started')

    // Get token from Authorization header or cookies
    let token = request.headers.get('authorization')?.replace('Bearer ', '')

    if (!token) {
      // Try to get from cookies
      const cookies = request.headers.get('cookie')
      if (cookies) {
        const tokenMatch = cookies.match(/accessToken=([^;]+)/)
        token = tokenMatch ? tokenMatch[1] : null
      }
    }

    if (!token) {
      return NextResponse.json({
        success: false,
        error: 'No token provided'
      }, { status: 401 })
    }

    // Verify token to get user ID
    const decoded = SimpleAuthService.verifyToken(token)
    if (!decoded) {
      return NextResponse.json({
        success: false,
        error: 'Invalid token'
      }, { status: 401 })
    }

    // Logout user (remove session)
    await SimpleAuthService.logout(decoded.id, token)

    // Create response
    const response = NextResponse.json({
      success: true,
      message: 'Logout successful'
    })

    // Clear cookies
    response.cookies.set('accessToken', '', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      path: '/',
      maxAge: 0
    })

    response.cookies.set('refreshToken', '', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      path: '/',
      maxAge: 0
    })

    response.cookies.set('user', '', {
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      path: '/',
      maxAge: 0
    })

    console.log('✅ Logout successful for user:', decoded.id)
    return response

  } catch (error) {
    console.error('❌ Logout error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: 'An unexpected error occurred during logout'
      },
      { status: 500 }
    )
  }
}

export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}
