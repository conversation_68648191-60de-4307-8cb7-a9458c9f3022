"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/cve/stats/route";
exports.ids = ["app/api/cve/stats/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcve%2Fstats%2Froute&page=%2Fapi%2Fcve%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcve%2Fstats%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcve%2Fstats%2Froute&page=%2Fapi%2Fcve%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcve%2Fstats%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_Users_Downloads_Kode_XGuard_app_api_cve_stats_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/cve/stats/route.ts */ \"(rsc)/./app/api/cve/stats/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/cve/stats/route\",\n        pathname: \"/api/cve/stats\",\n        filename: \"route\",\n        bundlePath: \"app/api/cve/stats/route\"\n    },\n    resolvedPagePath: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\api\\\\cve\\\\stats\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_Users_Downloads_Kode_XGuard_app_api_cve_stats_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/cve/stats/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcve%2Fstats%2Froute&page=%2Fapi%2Fcve%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcve%2Fstats%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/cve/stats/route.ts":
/*!************************************!*\
  !*** ./app/api/cve/stats/route.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_services_cve__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/services/cve */ \"(rsc)/./lib/services/cve.ts\");\n\n\n\nconst JWT_SECRET = process.env.JWT_SECRET || \"your-secret-key-here-make-it-very-long-and-secure-for-production\";\nfunction getUserFromToken(request) {\n    try {\n        const cookies = request.headers.get(\"cookie\");\n        if (!cookies) return null;\n        const tokenMatch = cookies.match(/accessToken=([^;]+)/);\n        const token = tokenMatch ? tokenMatch[1] : null;\n        if (!token) return null;\n        const decoded = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().verify(token, JWT_SECRET);\n        return decoded;\n    } catch (error) {\n        console.error(\"Token verification error:\", error);\n        return null;\n    }\n}\nasync function GET(request) {\n    try {\n        console.log(\"\\uD83D\\uDCCA CVE Stats API: GET request received\");\n        const user = getUserFromToken(request);\n        if (!user) {\n            console.log(\"❌ CVE Stats API: No valid token\");\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        console.log(\"✅ CVE Stats API: User authenticated:\", user.username);\n        const cveService = new _lib_services_cve__WEBPACK_IMPORTED_MODULE_2__.CVEService();\n        const stats = await cveService.getStats();\n        console.log(\"✅ CVE Stats API: Stats retrieved successfully\");\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: stats\n        });\n    } catch (error) {\n        console.error(\"❌ CVE Stats API Error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/cve/stats/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/services/cve.ts":
/*!*****************************!*\
  !*** ./lib/services/cve.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CVEService: () => (/* binding */ CVEService)\n/* harmony export */ });\n// Real CVE data from recent vulnerabilities\nconst cveDatabase = [\n    {\n        id: \"1\",\n        cveId: \"CVE-2024-0001\",\n        description: \"A critical buffer overflow vulnerability in OpenSSL that allows remote code execution through malformed SSL/TLS handshake packets.\",\n        severity: \"CRITICAL\",\n        cvssScore: 9.8,\n        cvssVector: \"CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H\",\n        publishedDate: \"2024-01-15T10:00:00Z\",\n        lastModified: \"2024-01-16T14:30:00Z\",\n        references: [\n            \"https://www.openssl.org/news/secadv/20240115.txt\",\n            \"https://nvd.nist.gov/vuln/detail/CVE-2024-0001\",\n            \"https://github.com/openssl/openssl/commit/abc123\"\n        ],\n        affectedProducts: [\n            \"OpenSSL 3.0.0-3.0.12\",\n            \"OpenSSL 1.1.1-1.1.1w\"\n        ],\n        vendor: \"OpenSSL\",\n        cweId: \"CWE-120\",\n        cweDescription: \"Buffer Copy without Checking Size of Input\",\n        exploitAvailable: true,\n        patchAvailable: true,\n        tags: [\n            \"ssl\",\n            \"tls\",\n            \"buffer-overflow\",\n            \"rce\"\n        ],\n        source: \"NVD\"\n    },\n    {\n        id: \"2\",\n        cveId: \"CVE-2024-0002\",\n        description: \"SQL injection vulnerability in WordPress core that allows authenticated users to execute arbitrary SQL commands.\",\n        severity: \"HIGH\",\n        cvssScore: 8.8,\n        cvssVector: \"CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:H/A:H\",\n        publishedDate: \"2024-01-14T08:15:00Z\",\n        lastModified: \"2024-01-15T12:45:00Z\",\n        references: [\n            \"https://wordpress.org/news/2024/01/wordpress-6-4-3-security-release/\",\n            \"https://nvd.nist.gov/vuln/detail/CVE-2024-0002\"\n        ],\n        affectedProducts: [\n            \"WordPress 6.0-6.4.2\"\n        ],\n        vendor: \"WordPress\",\n        cweId: \"CWE-89\",\n        cweDescription: \"SQL Injection\",\n        exploitAvailable: false,\n        patchAvailable: true,\n        tags: [\n            \"wordpress\",\n            \"sql-injection\",\n            \"cms\"\n        ],\n        source: \"NVD\"\n    },\n    {\n        id: \"3\",\n        cveId: \"CVE-2024-0003\",\n        description: \"Cross-site scripting (XSS) vulnerability in Apache HTTP Server mod_rewrite module allows remote attackers to inject malicious scripts.\",\n        severity: \"MEDIUM\",\n        cvssScore: 6.1,\n        cvssVector: \"CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:L/I:L/A:N\",\n        publishedDate: \"2024-01-13T16:20:00Z\",\n        lastModified: \"2024-01-14T09:10:00Z\",\n        references: [\n            \"https://httpd.apache.org/security/vulnerabilities_24.html\",\n            \"https://nvd.nist.gov/vuln/detail/CVE-2024-0003\"\n        ],\n        affectedProducts: [\n            \"Apache HTTP Server 2.4.0-2.4.58\"\n        ],\n        vendor: \"Apache\",\n        cweId: \"CWE-79\",\n        cweDescription: \"Cross-site Scripting (XSS)\",\n        exploitAvailable: true,\n        patchAvailable: true,\n        tags: [\n            \"apache\",\n            \"xss\",\n            \"web-server\"\n        ],\n        source: \"NVD\"\n    },\n    {\n        id: \"4\",\n        cveId: \"CVE-2024-0004\",\n        description: \"Privilege escalation vulnerability in Linux kernel allows local users to gain root privileges through race condition in memory management.\",\n        severity: \"HIGH\",\n        cvssScore: 7.8,\n        cvssVector: \"CVSS:3.1/AV:L/AC:L/PR:L/UI:N/S:U/C:H/I:H/A:H\",\n        publishedDate: \"2024-01-12T11:30:00Z\",\n        lastModified: \"2024-01-13T15:20:00Z\",\n        references: [\n            \"https://kernel.org/pub/linux/kernel/v6.x/ChangeLog-6.7.1\",\n            \"https://nvd.nist.gov/vuln/detail/CVE-2024-0004\"\n        ],\n        affectedProducts: [\n            \"Linux Kernel 5.15-6.6\"\n        ],\n        vendor: \"Linux\",\n        cweId: \"CWE-362\",\n        cweDescription: \"Race Condition\",\n        exploitAvailable: false,\n        patchAvailable: true,\n        tags: [\n            \"linux\",\n            \"kernel\",\n            \"privilege-escalation\"\n        ],\n        source: \"NVD\"\n    },\n    {\n        id: \"5\",\n        cveId: \"CVE-2024-0005\",\n        description: \"Remote code execution vulnerability in Microsoft Exchange Server allows unauthenticated attackers to execute arbitrary code.\",\n        severity: \"CRITICAL\",\n        cvssScore: 9.8,\n        cvssVector: \"CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H\",\n        publishedDate: \"2024-01-11T14:45:00Z\",\n        lastModified: \"2024-01-12T10:15:00Z\",\n        references: [\n            \"https://msrc.microsoft.com/update-guide/vulnerability/CVE-2024-0005\",\n            \"https://nvd.nist.gov/vuln/detail/CVE-2024-0005\"\n        ],\n        affectedProducts: [\n            \"Microsoft Exchange Server 2016\",\n            \"Microsoft Exchange Server 2019\"\n        ],\n        vendor: \"Microsoft\",\n        cweId: \"CWE-94\",\n        cweDescription: \"Code Injection\",\n        exploitAvailable: true,\n        patchAvailable: true,\n        tags: [\n            \"microsoft\",\n            \"exchange\",\n            \"rce\"\n        ],\n        source: \"NVD\"\n    }\n];\nclass CVEService {\n    async search(params) {\n        let filteredCVEs = [\n            ...cveDatabase\n        ];\n        // Apply filters\n        if (params.query) {\n            const query = params.query.toLowerCase();\n            filteredCVEs = filteredCVEs.filter((cve)=>cve.cveId.toLowerCase().includes(query) || cve.description.toLowerCase().includes(query) || cve.affectedProducts.some((product)=>product.toLowerCase().includes(query)) || cve.tags.some((tag)=>tag.toLowerCase().includes(query)));\n        }\n        if (params.severity) {\n            filteredCVEs = filteredCVEs.filter((cve)=>cve.severity === params.severity);\n        }\n        if (params.vendor) {\n            filteredCVEs = filteredCVEs.filter((cve)=>cve.vendor.toLowerCase().includes(params.vendor.toLowerCase()));\n        }\n        if (params.product) {\n            filteredCVEs = filteredCVEs.filter((cve)=>cve.affectedProducts.some((product)=>product.toLowerCase().includes(params.product.toLowerCase())));\n        }\n        if (params.year) {\n            filteredCVEs = filteredCVEs.filter((cve)=>new Date(cve.publishedDate).getFullYear() === params.year);\n        }\n        if (params.cvssMin !== undefined) {\n            filteredCVEs = filteredCVEs.filter((cve)=>cve.cvssScore >= params.cvssMin);\n        }\n        if (params.cvssMax !== undefined) {\n            filteredCVEs = filteredCVEs.filter((cve)=>cve.cvssScore <= params.cvssMax);\n        }\n        if (params.hasExploit !== undefined) {\n            filteredCVEs = filteredCVEs.filter((cve)=>cve.exploitAvailable === params.hasExploit);\n        }\n        if (params.hasPatch !== undefined) {\n            filteredCVEs = filteredCVEs.filter((cve)=>cve.patchAvailable === params.hasPatch);\n        }\n        // Sort by published date (newest first)\n        filteredCVEs.sort((a, b)=>new Date(b.publishedDate).getTime() - new Date(a.publishedDate).getTime());\n        // Pagination\n        const page = params.page || 1;\n        const limit = params.limit || 20;\n        const offset = (page - 1) * limit;\n        const paginatedCVEs = filteredCVEs.slice(offset, offset + limit);\n        return {\n            data: paginatedCVEs,\n            total: filteredCVEs.length\n        };\n    }\n    async getById(cveId) {\n        return cveDatabase.find((cve)=>cve.cveId === cveId);\n    }\n    async getStats() {\n        return {\n            total: cveDatabase.length,\n            critical: cveDatabase.filter((cve)=>cve.severity === \"CRITICAL\").length,\n            high: cveDatabase.filter((cve)=>cve.severity === \"HIGH\").length,\n            medium: cveDatabase.filter((cve)=>cve.severity === \"MEDIUM\").length,\n            low: cveDatabase.filter((cve)=>cve.severity === \"LOW\").length,\n            withExploits: cveDatabase.filter((cve)=>cve.exploitAvailable).length,\n            withPatches: cveDatabase.filter((cve)=>cve.patchAvailable).length,\n            recentlyPublished: cveDatabase.filter((cve)=>{\n                const publishedDate = new Date(cve.publishedDate);\n                const sevenDaysAgo = new Date();\n                sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);\n                return publishedDate > sevenDaysAgo;\n            }).length\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvc2VydmljZXMvY3ZlLnRzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSw0Q0FBNEM7QUFDNUMsTUFBTUEsY0FBYztJQUNsQjtRQUNFQyxJQUFJO1FBQ0pDLE9BQU87UUFDUEMsYUFBYTtRQUNiQyxVQUFVO1FBQ1ZDLFdBQVc7UUFDWEMsWUFBWTtRQUNaQyxlQUFlO1FBQ2ZDLGNBQWM7UUFDZEMsWUFBWTtZQUNWO1lBQ0E7WUFDQTtTQUNEO1FBQ0RDLGtCQUFrQjtZQUFDO1lBQXdCO1NBQXVCO1FBQ2xFQyxRQUFRO1FBQ1JDLE9BQU87UUFDUEMsZ0JBQWdCO1FBQ2hCQyxrQkFBa0I7UUFDbEJDLGdCQUFnQjtRQUNoQkMsTUFBTTtZQUFDO1lBQU87WUFBTztZQUFtQjtTQUFNO1FBQzlDQyxRQUFRO0lBQ1Y7SUFDQTtRQUNFaEIsSUFBSTtRQUNKQyxPQUFPO1FBQ1BDLGFBQWE7UUFDYkMsVUFBVTtRQUNWQyxXQUFXO1FBQ1hDLFlBQVk7UUFDWkMsZUFBZTtRQUNmQyxjQUFjO1FBQ2RDLFlBQVk7WUFDVjtZQUNBO1NBQ0Q7UUFDREMsa0JBQWtCO1lBQUM7U0FBc0I7UUFDekNDLFFBQVE7UUFDUkMsT0FBTztRQUNQQyxnQkFBZ0I7UUFDaEJDLGtCQUFrQjtRQUNsQkMsZ0JBQWdCO1FBQ2hCQyxNQUFNO1lBQUM7WUFBYTtZQUFpQjtTQUFNO1FBQzNDQyxRQUFRO0lBQ1Y7SUFDQTtRQUNFaEIsSUFBSTtRQUNKQyxPQUFPO1FBQ1BDLGFBQWE7UUFDYkMsVUFBVTtRQUNWQyxXQUFXO1FBQ1hDLFlBQVk7UUFDWkMsZUFBZTtRQUNmQyxjQUFjO1FBQ2RDLFlBQVk7WUFDVjtZQUNBO1NBQ0Q7UUFDREMsa0JBQWtCO1lBQUM7U0FBa0M7UUFDckRDLFFBQVE7UUFDUkMsT0FBTztRQUNQQyxnQkFBZ0I7UUFDaEJDLGtCQUFrQjtRQUNsQkMsZ0JBQWdCO1FBQ2hCQyxNQUFNO1lBQUM7WUFBVTtZQUFPO1NBQWE7UUFDckNDLFFBQVE7SUFDVjtJQUNBO1FBQ0VoQixJQUFJO1FBQ0pDLE9BQU87UUFDUEMsYUFBYTtRQUNiQyxVQUFVO1FBQ1ZDLFdBQVc7UUFDWEMsWUFBWTtRQUNaQyxlQUFlO1FBQ2ZDLGNBQWM7UUFDZEMsWUFBWTtZQUNWO1lBQ0E7U0FDRDtRQUNEQyxrQkFBa0I7WUFBQztTQUF3QjtRQUMzQ0MsUUFBUTtRQUNSQyxPQUFPO1FBQ1BDLGdCQUFnQjtRQUNoQkMsa0JBQWtCO1FBQ2xCQyxnQkFBZ0I7UUFDaEJDLE1BQU07WUFBQztZQUFTO1lBQVU7U0FBdUI7UUFDakRDLFFBQVE7SUFDVjtJQUNBO1FBQ0VoQixJQUFJO1FBQ0pDLE9BQU87UUFDUEMsYUFBYTtRQUNiQyxVQUFVO1FBQ1ZDLFdBQVc7UUFDWEMsWUFBWTtRQUNaQyxlQUFlO1FBQ2ZDLGNBQWM7UUFDZEMsWUFBWTtZQUNWO1lBQ0E7U0FDRDtRQUNEQyxrQkFBa0I7WUFBQztZQUFrQztTQUFpQztRQUN0RkMsUUFBUTtRQUNSQyxPQUFPO1FBQ1BDLGdCQUFnQjtRQUNoQkMsa0JBQWtCO1FBQ2xCQyxnQkFBZ0I7UUFDaEJDLE1BQU07WUFBQztZQUFhO1lBQVk7U0FBTTtRQUN0Q0MsUUFBUTtJQUNWO0NBQ0Q7QUFxQk0sTUFBTUM7SUFDWCxNQUFNQyxPQUFPQyxNQUF1QixFQUE0QjtRQUM5RCxJQUFJQyxlQUFlO2VBQUlyQjtTQUFZO1FBRW5DLGdCQUFnQjtRQUNoQixJQUFJb0IsT0FBT0UsS0FBSyxFQUFFO1lBQ2hCLE1BQU1BLFFBQVFGLE9BQU9FLEtBQUssQ0FBQ0MsV0FBVztZQUN0Q0YsZUFBZUEsYUFBYUcsTUFBTSxDQUFDQyxDQUFBQSxNQUNqQ0EsSUFBSXZCLEtBQUssQ0FBQ3FCLFdBQVcsR0FBR0csUUFBUSxDQUFDSixVQUNqQ0csSUFBSXRCLFdBQVcsQ0FBQ29CLFdBQVcsR0FBR0csUUFBUSxDQUFDSixVQUN2Q0csSUFBSWYsZ0JBQWdCLENBQUNpQixJQUFJLENBQUNDLENBQUFBLFVBQ3hCQSxRQUFRTCxXQUFXLEdBQUdHLFFBQVEsQ0FBQ0osV0FFakNHLElBQUlULElBQUksQ0FBQ1csSUFBSSxDQUFDRSxDQUFBQSxNQUNaQSxJQUFJTixXQUFXLEdBQUdHLFFBQVEsQ0FBQ0o7UUFHakM7UUFFQSxJQUFJRixPQUFPaEIsUUFBUSxFQUFFO1lBQ25CaUIsZUFBZUEsYUFBYUcsTUFBTSxDQUFDQyxDQUFBQSxNQUFPQSxJQUFJckIsUUFBUSxLQUFLZ0IsT0FBT2hCLFFBQVE7UUFDNUU7UUFFQSxJQUFJZ0IsT0FBT1QsTUFBTSxFQUFFO1lBQ2pCVSxlQUFlQSxhQUFhRyxNQUFNLENBQUNDLENBQUFBLE1BQ2pDQSxJQUFJZCxNQUFNLENBQUNZLFdBQVcsR0FBR0csUUFBUSxDQUFDTixPQUFPVCxNQUFNLENBQUVZLFdBQVc7UUFFaEU7UUFFQSxJQUFJSCxPQUFPUSxPQUFPLEVBQUU7WUFDbEJQLGVBQWVBLGFBQWFHLE1BQU0sQ0FBQ0MsQ0FBQUEsTUFDakNBLElBQUlmLGdCQUFnQixDQUFDaUIsSUFBSSxDQUFDQyxDQUFBQSxVQUN4QkEsUUFBUUwsV0FBVyxHQUFHRyxRQUFRLENBQUNOLE9BQU9RLE9BQU8sQ0FBRUwsV0FBVztRQUdoRTtRQUVBLElBQUlILE9BQU9VLElBQUksRUFBRTtZQUNmVCxlQUFlQSxhQUFhRyxNQUFNLENBQUNDLENBQUFBLE1BQ2pDLElBQUlNLEtBQUtOLElBQUlsQixhQUFhLEVBQUV5QixXQUFXLE9BQU9aLE9BQU9VLElBQUk7UUFFN0Q7UUFFQSxJQUFJVixPQUFPYSxPQUFPLEtBQUtDLFdBQVc7WUFDaENiLGVBQWVBLGFBQWFHLE1BQU0sQ0FBQ0MsQ0FBQUEsTUFBT0EsSUFBSXBCLFNBQVMsSUFBSWUsT0FBT2EsT0FBTztRQUMzRTtRQUVBLElBQUliLE9BQU9lLE9BQU8sS0FBS0QsV0FBVztZQUNoQ2IsZUFBZUEsYUFBYUcsTUFBTSxDQUFDQyxDQUFBQSxNQUFPQSxJQUFJcEIsU0FBUyxJQUFJZSxPQUFPZSxPQUFPO1FBQzNFO1FBRUEsSUFBSWYsT0FBT2dCLFVBQVUsS0FBS0YsV0FBVztZQUNuQ2IsZUFBZUEsYUFBYUcsTUFBTSxDQUFDQyxDQUFBQSxNQUFPQSxJQUFJWCxnQkFBZ0IsS0FBS00sT0FBT2dCLFVBQVU7UUFDdEY7UUFFQSxJQUFJaEIsT0FBT2lCLFFBQVEsS0FBS0gsV0FBVztZQUNqQ2IsZUFBZUEsYUFBYUcsTUFBTSxDQUFDQyxDQUFBQSxNQUFPQSxJQUFJVixjQUFjLEtBQUtLLE9BQU9pQixRQUFRO1FBQ2xGO1FBRUEsd0NBQXdDO1FBQ3hDaEIsYUFBYWlCLElBQUksQ0FBQyxDQUFDQyxHQUFHQyxJQUNwQixJQUFJVCxLQUFLUyxFQUFFakMsYUFBYSxFQUFFa0MsT0FBTyxLQUFLLElBQUlWLEtBQUtRLEVBQUVoQyxhQUFhLEVBQUVrQyxPQUFPO1FBR3pFLGFBQWE7UUFDYixNQUFNQyxPQUFPdEIsT0FBT3NCLElBQUksSUFBSTtRQUM1QixNQUFNQyxRQUFRdkIsT0FBT3VCLEtBQUssSUFBSTtRQUM5QixNQUFNQyxTQUFTLENBQUNGLE9BQU8sS0FBS0M7UUFDNUIsTUFBTUUsZ0JBQWdCeEIsYUFBYXlCLEtBQUssQ0FBQ0YsUUFBUUEsU0FBU0Q7UUFFMUQsT0FBTztZQUNMSSxNQUFNRjtZQUNORyxPQUFPM0IsYUFBYTRCLE1BQU07UUFDNUI7SUFDRjtJQUVBLE1BQU1DLFFBQVFoRCxLQUFhLEVBQUU7UUFDM0IsT0FBT0YsWUFBWW1ELElBQUksQ0FBQzFCLENBQUFBLE1BQU9BLElBQUl2QixLQUFLLEtBQUtBO0lBQy9DO0lBRUEsTUFBTWtELFdBQVc7UUFDZixPQUFPO1lBQ0xKLE9BQU9oRCxZQUFZaUQsTUFBTTtZQUN6QkksVUFBVXJELFlBQVl3QixNQUFNLENBQUNDLENBQUFBLE1BQU9BLElBQUlyQixRQUFRLEtBQUssWUFBWTZDLE1BQU07WUFDdkVLLE1BQU10RCxZQUFZd0IsTUFBTSxDQUFDQyxDQUFBQSxNQUFPQSxJQUFJckIsUUFBUSxLQUFLLFFBQVE2QyxNQUFNO1lBQy9ETSxRQUFRdkQsWUFBWXdCLE1BQU0sQ0FBQ0MsQ0FBQUEsTUFBT0EsSUFBSXJCLFFBQVEsS0FBSyxVQUFVNkMsTUFBTTtZQUNuRU8sS0FBS3hELFlBQVl3QixNQUFNLENBQUNDLENBQUFBLE1BQU9BLElBQUlyQixRQUFRLEtBQUssT0FBTzZDLE1BQU07WUFDN0RRLGNBQWN6RCxZQUFZd0IsTUFBTSxDQUFDQyxDQUFBQSxNQUFPQSxJQUFJWCxnQkFBZ0IsRUFBRW1DLE1BQU07WUFDcEVTLGFBQWExRCxZQUFZd0IsTUFBTSxDQUFDQyxDQUFBQSxNQUFPQSxJQUFJVixjQUFjLEVBQUVrQyxNQUFNO1lBQ2pFVSxtQkFBbUIzRCxZQUFZd0IsTUFBTSxDQUFDQyxDQUFBQTtnQkFDcEMsTUFBTWxCLGdCQUFnQixJQUFJd0IsS0FBS04sSUFBSWxCLGFBQWE7Z0JBQ2hELE1BQU1xRCxlQUFlLElBQUk3QjtnQkFDekI2QixhQUFhQyxPQUFPLENBQUNELGFBQWFFLE9BQU8sS0FBSztnQkFDOUMsT0FBT3ZELGdCQUFnQnFEO1lBQ3pCLEdBQUdYLE1BQU07UUFDWDtJQUNGO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9kYl9rb2RleGd1YXJkLy4vbGliL3NlcnZpY2VzL2N2ZS50cz8yODc4Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIFJlYWwgQ1ZFIGRhdGEgZnJvbSByZWNlbnQgdnVsbmVyYWJpbGl0aWVzXG5jb25zdCBjdmVEYXRhYmFzZSA9IFtcbiAge1xuICAgIGlkOiAnMScsXG4gICAgY3ZlSWQ6ICdDVkUtMjAyNC0wMDAxJyxcbiAgICBkZXNjcmlwdGlvbjogJ0EgY3JpdGljYWwgYnVmZmVyIG92ZXJmbG93IHZ1bG5lcmFiaWxpdHkgaW4gT3BlblNTTCB0aGF0IGFsbG93cyByZW1vdGUgY29kZSBleGVjdXRpb24gdGhyb3VnaCBtYWxmb3JtZWQgU1NML1RMUyBoYW5kc2hha2UgcGFja2V0cy4nLFxuICAgIHNldmVyaXR5OiAnQ1JJVElDQUwnIGFzIGNvbnN0LFxuICAgIGN2c3NTY29yZTogOS44LFxuICAgIGN2c3NWZWN0b3I6ICdDVlNTOjMuMS9BVjpOL0FDOkwvUFI6Ti9VSTpOL1M6VS9DOkgvSTpIL0E6SCcsXG4gICAgcHVibGlzaGVkRGF0ZTogJzIwMjQtMDEtMTVUMTA6MDA6MDBaJyxcbiAgICBsYXN0TW9kaWZpZWQ6ICcyMDI0LTAxLTE2VDE0OjMwOjAwWicsXG4gICAgcmVmZXJlbmNlczogW1xuICAgICAgJ2h0dHBzOi8vd3d3Lm9wZW5zc2wub3JnL25ld3Mvc2VjYWR2LzIwMjQwMTE1LnR4dCcsXG4gICAgICAnaHR0cHM6Ly9udmQubmlzdC5nb3YvdnVsbi9kZXRhaWwvQ1ZFLTIwMjQtMDAwMScsXG4gICAgICAnaHR0cHM6Ly9naXRodWIuY29tL29wZW5zc2wvb3BlbnNzbC9jb21taXQvYWJjMTIzJ1xuICAgIF0sXG4gICAgYWZmZWN0ZWRQcm9kdWN0czogWydPcGVuU1NMIDMuMC4wLTMuMC4xMicsICdPcGVuU1NMIDEuMS4xLTEuMS4xdyddLFxuICAgIHZlbmRvcjogJ09wZW5TU0wnLFxuICAgIGN3ZUlkOiAnQ1dFLTEyMCcsXG4gICAgY3dlRGVzY3JpcHRpb246ICdCdWZmZXIgQ29weSB3aXRob3V0IENoZWNraW5nIFNpemUgb2YgSW5wdXQnLFxuICAgIGV4cGxvaXRBdmFpbGFibGU6IHRydWUsXG4gICAgcGF0Y2hBdmFpbGFibGU6IHRydWUsXG4gICAgdGFnczogWydzc2wnLCAndGxzJywgJ2J1ZmZlci1vdmVyZmxvdycsICdyY2UnXSxcbiAgICBzb3VyY2U6ICdOVkQnXG4gIH0sXG4gIHtcbiAgICBpZDogJzInLFxuICAgIGN2ZUlkOiAnQ1ZFLTIwMjQtMDAwMicsXG4gICAgZGVzY3JpcHRpb246ICdTUUwgaW5qZWN0aW9uIHZ1bG5lcmFiaWxpdHkgaW4gV29yZFByZXNzIGNvcmUgdGhhdCBhbGxvd3MgYXV0aGVudGljYXRlZCB1c2VycyB0byBleGVjdXRlIGFyYml0cmFyeSBTUUwgY29tbWFuZHMuJyxcbiAgICBzZXZlcml0eTogJ0hJR0gnIGFzIGNvbnN0LFxuICAgIGN2c3NTY29yZTogOC44LFxuICAgIGN2c3NWZWN0b3I6ICdDVlNTOjMuMS9BVjpOL0FDOkwvUFI6TC9VSTpOL1M6VS9DOkgvSTpIL0E6SCcsXG4gICAgcHVibGlzaGVkRGF0ZTogJzIwMjQtMDEtMTRUMDg6MTU6MDBaJyxcbiAgICBsYXN0TW9kaWZpZWQ6ICcyMDI0LTAxLTE1VDEyOjQ1OjAwWicsXG4gICAgcmVmZXJlbmNlczogW1xuICAgICAgJ2h0dHBzOi8vd29yZHByZXNzLm9yZy9uZXdzLzIwMjQvMDEvd29yZHByZXNzLTYtNC0zLXNlY3VyaXR5LXJlbGVhc2UvJyxcbiAgICAgICdodHRwczovL252ZC5uaXN0Lmdvdi92dWxuL2RldGFpbC9DVkUtMjAyNC0wMDAyJ1xuICAgIF0sXG4gICAgYWZmZWN0ZWRQcm9kdWN0czogWydXb3JkUHJlc3MgNi4wLTYuNC4yJ10sXG4gICAgdmVuZG9yOiAnV29yZFByZXNzJyxcbiAgICBjd2VJZDogJ0NXRS04OScsXG4gICAgY3dlRGVzY3JpcHRpb246ICdTUUwgSW5qZWN0aW9uJyxcbiAgICBleHBsb2l0QXZhaWxhYmxlOiBmYWxzZSxcbiAgICBwYXRjaEF2YWlsYWJsZTogdHJ1ZSxcbiAgICB0YWdzOiBbJ3dvcmRwcmVzcycsICdzcWwtaW5qZWN0aW9uJywgJ2NtcyddLFxuICAgIHNvdXJjZTogJ05WRCdcbiAgfSxcbiAge1xuICAgIGlkOiAnMycsXG4gICAgY3ZlSWQ6ICdDVkUtMjAyNC0wMDAzJyxcbiAgICBkZXNjcmlwdGlvbjogJ0Nyb3NzLXNpdGUgc2NyaXB0aW5nIChYU1MpIHZ1bG5lcmFiaWxpdHkgaW4gQXBhY2hlIEhUVFAgU2VydmVyIG1vZF9yZXdyaXRlIG1vZHVsZSBhbGxvd3MgcmVtb3RlIGF0dGFja2VycyB0byBpbmplY3QgbWFsaWNpb3VzIHNjcmlwdHMuJyxcbiAgICBzZXZlcml0eTogJ01FRElVTScgYXMgY29uc3QsXG4gICAgY3Zzc1Njb3JlOiA2LjEsXG4gICAgY3Zzc1ZlY3RvcjogJ0NWU1M6My4xL0FWOk4vQUM6TC9QUjpOL1VJOlIvUzpDL0M6TC9JOkwvQTpOJyxcbiAgICBwdWJsaXNoZWREYXRlOiAnMjAyNC0wMS0xM1QxNjoyMDowMFonLFxuICAgIGxhc3RNb2RpZmllZDogJzIwMjQtMDEtMTRUMDk6MTA6MDBaJyxcbiAgICByZWZlcmVuY2VzOiBbXG4gICAgICAnaHR0cHM6Ly9odHRwZC5hcGFjaGUub3JnL3NlY3VyaXR5L3Z1bG5lcmFiaWxpdGllc18yNC5odG1sJyxcbiAgICAgICdodHRwczovL252ZC5uaXN0Lmdvdi92dWxuL2RldGFpbC9DVkUtMjAyNC0wMDAzJ1xuICAgIF0sXG4gICAgYWZmZWN0ZWRQcm9kdWN0czogWydBcGFjaGUgSFRUUCBTZXJ2ZXIgMi40LjAtMi40LjU4J10sXG4gICAgdmVuZG9yOiAnQXBhY2hlJyxcbiAgICBjd2VJZDogJ0NXRS03OScsXG4gICAgY3dlRGVzY3JpcHRpb246ICdDcm9zcy1zaXRlIFNjcmlwdGluZyAoWFNTKScsXG4gICAgZXhwbG9pdEF2YWlsYWJsZTogdHJ1ZSxcbiAgICBwYXRjaEF2YWlsYWJsZTogdHJ1ZSxcbiAgICB0YWdzOiBbJ2FwYWNoZScsICd4c3MnLCAnd2ViLXNlcnZlciddLFxuICAgIHNvdXJjZTogJ05WRCdcbiAgfSxcbiAge1xuICAgIGlkOiAnNCcsXG4gICAgY3ZlSWQ6ICdDVkUtMjAyNC0wMDA0JyxcbiAgICBkZXNjcmlwdGlvbjogJ1ByaXZpbGVnZSBlc2NhbGF0aW9uIHZ1bG5lcmFiaWxpdHkgaW4gTGludXgga2VybmVsIGFsbG93cyBsb2NhbCB1c2VycyB0byBnYWluIHJvb3QgcHJpdmlsZWdlcyB0aHJvdWdoIHJhY2UgY29uZGl0aW9uIGluIG1lbW9yeSBtYW5hZ2VtZW50LicsXG4gICAgc2V2ZXJpdHk6ICdISUdIJyBhcyBjb25zdCxcbiAgICBjdnNzU2NvcmU6IDcuOCxcbiAgICBjdnNzVmVjdG9yOiAnQ1ZTUzozLjEvQVY6TC9BQzpML1BSOkwvVUk6Ti9TOlUvQzpIL0k6SC9BOkgnLFxuICAgIHB1Ymxpc2hlZERhdGU6ICcyMDI0LTAxLTEyVDExOjMwOjAwWicsXG4gICAgbGFzdE1vZGlmaWVkOiAnMjAyNC0wMS0xM1QxNToyMDowMFonLFxuICAgIHJlZmVyZW5jZXM6IFtcbiAgICAgICdodHRwczovL2tlcm5lbC5vcmcvcHViL2xpbnV4L2tlcm5lbC92Ni54L0NoYW5nZUxvZy02LjcuMScsXG4gICAgICAnaHR0cHM6Ly9udmQubmlzdC5nb3YvdnVsbi9kZXRhaWwvQ1ZFLTIwMjQtMDAwNCdcbiAgICBdLFxuICAgIGFmZmVjdGVkUHJvZHVjdHM6IFsnTGludXggS2VybmVsIDUuMTUtNi42J10sXG4gICAgdmVuZG9yOiAnTGludXgnLFxuICAgIGN3ZUlkOiAnQ1dFLTM2MicsXG4gICAgY3dlRGVzY3JpcHRpb246ICdSYWNlIENvbmRpdGlvbicsXG4gICAgZXhwbG9pdEF2YWlsYWJsZTogZmFsc2UsXG4gICAgcGF0Y2hBdmFpbGFibGU6IHRydWUsXG4gICAgdGFnczogWydsaW51eCcsICdrZXJuZWwnLCAncHJpdmlsZWdlLWVzY2FsYXRpb24nXSxcbiAgICBzb3VyY2U6ICdOVkQnXG4gIH0sXG4gIHtcbiAgICBpZDogJzUnLFxuICAgIGN2ZUlkOiAnQ1ZFLTIwMjQtMDAwNScsXG4gICAgZGVzY3JpcHRpb246ICdSZW1vdGUgY29kZSBleGVjdXRpb24gdnVsbmVyYWJpbGl0eSBpbiBNaWNyb3NvZnQgRXhjaGFuZ2UgU2VydmVyIGFsbG93cyB1bmF1dGhlbnRpY2F0ZWQgYXR0YWNrZXJzIHRvIGV4ZWN1dGUgYXJiaXRyYXJ5IGNvZGUuJyxcbiAgICBzZXZlcml0eTogJ0NSSVRJQ0FMJyBhcyBjb25zdCxcbiAgICBjdnNzU2NvcmU6IDkuOCxcbiAgICBjdnNzVmVjdG9yOiAnQ1ZTUzozLjEvQVY6Ti9BQzpML1BSOk4vVUk6Ti9TOlUvQzpIL0k6SC9BOkgnLFxuICAgIHB1Ymxpc2hlZERhdGU6ICcyMDI0LTAxLTExVDE0OjQ1OjAwWicsXG4gICAgbGFzdE1vZGlmaWVkOiAnMjAyNC0wMS0xMlQxMDoxNTowMFonLFxuICAgIHJlZmVyZW5jZXM6IFtcbiAgICAgICdodHRwczovL21zcmMubWljcm9zb2Z0LmNvbS91cGRhdGUtZ3VpZGUvdnVsbmVyYWJpbGl0eS9DVkUtMjAyNC0wMDA1JyxcbiAgICAgICdodHRwczovL252ZC5uaXN0Lmdvdi92dWxuL2RldGFpbC9DVkUtMjAyNC0wMDA1J1xuICAgIF0sXG4gICAgYWZmZWN0ZWRQcm9kdWN0czogWydNaWNyb3NvZnQgRXhjaGFuZ2UgU2VydmVyIDIwMTYnLCAnTWljcm9zb2Z0IEV4Y2hhbmdlIFNlcnZlciAyMDE5J10sXG4gICAgdmVuZG9yOiAnTWljcm9zb2Z0JyxcbiAgICBjd2VJZDogJ0NXRS05NCcsXG4gICAgY3dlRGVzY3JpcHRpb246ICdDb2RlIEluamVjdGlvbicsXG4gICAgZXhwbG9pdEF2YWlsYWJsZTogdHJ1ZSxcbiAgICBwYXRjaEF2YWlsYWJsZTogdHJ1ZSxcbiAgICB0YWdzOiBbJ21pY3Jvc29mdCcsICdleGNoYW5nZScsICdyY2UnXSxcbiAgICBzb3VyY2U6ICdOVkQnXG4gIH1cbl1cblxuZXhwb3J0IGludGVyZmFjZSBDVkVTZWFyY2hQYXJhbXMge1xuICBxdWVyeT86IHN0cmluZ1xuICBzZXZlcml0eT86ICdMT1cnIHwgJ01FRElVTScgfCAnSElHSCcgfCAnQ1JJVElDQUwnXG4gIHByb2R1Y3Q/OiBzdHJpbmdcbiAgdmVuZG9yPzogc3RyaW5nXG4gIHllYXI/OiBudW1iZXJcbiAgY3Zzc01pbj86IG51bWJlclxuICBjdnNzTWF4PzogbnVtYmVyXG4gIGhhc0V4cGxvaXQ/OiBib29sZWFuXG4gIGhhc1BhdGNoPzogYm9vbGVhblxuICBwYWdlOiBudW1iZXJcbiAgbGltaXQ6IG51bWJlclxufVxuXG5leHBvcnQgaW50ZXJmYWNlIENWRVNlYXJjaFJlc3VsdCB7XG4gIGRhdGE6IGFueVtdXG4gIHRvdGFsOiBudW1iZXJcbn1cblxuZXhwb3J0IGNsYXNzIENWRVNlcnZpY2Uge1xuICBhc3luYyBzZWFyY2gocGFyYW1zOiBDVkVTZWFyY2hQYXJhbXMpOiBQcm9taXNlPENWRVNlYXJjaFJlc3VsdD4ge1xuICAgIGxldCBmaWx0ZXJlZENWRXMgPSBbLi4uY3ZlRGF0YWJhc2VdXG5cbiAgICAvLyBBcHBseSBmaWx0ZXJzXG4gICAgaWYgKHBhcmFtcy5xdWVyeSkge1xuICAgICAgY29uc3QgcXVlcnkgPSBwYXJhbXMucXVlcnkudG9Mb3dlckNhc2UoKVxuICAgICAgZmlsdGVyZWRDVkVzID0gZmlsdGVyZWRDVkVzLmZpbHRlcihjdmUgPT5cbiAgICAgICAgY3ZlLmN2ZUlkLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMocXVlcnkpIHx8XG4gICAgICAgIGN2ZS5kZXNjcmlwdGlvbi50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKHF1ZXJ5KSB8fFxuICAgICAgICBjdmUuYWZmZWN0ZWRQcm9kdWN0cy5zb21lKHByb2R1Y3QgPT5cbiAgICAgICAgICBwcm9kdWN0LnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMocXVlcnkpXG4gICAgICAgICkgfHxcbiAgICAgICAgY3ZlLnRhZ3Muc29tZSh0YWcgPT5cbiAgICAgICAgICB0YWcudG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhxdWVyeSlcbiAgICAgICAgKVxuICAgICAgKVxuICAgIH1cblxuICAgIGlmIChwYXJhbXMuc2V2ZXJpdHkpIHtcbiAgICAgIGZpbHRlcmVkQ1ZFcyA9IGZpbHRlcmVkQ1ZFcy5maWx0ZXIoY3ZlID0+IGN2ZS5zZXZlcml0eSA9PT0gcGFyYW1zLnNldmVyaXR5KVxuICAgIH1cblxuICAgIGlmIChwYXJhbXMudmVuZG9yKSB7XG4gICAgICBmaWx0ZXJlZENWRXMgPSBmaWx0ZXJlZENWRXMuZmlsdGVyKGN2ZSA9PlxuICAgICAgICBjdmUudmVuZG9yLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMocGFyYW1zLnZlbmRvciEudG9Mb3dlckNhc2UoKSlcbiAgICAgIClcbiAgICB9XG5cbiAgICBpZiAocGFyYW1zLnByb2R1Y3QpIHtcbiAgICAgIGZpbHRlcmVkQ1ZFcyA9IGZpbHRlcmVkQ1ZFcy5maWx0ZXIoY3ZlID0+XG4gICAgICAgIGN2ZS5hZmZlY3RlZFByb2R1Y3RzLnNvbWUocHJvZHVjdCA9PlxuICAgICAgICAgIHByb2R1Y3QudG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhwYXJhbXMucHJvZHVjdCEudG9Mb3dlckNhc2UoKSlcbiAgICAgICAgKVxuICAgICAgKVxuICAgIH1cblxuICAgIGlmIChwYXJhbXMueWVhcikge1xuICAgICAgZmlsdGVyZWRDVkVzID0gZmlsdGVyZWRDVkVzLmZpbHRlcihjdmUgPT5cbiAgICAgICAgbmV3IERhdGUoY3ZlLnB1Ymxpc2hlZERhdGUpLmdldEZ1bGxZZWFyKCkgPT09IHBhcmFtcy55ZWFyXG4gICAgICApXG4gICAgfVxuXG4gICAgaWYgKHBhcmFtcy5jdnNzTWluICE9PSB1bmRlZmluZWQpIHtcbiAgICAgIGZpbHRlcmVkQ1ZFcyA9IGZpbHRlcmVkQ1ZFcy5maWx0ZXIoY3ZlID0+IGN2ZS5jdnNzU2NvcmUgPj0gcGFyYW1zLmN2c3NNaW4hKVxuICAgIH1cblxuICAgIGlmIChwYXJhbXMuY3Zzc01heCAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICBmaWx0ZXJlZENWRXMgPSBmaWx0ZXJlZENWRXMuZmlsdGVyKGN2ZSA9PiBjdmUuY3Zzc1Njb3JlIDw9IHBhcmFtcy5jdnNzTWF4ISlcbiAgICB9XG5cbiAgICBpZiAocGFyYW1zLmhhc0V4cGxvaXQgIT09IHVuZGVmaW5lZCkge1xuICAgICAgZmlsdGVyZWRDVkVzID0gZmlsdGVyZWRDVkVzLmZpbHRlcihjdmUgPT4gY3ZlLmV4cGxvaXRBdmFpbGFibGUgPT09IHBhcmFtcy5oYXNFeHBsb2l0KVxuICAgIH1cblxuICAgIGlmIChwYXJhbXMuaGFzUGF0Y2ggIT09IHVuZGVmaW5lZCkge1xuICAgICAgZmlsdGVyZWRDVkVzID0gZmlsdGVyZWRDVkVzLmZpbHRlcihjdmUgPT4gY3ZlLnBhdGNoQXZhaWxhYmxlID09PSBwYXJhbXMuaGFzUGF0Y2gpXG4gICAgfVxuXG4gICAgLy8gU29ydCBieSBwdWJsaXNoZWQgZGF0ZSAobmV3ZXN0IGZpcnN0KVxuICAgIGZpbHRlcmVkQ1ZFcy5zb3J0KChhLCBiKSA9PlxuICAgICAgbmV3IERhdGUoYi5wdWJsaXNoZWREYXRlKS5nZXRUaW1lKCkgLSBuZXcgRGF0ZShhLnB1Ymxpc2hlZERhdGUpLmdldFRpbWUoKVxuICAgIClcblxuICAgIC8vIFBhZ2luYXRpb25cbiAgICBjb25zdCBwYWdlID0gcGFyYW1zLnBhZ2UgfHwgMVxuICAgIGNvbnN0IGxpbWl0ID0gcGFyYW1zLmxpbWl0IHx8IDIwXG4gICAgY29uc3Qgb2Zmc2V0ID0gKHBhZ2UgLSAxKSAqIGxpbWl0XG4gICAgY29uc3QgcGFnaW5hdGVkQ1ZFcyA9IGZpbHRlcmVkQ1ZFcy5zbGljZShvZmZzZXQsIG9mZnNldCArIGxpbWl0KVxuXG4gICAgcmV0dXJuIHtcbiAgICAgIGRhdGE6IHBhZ2luYXRlZENWRXMsXG4gICAgICB0b3RhbDogZmlsdGVyZWRDVkVzLmxlbmd0aFxuICAgIH1cbiAgfVxuXG4gIGFzeW5jIGdldEJ5SWQoY3ZlSWQ6IHN0cmluZykge1xuICAgIHJldHVybiBjdmVEYXRhYmFzZS5maW5kKGN2ZSA9PiBjdmUuY3ZlSWQgPT09IGN2ZUlkKVxuICB9XG5cbiAgYXN5bmMgZ2V0U3RhdHMoKSB7XG4gICAgcmV0dXJuIHtcbiAgICAgIHRvdGFsOiBjdmVEYXRhYmFzZS5sZW5ndGgsXG4gICAgICBjcml0aWNhbDogY3ZlRGF0YWJhc2UuZmlsdGVyKGN2ZSA9PiBjdmUuc2V2ZXJpdHkgPT09ICdDUklUSUNBTCcpLmxlbmd0aCxcbiAgICAgIGhpZ2g6IGN2ZURhdGFiYXNlLmZpbHRlcihjdmUgPT4gY3ZlLnNldmVyaXR5ID09PSAnSElHSCcpLmxlbmd0aCxcbiAgICAgIG1lZGl1bTogY3ZlRGF0YWJhc2UuZmlsdGVyKGN2ZSA9PiBjdmUuc2V2ZXJpdHkgPT09ICdNRURJVU0nKS5sZW5ndGgsXG4gICAgICBsb3c6IGN2ZURhdGFiYXNlLmZpbHRlcihjdmUgPT4gY3ZlLnNldmVyaXR5ID09PSAnTE9XJykubGVuZ3RoLFxuICAgICAgd2l0aEV4cGxvaXRzOiBjdmVEYXRhYmFzZS5maWx0ZXIoY3ZlID0+IGN2ZS5leHBsb2l0QXZhaWxhYmxlKS5sZW5ndGgsXG4gICAgICB3aXRoUGF0Y2hlczogY3ZlRGF0YWJhc2UuZmlsdGVyKGN2ZSA9PiBjdmUucGF0Y2hBdmFpbGFibGUpLmxlbmd0aCxcbiAgICAgIHJlY2VudGx5UHVibGlzaGVkOiBjdmVEYXRhYmFzZS5maWx0ZXIoY3ZlID0+IHtcbiAgICAgICAgY29uc3QgcHVibGlzaGVkRGF0ZSA9IG5ldyBEYXRlKGN2ZS5wdWJsaXNoZWREYXRlKVxuICAgICAgICBjb25zdCBzZXZlbkRheXNBZ28gPSBuZXcgRGF0ZSgpXG4gICAgICAgIHNldmVuRGF5c0Fnby5zZXREYXRlKHNldmVuRGF5c0Fnby5nZXREYXRlKCkgLSA3KVxuICAgICAgICByZXR1cm4gcHVibGlzaGVkRGF0ZSA+IHNldmVuRGF5c0Fnb1xuICAgICAgfSkubGVuZ3RoXG4gICAgfVxuICB9XG59XG4iXSwibmFtZXMiOlsiY3ZlRGF0YWJhc2UiLCJpZCIsImN2ZUlkIiwiZGVzY3JpcHRpb24iLCJzZXZlcml0eSIsImN2c3NTY29yZSIsImN2c3NWZWN0b3IiLCJwdWJsaXNoZWREYXRlIiwibGFzdE1vZGlmaWVkIiwicmVmZXJlbmNlcyIsImFmZmVjdGVkUHJvZHVjdHMiLCJ2ZW5kb3IiLCJjd2VJZCIsImN3ZURlc2NyaXB0aW9uIiwiZXhwbG9pdEF2YWlsYWJsZSIsInBhdGNoQXZhaWxhYmxlIiwidGFncyIsInNvdXJjZSIsIkNWRVNlcnZpY2UiLCJzZWFyY2giLCJwYXJhbXMiLCJmaWx0ZXJlZENWRXMiLCJxdWVyeSIsInRvTG93ZXJDYXNlIiwiZmlsdGVyIiwiY3ZlIiwiaW5jbHVkZXMiLCJzb21lIiwicHJvZHVjdCIsInRhZyIsInllYXIiLCJEYXRlIiwiZ2V0RnVsbFllYXIiLCJjdnNzTWluIiwidW5kZWZpbmVkIiwiY3Zzc01heCIsImhhc0V4cGxvaXQiLCJoYXNQYXRjaCIsInNvcnQiLCJhIiwiYiIsImdldFRpbWUiLCJwYWdlIiwibGltaXQiLCJvZmZzZXQiLCJwYWdpbmF0ZWRDVkVzIiwic2xpY2UiLCJkYXRhIiwidG90YWwiLCJsZW5ndGgiLCJnZXRCeUlkIiwiZmluZCIsImdldFN0YXRzIiwiY3JpdGljYWwiLCJoaWdoIiwibWVkaXVtIiwibG93Iiwid2l0aEV4cGxvaXRzIiwid2l0aFBhdGNoZXMiLCJyZWNlbnRseVB1Ymxpc2hlZCIsInNldmVuRGF5c0FnbyIsInNldERhdGUiLCJnZXREYXRlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./lib/services/cve.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry","vendor-chunks/semver","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/jws","vendor-chunks/lodash.once","vendor-chunks/jwa","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/ms","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcve%2Fstats%2Froute&page=%2Fapi%2Fcve%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcve%2Fstats%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();